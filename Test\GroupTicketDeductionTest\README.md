# Group Ticket Deduction Test Program

## Overview

This test program simulates the deduction of group ticket items using Octopus CSC (Contactless Smart Card) functionality. It provides a comprehensive testing framework for validating group ticket processing logic without requiring actual Octopus hardware.

## Purpose

The program demonstrates and tests the following key functionality:
- Group ticket item creation and management
- Octopus CSC deduction simulation
- Error handling for insufficient funds scenarios
- Negative value limit usage
- Transaction retry logic
- Multiple ticket types (ordinary and deluxe)

## Architecture

### Key Components

1. **GroupTicketDeductionSimulator**: Main simulation class that manages ticket items and processes deductions
2. **MockCSCData**: Structure that simulates Octopus CSC state including purse value and limits
3. **CPosTicketItem**: Existing OCP class for representing individual ticket items
4. **MockOcpDeduct**: Mock function that simulates the actual OcpDeduct API call

### Test Scenarios

The program includes several predefined test scenarios:

1. **Sufficient Funds Scenario**: Tests normal group ticket processing with adequate purse balance
2. **Insufficient Funds Scenario**: Tests error handling when purse balance is too low
3. **Negative Limit Usage Scenario**: Tests transactions that use the negative value limit

## Key Features

### Realistic Simulation
- Uses actual OCP data structures (CPosTicketItem, CCurrency)
- Follows the same logic flow as the real DeductGroupTicketsByItem function
- Implements proper error codes and retry mechanisms

### Comprehensive Testing
- Multiple ticket types (ordinary/deluxe)
- Various price points and quantities
- Edge cases with negative value limits
- Error condition handling

### Detailed Logging
- Step-by-step transaction logging
- Purse value tracking
- Error reporting and diagnostics

## Usage

### Building the Program

1. Ensure you have the OCP development environment set up
2. Open the project in Visual Studio or compatible IDE
3. Build the project (both Debug and Release configurations supported)

### Running Tests

Execute the program to run all predefined test scenarios:

```
GroupTicketDeductionTest.exe
```

The program will:
1. Initialize a mock CSC with default values
2. Run through all test scenarios
3. Display detailed results for each test
4. Show final summary

### Sample Output

```
Group Ticket Deduction Test Program
===================================
Mock CSC initialized:
  Purse value: $50.00
  Negative limit: $35.00
  Physical ID: 12345678

--- Test Scenario 1: Normal Group Ticket (Sufficient Funds) ---
Added ticket item: ID=1, Deluxe=No, Qty=2, Price=$1.50, Subtotal=$3.00
Added ticket item: ID=2, Deluxe=Yes, Qty=1, Price=$2.50, Subtotal=$2.50

=== Current Ticket Items ===
Item 1: ID=1, Deluxe=No, Qty=2, Price=$1.50, Subtotal=$3.00
Item 2: ID=2, Deluxe=Yes, Qty=1, Price=$2.50, Subtotal=$2.50
Total fare: $5.50

=== Starting Group Ticket Deduction Simulation ===
Initial purse value: $50.00
Negative value limit: $35.00
Total items to process: 2

--- Processing item 1 of 2 ---
Item fare: $3.00
Patron class: 1
Is deluxe: No
MockOcpDeduct: Attempting to deduct $3.00
MockOcpDeduct: Success! New purse value: $47.00
Deduction successful for item 1

--- Processing item 2 of 2 ---
Item fare: $2.50
Patron class: 2
Is deluxe: Yes
MockOcpDeduct: Attempting to deduct $2.50
MockOcpDeduct: Success! New purse value: $44.50
Deduction successful for item 2

=== Group Ticket Deduction Complete ===
Final purse value: $44.50
Result: SUCCESS
Scenario 1 result: PASS
```

## Configuration

### Mock CSC Settings

Default CSC configuration can be modified in the `InitializeMockCSC()` function:

```cpp
g_mockCSC.purseValue = CCurrency(500, 0);        // $50.00
g_mockCSC.negativeValueLimit = CCurrency(350, 0); // $35.00
g_mockCSC.physicalId = 12345678;
```

### Test Data

Test scenarios can be customized by modifying the ticket items in `RunTestScenarios()`:

```cpp
// Add custom ticket items
simulator.AddTicketItem(itemId, isDeluxe, quantity, price);
```

## Integration with Existing Code

This test program references the following existing OCP components:

- **CPosTicketItem**: From `OCP/PosItem.h` and `OCP/PosItem.cpp`
- **CCurrency**: From `INC/Currency.h` and `utils/Currency.cpp`
- **Error codes**: From `INC/OCPError.h`
- **OCP types**: From `INC/Ocptypes.h`

The mock functions simulate the behavior of:
- `OcpDeduct()`: From `csc/Deduct.cpp`
- `DeductGroupTicketsByItem()`: From `OCP/PHyfPTrp.cpp`

## Extending the Tests

### Adding New Scenarios

To add new test scenarios:

1. Create ticket items using `AddTicketItem()`
2. Set appropriate CSC state
3. Call `DeductGroupTicketsByItem()`
4. Validate results

### Custom Mock Behavior

The `MockOcpDeduct()` function can be extended to simulate:
- Network timeouts
- Hardware failures
- Autopay scenarios
- Different error conditions

## Dependencies

- MFC libraries
- OCP utility libraries (Currency, PosItem)
- Standard C++ libraries

## Notes

- This is a simulation program and does not interact with actual Octopus hardware
- All deductions are performed on mock data structures
- The program follows the same logic patterns as the production code
- Error codes and behavior match the real OCP system

## Troubleshooting

### Build Issues
- Ensure all include paths are correctly set
- Verify that OCP libraries are built and available
- Check that MFC is properly configured

### Runtime Issues
- Verify that all required DLLs are available
- Check console output for detailed error messages
- Ensure sufficient system resources

## Future Enhancements

Potential improvements to the test program:
- XML/JSON configuration files for test scenarios
- Performance benchmarking
- Integration with automated testing frameworks
- Support for more complex group ticket scenarios
- Database logging of test results
