# Makefile for Group Ticket Deduction Test Program
# This makefile provides an alternative build method for the test program

# Compiler and flags
CXX = cl.exe
CXXFLAGS = /nologo /MTd /W3 /Gm /GX /ZI /Od /D "WIN32" /D "_DEBUG" /D "_CONSOLE" /D "_MBCS" /D "_AFXDLL"

# Include directories
INCLUDES = /I "..\..\INC" /I "..\..\OCP" /I "..\..\utils" /I "..\..\eod" /I "..\..\csc"

# Library directories
LIBDIRS = /libpath:"..\..\utils\Debug" /libpath:"..\..\eod\Debug" /libpath:"..\..\csc\Debug" /libpath:"..\..\OCP\Debug"

# Libraries
LIBS = kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib

# Source files
SOURCES = GroupTicketDeductionTest.cpp stdafx.cpp

# Object files
OBJECTS = $(SOURCES:.cpp=.obj)

# Target executable
TARGET = GroupTicketDeductionTest.exe

# Default target
all: $(TARGET)

# Build the executable
$(TARGET): $(OBJECTS)
	link.exe /nologo /subsystem:console /debug /machine:I386 /pdbtype:sept $(LIBDIRS) /out:$(TARGET) $(OBJECTS) $(LIBS)

# Compile source files
.cpp.obj:
	$(CXX) $(CXXFLAGS) $(INCLUDES) /c $<

# Clean build artifacts
clean:
	del /Q *.obj *.pdb *.ilk *.exe 2>nul

# Rebuild everything
rebuild: clean all

# Run the test program
test: $(TARGET)
	$(TARGET)

# Help target
help:
	@echo Available targets:
	@echo   all      - Build the test program (default)
	@echo   clean    - Remove build artifacts
	@echo   rebuild  - Clean and build
	@echo   test     - Build and run the test program
	@echo   help     - Show this help message

.PHONY: all clean rebuild test help
