//===========================================================================
//
// Copyright (c) ERG Electronics Ltd. 1997
//
// Module name   : coms_lpd.cpp
// Module type   : OCP Library source file
// Compiler(s)   : VC
// Environment(s): WIN32
//
// Description:
//
//  WIN32 LDP communications module.
//
// Contents:
//
//  Provides user functions
//
//  LdpComsInitialise()
//  LdpComsTask()
//  LdpHfp()
//
//  CCE required callbacks supported;
//
//  AppStatus()
//  UD_GetUD_Status()
//  UD_GetUD()
//  AR_GetAR_Status()
//  AR_GetAR()
//  OpaqueDataReceived()
//  UD_NewUDRec()
//  EODFileopNotify()
//  DoDiagnostics()
//  ComsStateChanged()
//  ComsTimeChanged()
//  AuthvarNotify()
//  AuditvarNotify()
//
//--------------------------------------------------------------------
// Version   Who      Date       Description
//--------------------------------------------------------------------
//  0.0     APY      07/05/97   Created
//  1.0     JW       09/05/97   Added OCP functionality
//  1.1     JW       29/05/97   Changed "EODFileopNotify" to use new
//                              EOD functions.
//  1.2     JW       04/06/97   Added Changes to use UDMAN interface
//                              for UD Logging in FIFO.
//  1.3     JohnD    18/09/97   Added post quota events.
//
// $Log$
// Revision 1.3  2012/08/31 10:26:11  wto
// additional hfp debug message, by default register comm threads
//
// Revision 1.2  2012/06/25 07:18:45  wto
// Conversion into tab-based UI for POST and OCP. Many features not working yet.
//
// Revision 1.1  2012/05/16 07:26:06  wto
// First commit after porting to VC2010. Existing VC6 project files are not yet deleted
//
// Revision *******  2007/04/23 02:06:15  pchoi
// PTWebCall#6804 - ignore CSC short key so as to accomodate DB2020 DSM as well
//
// Revision *******  2006/02/21 07:42:55  pchoi
// Roll back to remove direct HFP
//
// Revision 1.9  2005/09/30 08:41:34  pchoi
// PTWebCall#5809 - Add option to disable Hfp
//
// Revision 1.8  2005/04/01 06:45:23  pchoi
// PTWebCall#4669 - Fix send HFP (boardcasting/direct) (read/not read back Ldp Ip address)
//
// Revision 1.7  2005/01/26 09:36:59  pchoi
// PTWebCall#4551 - add to use direct HFP rather than broadcasting HFP
//
// Revision 1.6  2003/04/17 06:32:07  aluk
// no message
//
// Revision 1.5  2003/03/05 07:02:50  johnd
// fixed ComsLdp function to always HFP as normal, and only register the
// IP host address in the ini file when the HFP fails.
//
// Revision 1.4  2002/12/23 06:05:30  johnd
// ocp ******* import
//
// Revision 1.1  1999/08/19 09:19:20  johnd
// new source code
//
// Revision 1.40  1999/01/15 04:26:25  RAJIVE
// When a DEVSTATUS is issued from the LDP, the OCP/POST now
// returns the version number of the running application and if a new
// application file has been downloaded then it returns the version
// number of the downloaded application instead.
// Revision 1.39  1998/12/15 06:03:18  RAJIVE
// sys_stat_versn_program variable  was set to the
// IDS_OCP_VERSION string which is the version number of the
// OCP/POST release.
// Revision 1.38  1998/10/13 03:40:43  RAJIVE
// Enabled Application File Download from LDP.
//   stat->sys_stat_versn_program      = VER_FILE_BAD;
// Revision 1.37  1998/09/09 07:08:45  RJONES
// Replaced references to global Hyf Trip with local references. Made
// key table update check for presence of DSM before continuing.
//===========================================================================

#include "stdafx.h"
#include <stdio.h>
#include <stdlib.h>
#include <process.h>
#include <WinSock2.h>
#include <sys\timeb.h> 
#include "hfp.h"
#include "comsapi.h"
#include "comsapix.h"
#include "udmf.h"
#include "coms_ldp.h"
#include "status.h"
#include "eodvo.h"
#include "DateTime.h"
#include "currency.h"
#include "audit.h"
#include "eod.h"
#include "OcpEData.h"
#include "posthyf.h"
#include "eodfmt.h"
#include "NonVol.h"
#include "cDsm.h"

#if !defined(PRODUCTSERIALNUMBER_TAG)
#define PRODUCTSERIALNUMBER_TAG (BYTE)134       // Product serial number
#endif

#define OCP_TYPE                (MT_NWFF_OCP)	//MT_OCP		// williamto 29Jul2013: changeover to new OCP
#define POST_TYPE               (MT_NWFF_POS)	//MT_CSC_POST	// williamto 29Jul2013: changeover to new POST
#define HFPTIMEOUT              (HFP_DELAY*4)   // HFP timeout
#define OCPCOMSSTACK            (16*1024)       // Stack size

#define KEYMAN_FIRST_BLOCK      0x4000
#define KEYMAN_FINAL_BLOCK      0x8000
#define KEYMAN_BLOCK_SIZE       (~ ( KEYMAN_FINAL_BLOCK | KEYMAN_FIRST_BLOCK ) )

static int       hostState = 0;
static int       hostHandle = -1;
static unsigned  ThreadId;
static HANDLE    hThread;
static u_long    esn = 0;
static rpc_u32_t atime;
static rpc_u32_t ComAuditSnapShotTimeSec = 0;
static rpc_u32_t ForceSnapShotTimeSec;
static rpc_u32_t OcpLoWuAvailable;
static rpc_u32_t OcpLoWuQuotaEnable;
static rpc_u32_t PostStatus           = 0;
static rpc_u32_t PostHyfTripStatus    = 1;
static rpc_u32_t PostHyfRouteId       = 1;
static rpc_u32_t PostHyfSailingTime   = 0;
static rpc_u32_t PostHyfVesselId      = 1;
static rpc_u32_t PostHyfQuotaEnabled  = 1;
static rpc_u32_t PostHyfQuotaPrimary  = 0;
static rpc_u32_t PostHyfQuotaReserved = 0;

DWORD       SendEventToLDP(EventID_t event);
time_t      GetEventDateTime(void);
static int  LdpHfp(void);
static void ComsStateChanged(int hHost, int newState);

static int  AuthvarNotify(int hHost, commsvar_t *vrec);
static int  AuditvarNotify(int hHost, commsvar_t *vrec);
static int  LoWuVarAvailableNotify(int hHost, commsvar_t *vrec);
static int  LoWuVarEnableNotify(int hHost, commsvar_t *vrec);
static int  PostStatusNotify(int hHost, commsvar_t *vrec);
static int  PostHyfTripStatusNotify(int hHost, commsvar_t *vrec);
static int  PostHyfRouteIdNotify(int hHost, commsvar_t *vrec);
static int  PostHyfSailingTimeNotify(int hHost, commsvar_t *vrec);
static int  PostHyfVesselIdNotify(int hHost, commsvar_t *vrec);
static int  PostHyfQuotaEnabledNotify(int hHost, commsvar_t *vrec);
static int  PostHyfQuotaPrimaryNotify(int hHost, commsvar_t *vrec);
static int  PostHyfQuotaReservedNotify(int hHost, commsvar_t *vrec);

//
//  Name                              Value                  Notify                       Trap  Value Attribs
//  ----------------------------------------------------------------------------------------------------
//
static  commsvar_t  cvars[] =
{
    {VO_AUTHENTICATE_TIMEOUT,         &atime,                AuthvarNotify,               NULL, 0,    VAROPA_RDWR},
    {VO_AUDIT_REGISTER_GENERATE_TIME, &ForceSnapShotTimeSec, AuditvarNotify,              NULL, 0,    VAROPA_RDWR},
    {VO_MPR_LOWU_AVAILABLE,           &OcpLoWuAvailable,     LoWuVarAvailableNotify,      NULL, 0,    VAROPA_RDWR},
    {VO_GATE_LW_QUOTA_ACTIVE,         &OcpLoWuQuotaEnable,   LoWuVarEnableNotify,         NULL, 0,    VAROPA_RDWR},
    {VO_POST_STATUS,                  &PostStatus,           &PostStatusNotify,           NULL, 0,    VAROPA_RDWR},
    {VO_POST_TRIP_STATUS,             &PostHyfTripStatus,    &PostHyfTripStatusNotify,    NULL, 0,    VAROPA_RDWR},
    {VO_HYF_ROUTE_ID,                 &PostHyfRouteId,       &PostHyfRouteIdNotify,       NULL, 0,    VAROPA_RDWR},
  //{VO_POST_HYF_ROUTE_ID,            &PostHyfRouteId,       &PostHyfRouteIdNotify,       NULL, 0,    VAROPA_RDWR},
    {VO_GATE_HYF_SAILING_TIME,        &PostHyfSailingTime,   &PostHyfSailingTimeNotify,   NULL, 0,    VAROPA_RDWR},
    {VO_POST_HYF_VESSEL_ID,           &PostHyfVesselId,      &PostHyfVesselIdNotify,      NULL, 0,    VAROPA_RDWR},
    {VO_GATE_HYF_QUOTA_ENABLED,       &PostHyfQuotaEnabled,  &PostHyfQuotaEnabledNotify,  NULL, 0,    VAROPA_RDWR},
    {VO_POST_QUOTA_PRIMARY,           &PostHyfQuotaPrimary,  &PostHyfQuotaPrimaryNotify,  NULL, 0,    VAROPA_RDWR},
    {VO_POST_QUOTA_RESERVED,          &PostHyfQuotaReserved, &PostHyfQuotaReservedNotify, NULL, 0,    VAROPA_RDWR}
};

static void ResetAddValue(void)
{
#if 0		// 30Jan2013: download the add value quota file if updated to the URP Reader/Writer
	CNonVolatile
        nv;
    CCurrency
        TotalAddValue;
    if( nv.Read( &TotalAddValue, CNonVolatile::TOTAL_ADD_VALUE, CNonVolatile::READ_LOCK ) )
    {
        TotalAddValue = gEod.AddValueLimit();
        nv.Write( &TotalAddValue );
    }
#endif
}

void LdpComsRestart(void)
{
    if( hostHandle >= 0 )
    {
        ComsHostDeregister(hostHandle);
        hostHandle = -1;
    }
}

//===========================================================================
//  Function name
//
//       void LdpComsInitialise
//           (
//               void
//           )
//   Description:
//        Initialise communication engine
//   Parameter(s):
//
//        void
//   Returns:       void
//
//  Comments:      None
//===========================================================================

DWORD LdpComsInitialise(void)
{
    DWORD           Status = ERROR_SUCCESS;

	/*PortMapInit(THREAD_PRIORITY_ABOVE_NORMAL);
    ComsSvrInit(THREAD_PRIORITY_ABOVE_NORMAL);
    ComsCliInit(THREAD_PRIORITY_NORMAL);
	ComsFtpInit(THREAD_PRIORITY_NORMAL);*/

	// Kick off thread to handle LPD<->OCP comms to support CCE Functionality
	Status = LdpComsRun(THREAD_PRIORITY_NORMAL);

    return (Status);
}


//===========================================================================
//  Function name
//
//       int LdpComsRun
//           (
//               int priority
//           )
//   Description:
//        Create and run OCP communication task
//   Parameter(s):
//
//       void
//   Returns:       int -
//
//===========================================================================

DWORD LdpComsRun(int priority)
{
    int         i;
    DWORD       ReturnStatus = !ERROR_SUCCESS;
    CWinThread* LdpThread;

    // Register callbacks
    //
    //   COMSCB_APPSTATUS,
    //   COMSCB_FILEOP,
    //   COMSCB_UDSTATUS,
    //   COMSCB_UDGET,
    //   COMSCB_UDNEW,
    //   COMSCB_ARSTATUS,
    //   COMSCB_ARGET,
    //   COMSCB_OPAQUE,
    //   COMSCB_DIAG,
    //   COMSCB_STATE,
    //   COMSCB_TIME,
    //

    if( ( ComsCbRegister( COMSCB_STATE,     (ComsCb_t) ComsStateChanged ) == 0 )
      &&( ComsCbRegister( COMSCB_APPSTATUS, (ComsCb_t) AppStatus        ) == 0 )
      &&( ComsCbRegister( COMSCB_FILEOP,    (ComsCb_t) EODFileopNotify  ) == 0 )
      &&( ComsCbRegister( COMSCB_UDSTATUS,  (ComsCb_t) UD_GetUD_Status  ) == 0 )
      &&( ComsCbRegister( COMSCB_UDGET,     (ComsCb_t) UD_GetUD         ) == 0 )
      &&( ComsCbRegister( COMSCB_UDNEW,     (ComsCb_t) UD_NewUDRec      ) == 0 )
      &&( ComsCbRegister( COMSCB_ARSTATUS,  (ComsCb_t) AR_GetAR_Status  ) == 0 )
      &&( ComsCbRegister( COMSCB_ARGET,     (ComsCb_t) AR_GetAR         ) == 0 )
      &&( ComsCbRegister( COMSCB_KEYMAN,    (ComsCb_t) ComsKeyManagement) == 0 ) )
    {
        ReturnStatus = ERROR_SUCCESS;

        // ComsCbRegister( COMSCB_DIAG,     (ComsCb_t) DoDiagnostics );
        // ComsCbRegister( COMSCB_OPAQUE,   (ComsCb_t) OpaqueDataReceived );
        // ComsCbRegister( COMSCB_TIME,     (ComsCb_t) ComsTimeChanged );
    }

    if (ReturnStatus == ERROR_SUCCESS)
    {
        //
        // Register the VAROP variables
        //
        for (i = 0; i < (sizeof(cvars) / sizeof(commsvar_t)); i++)
        {
            ComsVarRegister(&cvars[i]);
        }

        // Create thread
        LdpThread = AfxBeginThread(LdpComsTask, 0, priority, OCPCOMSSTACK, 0, NULL);

        if (LdpThread == NULL)
        {
            ReturnStatus = GetLastError();
        }
        else
        {
            ::DuplicateHandle(
                GetCurrentProcess(),    // handle to process with handle to duplicate
                LdpThread->m_hThread,   // handle to duplicate
                GetCurrentProcess(),    // handle to process with handle to duplicate
                &hThread,               // pointer to duplicate handle
                0,                      // access for duplicate handle
                FALSE,                  // handle inheritance flag
                DUPLICATE_SAME_ACCESS);

            ThreadId = LdpThread->m_nThreadID;
        }
    }
    return ReturnStatus;
}

//===========================================================================
//  Function name
//
//       LdpComsClose
//           (
//
//           )
//   Description:
//
//   Parameter(s):
//
//
//   Returns:
//===========================================================================
void LdpComsClose(void)
{
    // First Deregister All the callbacks.
    ComsCbDeregister(COMSCB_STATE);
    ComsCbDeregister(COMSCB_APPSTATUS);
    ComsCbDeregister(COMSCB_FILEOP);
    ComsCbDeregister(COMSCB_UDSTATUS);
    ComsCbDeregister(COMSCB_UDGET);
    ComsCbDeregister(COMSCB_UDNEW);
    ComsCbDeregister(COMSCB_ARSTATUS);
    ComsCbDeregister(COMSCB_ARGET);
    ComsCbDeregister(COMSCB_STATE);

    // Should now shutdown the LDP RPC Server
    // ??
}

//===========================================================================
//  Function name
//
//       static unsigned __stdcall LdpComsTask
//           (
//               void *pVoid
//           )
//   Description:
//        OCP communication supervisor thread
//   Parameter(s):
//
//       void *pVoid
//   Returns:       unsigned -
//===========================================================================

UINT LdpComsTask(LPVOID pParam)
{
    AuditRegisters  Audit;
    UINT            AuditSnapShotTimerId = 0;   // Set to expired state
    UINT            hTimer = 0;
    MSG             msg;
    int             ret;

    //AuditSnapShotTimeSec = 10;

    for (;;)
    {
        ComAuditSnapShotTimeSec = Audit.AuditSnapShotTimeSec;

        if ((ComAuditSnapShotTimeSec != 0 ))
        {
            // Set timer only if it has expired
            if (AuditSnapShotTimerId == 0)
            {
                AuditSnapShotTimerId = SetTimer(NULL, 0, ComAuditSnapShotTimeSec * 1000, NULL);
            }
        }

        // Create connection
        if (hostHandle == -1)
        {
			if (LdpHfp() != 0)
            {
				PRINT_DEBUG_TRACE(_T("\r\nLdpComsTask: set LDP timer"));   
				if ((hTimer = SetTimer(NULL, 0, 15*1000, NULL)) != 0)
                {
                    hostHandle = -2;
                }
            }
        }

        // Retrieve next thread message
        ret = GetMessage(&msg, NULL, 0, 0);

        if (ret == TRUE)
        {
            CHyfTrip
                HyfTrip;

            switch(msg.message)
            {
                case WM_TIMER:
                    //
                    // HFP backoff timer
                    //
                    PRINT_DEBUG_TRACE(_T("\r\nLdpComsTask: WM_TIMER"));
					if (hTimer)
                    {
                        KillTimer(NULL, hTimer);
                        hTimer = 0;
                    }

                    if (hostHandle == -2)
                    {
                        hostHandle = -1;
                    }

                    //
                    // Audit timer (Message 'wParam' contains the timer ID)
                    //
                    if ((msg.wParam == AuditSnapShotTimerId) && (AuditSnapShotTimerId))
                    {
                        KillTimer(NULL, AuditSnapShotTimerId);

                        // Set as expired
                        AuditSnapShotTimerId = 0;
                        Audit.SnapShot();
                    }
                    break;

                case OCPWM_STATECHANGED:
					PRINT_DEBUG_TRACE(_T("\r\n%s: OCPWM_STATECHANGED"), _T(__FUNCTION__));
                    if ((hostState & CSTAT_AUTH) && ((int) msg.lParam & CSTAT_AUTH) == 0)
                    {
                        // Authentication state dropped, deregister and force HFP
						PRINT_DEBUG_TRACE(_T("\r\n%s: ComsHostDeregister being called"), _T(__FUNCTION__));
                        ComsHostDeregister(hostHandle);
                        hostHandle = -1;

                        if (gEod.IsPost())
                        {
                            // Set operation to stand-alone mode
							PRINT_DEBUG_TRACE(_T("\r\n%s: POST in Standalone mode"), _T(__FUNCTION__));
                            HyfTrip.SetOperationMode(CHyfTrip::StandAloneMode);
                        }
                    }
                    else
                    {
						PRINT_DEBUG_TRACE(_T("\r\n%s: hostState=%X, msg.lParam=%X"), _T(__FUNCTION__), hostState, msg.lParam );
						if ((hostState & CSTAT_AUTH) && ((int) msg.lParam & CSTAT_AUTH))
                        {
	                        PRINT_DEBUG_TRACE(_T("\r\n%s: Authentication OK"), _T(__FUNCTION__));
                            if (gEod.IsPost() && !HyfTrip.IsInTrip())
                            {
                                // set operation back to LDP mode
								PRINT_DEBUG_TRACE(_T("\r\n%s: POST in LDP mode"), _T(__FUNCTION__));
                                HyfTrip.SetOperationMode(CHyfTrip::LdpMode);
                            }
                        }
                    }
                    hostState = (int)msg.lParam;
                    break;

                default:
					PRINT_DEBUG_TRACE(_T("\r\nLdpComsTask: default"));
                    break;
            }
			PRINT_DEBUG_TRACE(_T("\r\nLdpComsTask: running"));
        }
        else
        {
            if (ret == FALSE)
            {
                // Termination message
				PRINT_DEBUG_TRACE(_T("\r\nLdpComsTask: termination message return FALSE"));
                return TRUE;
            }
            else
            {
                // General error
				PRINT_DEBUG_TRACE(_T("\r\nLdpComsTask: termination message return unknown"));
                return FALSE;
            }
        }
    }
}


//===========================================================================
//
//  Function Name   : LdpHfp(void)
//
//  Description     :
//
//  Parameters
//  ----------
//
//  Returns
//  -------
//
//  Comments
//  --------
//
//===========================================================================

static int LdpHfp(void)
{
	unsigned char   vendBuf[HFP_MAXFIELDS] = { 0 };
    int             vendSz = HFP_MAXFIELDS - (4 + 6);		// williamto 23Oct2013: temporary hack for FAT failure ********, reserve 10 bytes 4 bytes for ESN and 6 bytes for padding
    HFP_U32         timeout = 0;
    Hfp_t          *pHfp = 0;
    u_long          esn = 0;
    int             i = 0, hfp_ret = 0;
	U32_t ip		= gEod.LdpIp();

	PRINT_DEBUG_TRACE(_T("\r\n%s: HfpEnabled=%d, ip=%X"), _T(__FUNCTION__), gEod.IsHfpEnabled(), ip);

    if (gEod.IsHfpEnabled() == 0)
	{
		// Directly register the host by the ip address set in 
		// the ocp.ini if HFP disabled 

		if (ip)
		{
			hostHandle = ComsHostRegister(NULL, ip);
			return 0;
		}
		// else, nothing in ini file, fall through and fail
      return -1;
	}

    // Defaults
    //esn = (0x00FFFFFF) & (gEod.MachineId());
	esn = gEod.MachineId();

	PRINT_DEBUG_TRACE(_T("\r\n%s: send HFP to LDP, esn=%08X"), _T(__FUNCTION__), esn);

    timeout = (HFP_DELAY * 4);        // 4 retries (each 5 seconds)

    // Create vendor information
    esn = htonl(esn);
    hfp_ret = HfpVendAdd(PRODUCTSERIALNUMBER_TAG, (char *) &esn, 4, vendBuf, &vendSz);

    if (!hfp_ret)
    {
		PRINT_DEBUG_TRACE(_T("\r\n%s: send HFP to LDP, _countof(vendBuf)=%d"), _T(__FUNCTION__), _countof(vendBuf));
		// send HFP to LDP
		pHfp = HfpSend(timeout, vendBuf, vendSz, &i);
		if (pHfp)
		{
            PRINT_DEBUG_TRACE(_T("\r\n%s: send HFP to LDP, got reply, register"), _T(__FUNCTION__));
			// Got a reply, register it
            hostHandle = ComsHostRegister(NULL, pHfp->hfp_siaddr.s_addr);
			PRINT_DEBUG_TRACE(_T("\r\n%s: send HFP to LDP, got reply, hostHandle=%X, hostname=%s, address=%s"), _T(__FUNCTION__), 
				hostHandle, CA2CT((char*)pHfp->hfp_sname).m_psz, CA2CT(inet_ntoa(pHfp->hfp_siaddr)).m_psz);
            HfpFree(pHfp);
            return 0;
		}
		else 
		{
			U32_t ip = gEod.LdpIp();

			PRINT_DEBUG_TRACE(_T("\r\n%s: pHfp=NULL, ip=%X"), _T(__FUNCTION__), ip);

			// Directly register the host by the ip address set in 
			// the ocp.ini if no reply - note this is a workaround
			// for bad routers which cannot route the HFP reply back
			// from the LDP (citybus).  The HFP must still be sent
			// to trigger the LDP into attempting a connection.

			if (ip)
			{
				hostHandle = ComsHostRegister(NULL, ip);
				return 0;
			}
			// else, nothing in ini file, fall through and fail
		}
    }
    
    return -1;
}

//===========================================================================
//
//  Function Name   : AppStatus ()
//
//  Description     :
//
//  Parameters
//  ----------
//
//  Returns
//  -------
//
//  Comments
//  --------
//
//===========================================================================

void AppStatus(int handle, AppStatus_t *stat)
{
	FILE			*fp;
	long			headerOffset = 10;	// bytes
	CFileException	e;
	int				versionField[2];

    stat->sys_stat_generic     = 0;
    stat->sys_stat_machine_dep = 0;

    if (gEod.IsPost())
    {
        CHyfTrip
            HyfTrip;

        int Value     = (HyfTrip.IsInService()) ? STATUS_NORMAL : STATUS_OOS;
        int TripValue = (HyfTrip.IsInTrip())    ? POST_ON_TRIP  : POST_OFF_TRIP;

        // generic stuff
        stat->sys_stat_type     = POST_TYPE;
        stat->sys_stat_generic |= (Value << GEN_SERVICE_STATUS_SHIFT) & GEN_SERVICE_STATUS_MASK;
        stat->sys_stat_generic |= (EV_OUT_OF_SERVICE << GEN_EVENT_ID_SHIFT) & GEN_EVENT_ID_MASK;

        // trip stuff (POST specific)
        stat->sys_stat_machine_dep = (TripValue << POST_FARE_MODE_SHIFT) & POST_TRIP_MODE_MASK;
    }
    else
    {
        stat->sys_stat_type = OCP_TYPE;
    }

    // Rajiv - Original line - program file version
	//	stat->sys_stat_versn_program	   = VER_FILE_BAD;

	// If a new application file has not been downloaded then send the
	// version number of the application currently running, else send
	// the version number of the newly downloaded application.
	if( (fp = _tfopen( NEW_APPLICATION_FILE, _T("rb"))) == NULL )
	{
		CString	tmpString;
		tmpString.LoadString(IDS_OCP_VERSION); 

		stat->sys_stat_versn_program       = (unsigned short)_tstoi(tmpString);
		stat->sys_stat_versn_program_unact = (unsigned short) -2; // VER_FILE_NOT_NEEDED
	}
	else
	{
		// Position the file after the header information and then read the file.
		if ( !fseek( fp, headerOffset, SEEK_SET ) )
		{
			versionField[0] = fgetc(fp);
			versionField[1] = fgetc(fp);

			stat->sys_stat_versn_program       = (unsigned short)((versionField[0]*16*16)+versionField[1]);
			stat->sys_stat_versn_program_unact = (unsigned short) -2;
		}
		else
		{
	   #ifdef _DEBUG
			afxDump << "Unable to seek to end of header block " << e.m_cause << "\n";
	   #endif
		}

		// Close the files
		fclose(fp);
	}

    // EOD file version numbers
    gEod.AppStatus(stat);
}

//===========================================================================
//
//  Function Name   : UD_GetUD_Status ()
//
//  Description     :
//
//  Parameters
//  ----------
//
//  Returns
//  -------
//
//  Comments
//  --------
//
//===========================================================================
void UD_GetUD_Status (int handle, UD_Status_t *stat)
{
    unsigned short  oldestSeq;
    unsigned short  newestSeq;
    int             recordsUsed;
    int             recordsFree;

    ResetAddValue();

    COcpEventDataLdpI::GetUdLogStatus(&oldestSeq, &newestSeq, &recordsUsed, &recordsFree);

	PRINT_DEBUG_TRACE( _T("\r\nInput oldestSeq=%d, newestSeq=%d, recordsUsed=%d, recordsFree=%d"), oldestSeq, newestSeq, recordsUsed, recordsFree );

    stat->UD_OldestSeq   = oldestSeq;
    stat->UD_NewestSeq   = newestSeq;
    stat->UD_RecordsUsed = recordsUsed;
    stat->UD_RecordsFree = recordsFree;
}

//===========================================================================
//
//  Function Name   : UD_GetUD ()
//
//  Description     :
//
//  Parameters
//  ----------
//
//  Returns
//  -------
//
//  Comments
//  --------
//
//===========================================================================
int UD_GetUD(int handle, rpc_u16_t next_seq, rpc_bool_t delete_ok, struct ud *ud_buffer)
{
	ResetAddValue();

	PRINT_DEBUG_TRACE( _T("\r\nInput next_seq=%d, delete_ok=%d, sizeof(ud)=%d"), next_seq, delete_ok, sizeof(ud) );

	return COcpEventDataLdpI::GetUd(next_seq, delete_ok, ud_buffer, sizeof(ud));
}

//===========================================================================
//
//  Function Name   : AR_GetAR_Status ()
//
//  Description     :
//
//  Parameters
//  ----------
//
//  Returns
//  -------
//
//  Comments
//  --------
//
//===========================================================================
void AR_GetAR_Status(int handle, AR_Status_t *stat)
{
    unsigned short  oldestSeq;
    unsigned short  newestSeq;
    int             recordsUsed;
    int             recordsFree;

    ResetAddValue();

    COcpEventDataLdpI::GetARLogStatus(&oldestSeq, &newestSeq, &recordsUsed,&recordsFree);
    stat->AR_OldestSeq   = oldestSeq;
    stat->AR_NewestSeq   = newestSeq;
    stat->AR_RecordsUsed = recordsUsed;
    stat->AR_RecordsFree = recordsFree;
}

//===========================================================================
//
//  Function Name   : AR_GetAR()
//
//  Description     :
//
//  Parameters
//  ----------
//
//  Returns
//  -------
//
//  Comments
//  --------
//
//===========================================================================
int AR_GetAR(int handle, rpc_u16_t next_seq, rpc_bool_t delete_ok, struct ar *ar_buffer)
{
    int             status = -1;

    ResetAddValue();

    status = COcpEventDataLdpI::GetAR(next_seq, (int) delete_ok, ar_buffer, sizeof(ar));
    return status;
}

//===========================================================================
//
//  Function Name   : OpaqueDataReceived ()
//
//  Description     :
//
//  Parameters
//  ----------
//
//  Returns
//  -------
//
//  Comments
//  --------
//
//===========================================================================
void OpaqueDataReceived (int handle, struct hk_datax *drec)
{
    // not supported
    return;
}

//===========================================================================
//
//  Function Name   : UD_NewUDRec ()
//
//  Description     :
//
//  Parameters
//  ----------
//
//  Returns
//  -------
//
//  Comments
//  --------
//
//===========================================================================
void UD_NewUDRec (int handle, struct ud *udrec)
{
    // not supported
    return;
}

//===========================================================================
//
//  Function Name   : EODFileopNotify()
//
//  Description     :
//
//  Parameters
//  ----------
//
//  Returns
//  -------
//
//  Comments
//  --------
//
//===========================================================================
rpc_bool_t EODFileopNotify(
    int hHost,
    rpc_callback_when_t when,
    rpc_u8_t command,
    char *pFname,
    int flag
#if (COMSAPI_VERSION >= 3)
    , ComsFileNotifyStatus_t *pStatus )
#else
    )
#endif
{
    switch(when)
    {
    case when_pre_operation:
        switch( command )
        {
        case XFEROP_RECV_FILE:
            flag = gEod.RecvRequest(pFname);
            break;
        case XFEROP_FILE_CHANGED:
        case XFEROP_FILE_STATUS:
        case XFEROP_SEND_FILE:
           flag = gEod.SendRequest(pFname);
            break;
        default:
            flag = 0;
            break;
        }
        return flag;

    case when_post_operation:
        switch( command )
        {
        case XFEROP_RECV_FILE:
            flag = gEod.RecvComplete(pFname, flag);
			//AfxMessageBox( _T("New Application Available"), IDOK); // Rajiv 6-1-98	// williamto 02Sept2013: FAT failure 4.29.1/3, just let it be slient
            break;
        case XFEROP_SEND_FILE:
        default:
            flag = -1;
            break;
        }
        break;

    case when_dummy:
    default:
        flag = 0;
        break;
    }

#if (COMSAPI_VERSION >= 3)
    if (flag == 1)
    {
    /* Success */
        pStatus->StatusCode = COMSS_SUCCESS;
        pStatus->SubCode = 0;
    }
    else if (flag == -1)
    {
    /* Undefined */
        pStatus->StatusCode = COMSS_EUNDEF;
        flag = 0;
    }
    else
    {
    /* Application error */
        pStatus->StatusCode = COMSS_EAPP;
        if (when == when_pre_operation)
            pStatus->SubCode = EV_IN_SERVICE;               /* Not OOS */
        else {
            pStatus->SubCode = EV_FLASH_CHECKSUM_ERROR;     /* CRC wrong */
        }
    }
#endif  /*COMSAPI_VERSION*/
    return (rpc_bool_t)flag;
}

//===========================================================================
//
//  Function Name   : ComsKeyManagement ()
//
//  Description     :
//
//  Parameters
//  ----------
//
//  Returns
//  -------
//
//  Comments
//  --------
//
//===========================================================================
void ComsKeyManagement( int hHost, struct hk_key_management *pKeyMan, int *pStatusCode, int *pSubCode )
{
    cDsm
        Dsm;
    DSM_Report_output_t     
        report;
    dsm_error_t
        error;
    U8
        **ppTable;
    U16
        size,
        *pnTable;
    U32
        oldVersion,
        *pVersion;
    static U8
        *pCardLTable = NULL,
        *pRWTable = NULL,
        *pKBATable = NULL,
        *pSessionTable = NULL,
        *pIVKBATable = NULL,
        *pCardTable = NULL,
		*pNCardTable = NULL,     // dsm : new card key table for CSCNEW
		*pRWERGTable = NULL,       // dsm : new RW key table for RWERG
        *pSKIPTable = NULL;
    static U16
        nCardLTable = 0,
        nRWTable = 0,
        nKBATable = 0,
        nSessionTable = 0,
        nIVKBATable = 0,
        nCardTable = 0,
		nNCardTable =0,          // dsm : new card key table for CSCNEW
		nRWERGTable = 0,           // dsm : new RW key table for RWERG
        nSKIPTable = 0;
    size_t
        MAX_KEYMAN_DATA_SIZE = sizeof( pKeyMan->Data );

    if( !Dsm.GetDsmInUse() )
    {
        *pStatusCode = COMSS_EBADOP;
        return;
    }

	*pStatusCode = COMSS_SUCCESS;

    switch( pKeyMan->TableID )
    {
    //case KEYMAN_SONY_CARDL_TABLE :	// [webcall][#12859][ocp][post][disable key usage]
    //    pVersion = &report.CSC_Table_Version;
    //    ppTable = &pCardLTable;
    //    pnTable = &nCardLTable;
    //    break;
    case KEYMAN_SONY_RW_TABLE :
        pVersion = &report.RW_Table_Version;
        ppTable = &pRWTable;
        pnTable = &nRWTable;
        break;
    case KEYMAN_KBA_TABLE :
        pVersion = &report.StarKBA_Table_Version;
        ppTable = &pKBATable;
        pnTable = &nKBATable;
        break;
    //case KEYMAN_SONY_CARD_TABLE :		// [webcall][#12859][ocp][post][disable key usage]
    //    pVersion = &report.CSC_Short_Table_Version;
    //    ppTable = &pCardTable;
    //    pnTable = &nCardTable;
    //    break;
	case KEYMAN_CSCNEW_TABLE :    // dsm : new card key table CSCNEW
		pVersion = &report.CSCNEW_Table_Version;
		ppTable = &pNCardTable;
		pnTable = &nNCardTable;
		break;
	//case KEYMAN_RWERG_TABLE :    // dsm : new rw key table RWERG		// [webcall][#12859][ocp][post][disable key usage]
	//	pVersion = &report.RWERG_Table_Version;
	//	ppTable = &pRWERGTable;
	//	pnTable = &nRWERGTable;
	//	break;
	case KEYMAN_SESSION_TABLE :
    case KEYMAN_IVKBA_TABLE :
    case KEYMAN_SKIP_TABLE :
    default :
        *pStatusCode = COMSS_ENOSUPPORT;
        return;
        break;
    }

    if( pKeyMan->Request == KEYMAN_PUT_TABLE )
    {
        size = pKeyMan->Parameter & KEYMAN_BLOCK_SIZE;

        if( pKeyMan->Parameter & KEYMAN_FIRST_BLOCK )
        {
            // Free the memory used to build the table.
            if( *ppTable )
            {
                free( *ppTable );
            }
            *ppTable = NULL;
            *pnTable = 0;
        }

        // Build the complete Key Table, possibly from multiple blocks.
        // This assumes that blocks come consecutively.
        *ppTable = (U8*)realloc( *ppTable, *pnTable + size );
        if( !ppTable )
        {
            *pStatusCode = COMSS_EREJECTED;
            return;
        }
        memcpy( *ppTable + *pnTable, pKeyMan->Data, size );
        *pnTable += size;

        if( pKeyMan->Parameter &= KEYMAN_FINAL_BLOCK )
        {
            if( Dsm.Report( &report, &error, DSM_UNIVERSAL_TIMEOUT ) != DSM_OK )
            {
                *pStatusCode = COMSS_EREJECTED;
            }
            else
            {
                DSM_Key_Table_Extract_KBA_input_t
                    inputKBA;

                oldVersion = *pVersion & 0x7FFFFFF;     // In case it was -2 (no table).

                inputKBA.Cipher_Stream = *ppTable;
                inputKBA.Cipher_Stream_Size = *pnTable;
                inputKBA.StarKBA_Version = report.StarKBA_Table_Latest_Entry;

                if( Dsm.Key_Table_Extract_KBA( &inputKBA, &error, DSM_UNIVERSAL_TIMEOUT ) != DSM_OK )
                {
                    *pStatusCode = COMSS_EREJECTED;
                }

                // Check that the table has been loaded correctly.
                if( ( *pStatusCode == COMSS_SUCCESS )
                  &&( ( Dsm.Report( &report, &error, DSM_UNIVERSAL_TIMEOUT ) != DSM_OK )
                    ||( *pVersion <= oldVersion ) ) )
                {
                    *pStatusCode = COMSS_EREJECTED;
                }
            }

            // Free the memory used to build the table.
            free( *ppTable );
            *ppTable = NULL;
            *pnTable = 0;
        }

    }
    else if( pKeyMan->Request == KEYMAN_GET_TABLE_VERSION )
    {
        if( Dsm.Report( &report, &error, DSM_UNIVERSAL_TIMEOUT ) != DSM_OK )
        {
            *pStatusCode = COMSS_EREJECTED;
        }
        else
        {
			pKeyMan->Parameter = (U16)*pVersion;
        }
    }
    else
    {
        *pStatusCode = COMSS_EBADOP;
    }
}
//===========================================================================
//
//  Function Name   : DoDiagnostics ()
//
//  Description     :
//
//  Parameters
//  ----------
//
//  Returns
//  -------
//
//  Comments
//  --------
//
//===========================================================================
rpc_u8_t DoDiagnostics (int handle, rpc_u8_t diag_op,
                        rpc_u16_t diagparamA,
                        rpc_u16_t diagparamB,
                        rpc_u32_t diagparamC,
                        rpc_u32_t diagparamD,
                        rpc_u32_t *diag_returnA,
                        DataBlock_t *diag_returnB)
{
    // not supported
    return(0);
}


//===========================================================================
//
//  Function Name   : ComsStateChanged()
//
//  Description     :
//
//  Parameters
//  ----------
//
//  Returns
//  -------
//
//  Comments
//  --------
//
//===========================================================================
static void ComsStateChanged(int hHost, int newState)
{
    PostThreadMessage(ThreadId, OCPWM_STATECHANGED, (WPARAM) hHost, (LPARAM) newState);
}

//===========================================================================
//
//  Function Name   : ComsTimeChanged ()
//
//  Description     :
//
//  Parameters
//  ----------
//
//  Returns
//  -------
//
//  Comments
//  --------
//
//===========================================================================
void ComsTimeChanged(int handle, time_t newZone /*, EosTmTime_t *newTime */)
{
    // coms has changed the time
    // what does the application need to to ??
}

//===========================================================================
//
//  Function Name   : AuthvarNotify()
//
//  Description     :
//
//  Parameters
//  ----------
//
//  Returns
//  -------
//
//  Comments
//  --------
//
//===========================================================================
static int AuthvarNotify(int hHost, commsvar_t *vrec)
{
    ComsOptSet(hHost, COMSOPT_AUTHTIMEOUT, (int) *vrec->var_value / 1000);

    // On LDP authentication, reset the Total Add Value counter
    ResetAddValue();

    return VARNOT_COMPLETE;
}

//===========================================================================
//
//  Function Name   : AuditvarNotify()
//
//  Description     :
//
//  Parameters
//  ----------
//
//  Returns
//  -------
//
//  Comments
//  --------
//
//===========================================================================
//  Used to for "AuditSnapShotTime" variable
static int AuditvarNotify(int hHost, commsvar_t *vrec)
{
	struct timeb st_begin, st_end;
    
	AuditRegisters Audit;

    ForceSnapShotTimeSec = *(vrec->var_value);

    if (ForceSnapShotTimeSec == 0)
    {
        ftime(&st_begin);
		Audit.SnapShot();
		ftime(&st_end);

		PRINT_DEBUG_TRACE( _T( "\r\nElapsed Time for Audit.SnapShot()=%d" ), (1000*(st_end.time - st_begin.time) + (st_end.millitm - st_begin.millitm) ) ); 
    }

    return VARNOT_COMPLETE;
}

//===========================================================================
//
//  Function Name   : LoWuNotify()
//
//  Description     :
//
//  Parameters
//  ----------
//
//  Returns
//  -------
//
//  Comments
//  --------
//
//===========================================================================
//  Used to for "LoWuAvailable" variable

static int LoWuVarAvailableNotify(int hHost, commsvar_t *vrec)
{
    return VARNOT_COMPLETE;
}

static int LoWuVarEnableNotify(int hHost, commsvar_t *vrec)
{
    return VARNOT_COMPLETE;
}

LONG GetLoWuAvailable(void)
{
    return OcpLoWuAvailable;
}

LONG GetLoWuQuotaEnabled(void)
{
    return OcpLoWuQuotaEnable;
}

DWORD SendLoWuTempBitSetEventToLDP(void)
{
    return SendEventToLDP(EV_TEMPORARY_LOWU_BIT_SET);
}

DWORD SendEventToLDP(EventID_t event)
{
    DWORD           Status = !ERROR_SUCCESS;
    ComsQueueHdr_t  hdrBuf;

#if( OPAQUE_UD_SIZE == 112 )
    struct ud
#else
    // This horrible patch is due to the need to modify the size of OPAQUE_UD_SIZE in cmds.h
    // in order to get TOAS to work.
    struct {
        u_short ud_sequence;
        u_long ud_when;
        char ud_data[ 112 ];
        }
#endif
        udBuf,
        *pUD;
    struct UD_t     opaque;
    struct UD_t     *popaque;
    long            EodUnitId;
    MachineID_t     UnitId;

    pUD     = &udBuf;
    popaque = &opaque;

    // Valid Host handle if > 0, exit with error otherwise
    // hosthandle set by HFP in the background
    if ( hostHandle > 0 )
    {
        memset(&hdrBuf, 0,sizeof(hdrBuf));
        memset(&udBuf,  0,sizeof(udBuf));
        memset(&opaque, 0,sizeof(opaque));

        hdrBuf.ch_host      = hostHandle;
        hdrBuf.ch_status    = COMSS_REQUEST;
        hdrBuf.ch_command   = UD_NOTIFY;

        EodUnitId = gEod.MachineId();
        UnitId[2] = (unsigned char)   EodUnitId ;
        UnitId[1] = (unsigned char)  (EodUnitId >> 8);
        UnitId[0] = (unsigned char)  (EodUnitId >> 16);

        pUD->ud_sequence    = 0;
        pUD->ud_when        = GetEventDateTime();

        // Setup UD header
        popaque ->UDSN       = 0;
        popaque ->ReportTime = pUD->ud_when;
        for (int i = 0; i < sizeof( popaque -> DeviceID ); i++)
        {
            popaque -> DeviceID[i] = UnitId[i];
        }

        // Set Event type
        popaque ->UDData.MsgType = MSG_EVENT;
        popaque ->UDData.UDData_u.Event.Event = event;

        memcpy( &(pUD->ud_data), popaque, sizeof( pUD->ud_data ) );

        if( ComsCliPost( (ComsQueue_t *)&hdrBuf,
                          &udBuf,
                          sizeof(udBuf) ) == 0 )
        {
            Status = ERROR_SUCCESS;
        }
    }
    return Status;
}

time_t GetEventDateTime(void)
{
    CDateTime   now(time(NULL));
    time_t      current_t;

    current_t = now.GetTimet();

    return (current_t);
}

//===========================================================================
//
//  Function Name   :
//
//  Description     :
//
//  Parameters
//  ----------
//
//  Returns
//  -------
//
//  Comments
//  --------
//
//===========================================================================
//  Used for POST operations

int ComsLdpIsHostAuthenticated()
{
    return ( (hostHandle > 0) && ( hostState & CSTAT_AUTH) );	// williamto 10Dec2013: [OP][POST][Issue #126] now always check the hostState to determine whether it's really authenticated with LDP
}

int PostStatusNotify(int hHost, commsvar_t *vrec)
{
    return VARNOT_COMPLETE;
}

int PostHyfTripStatusNotify(int hHost, commsvar_t *vrec)
{
    CHyfTrip
        HyfTrip;

    if (gEod.IsPost() && HyfTrip.IsInService())
    {
        if (PostHyfTripStatus == POST_TRIP_START)
        {
            // Start a trip if not already started
            if (!HyfTrip.IsInTrip())
            {
                PRINT_DEBUG_TRACE(_T("\r\nPostHyfRouteId=%d"), PostHyfRouteId);
				HyfTrip.SetOperationMode(CHyfTrip::LdpMode);
                HyfTrip.StartTrip(PostHyfRouteId/* - 1*/, PostHyfVesselId - 1, PostHyfSailingTime);		// williamto 20Jan2014: the PostHyfRouteId is a route index and independent of any array position
            }
        }
        else
        {
            // End the trip if there is one.
            // Note that we cannot end the
            // trip if in standalone mode.

            if (HyfTrip.IsInTrip() && HyfTrip.GetOperationMode() == CHyfTrip::LdpMode)
            {
                HyfTrip.EndTrip();
            }
        }
    }
    return VARNOT_COMPLETE;
}

int PostHyfRouteIdNotify(int hHost, commsvar_t *vrec)
{
    //
    // only read this on trip start
    //
    return VARNOT_COMPLETE;
}

int PostHyfSailingTimeNotify(int hHost, commsvar_t *vrec)
{
    //
    // only read this on trip start
    //
    return VARNOT_COMPLETE;
}

int PostHyfVesselIdNotify(int hHost, commsvar_t *vrec)
{
    //
    // only read this on trip start
    //
    return VARNOT_COMPLETE;
}

int PostHyfQuotaEnabledNotify(int hHost, commsvar_t *vrec)
{
    CHyfTrip
        HyfTrip;

    if (gEod.IsPost() && HyfTrip.IsInService())
    {
        if (HyfTrip.GetOperationMode() == CHyfTrip::LdpMode)
        {
            if (PostHyfQuotaEnabled)
            {
                HyfTrip.SetQuotaMode(CHyfTrip::QuotaEnabled);
            }
            else
            {
                HyfTrip.SetQuotaMode(CHyfTrip::QuotaDisabled);
            }
        }
    }
    return VARNOT_COMPLETE;
}

int PostHyfQuotaPrimaryNotify(int hHost, commsvar_t *vrec)
{
    CHyfTrip
        HyfTrip;

    if (gEod.IsPost() && HyfTrip.IsInService())
    {
        if (HyfTrip.IsInTrip() && HyfTrip.GetOperationMode() == CHyfTrip::LdpMode)
        {
            HyfTrip.PrimaryQuotaReply(PostHyfQuotaPrimary);
        }
    }
    return VARNOT_COMPLETE;
}

int PostHyfQuotaReservedNotify(int hHost, commsvar_t *vrec)
{
    CHyfTrip
        HyfTrip;

    if (gEod.IsPost() && HyfTrip.IsInService())
    {
        if (HyfTrip.IsInTrip() && HyfTrip.GetOperationMode() == CHyfTrip::LdpMode)
        {
            HyfTrip.ReservedQuotaReply(PostHyfQuotaReserved);
        }
    }
    return VARNOT_COMPLETE;
}

int ComsLdpPostHyfSetQuotaRequest(unsigned int Primary, unsigned int Reserved)
{
    PostHyfQuotaPrimary  = Primary;
    PostHyfQuotaReserved = Reserved;
    return 0;
}

/* end of file */
