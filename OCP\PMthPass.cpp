//===========================================================================
//  Copyright (c) ERG Ltd. 1996
//
//  Module name     : PMthPass.cpp
//  Module type     : Library source file
//  Compiler(s)     : Microsoft Visual C++ v4.0
//  Environment(s)  : Win32
//
//  Note:   4-character hard tabs are used in this file.
//
//---------------------------------------------------------------------------
//
//  Description
//  -----------
//	CHyfProcessFareCalc impl file
//
//
//
//---------------------------------------------------------------------------
//
//  Who       Date      Description
//  ---     --------    -----------
//	JohnD	 6-9-97 	Hacked from the original file; it now encompasses
//						all of the HYF fare calcs, rather than just monthly
//						passes.
//	YTAY	07/05/00	HYF holiday concession scheme
//===========================================================================

#include "stdafx.h"
#include "ocp.h"
#include "ocpcsc.h"
#include "PMthPass.h"
#include "OcpMsgBx.h"
#include "OCPUtil.h"
#include "crepprn.h"
#include "cscchk.h"
#include "hk1util.h"
#include "NonVol.h"
#include "RedoAVd.h"
#include "farecalc.h"

extern OcpGeneralCscInfo UnconfirmedAV_CSCData;
CPostCsc::CPostCsc()
{
    InvalidateData();
}

BOOL CPostCsc::Poll(BOOL CheckForExisting)
{
    DWORD               PollError;
    int                 PollOk  = FALSE;
    int                 Repairs = 0;

    while (TRUE)
    {
        if (CheckForExisting)
        {
            // poll for the last card we looked for
            PollError = CscFastPollCard(CSC_REMAINING, &SerialNumber);
        }
        else
        {
            // poll for a new csc
            PollError = CscFastPollCard(CSC_REMAINING, &SerialNumber);
            if (PollError)
            {
                PollError = CscFastPollCard(CSC_FORMATTED, &SerialNumber);
            }
        }

        if (PollError == CSC_POLLTIMEOUT)
        {
            // if there is no CSC and the user wants to cancel
            if(OcpMessageBox(OCPMSG_PLACECARD) != IDOK)
            {
                // then cancel
                break;
            }
            else
            {
                continue;
            }
        }

        if (PollError == CSC_CARDREQREPAIR)
        {
            if  (Repairs < 2 && CscRepairCard(gEod.DaysAdvance()) )
            {
                Repairs++;
                continue;
            }
            else
            {
                // we've alread repaired this card three times, something must be wrong!
                break;
            }
        }

        if (!PollError)
        {
            // Save the serial number of the CSC
            PollOk = TRUE;
            break;
        }
    }

    return PollOk;
}

BOOL CPostCsc::Refresh()
{
    DWORD RetVal;
    DWORD RetVal2;

    CscDataValid = FALSE;

	gPid.ResetPatronLanguage();

    if (Poll())
    {
        // initialise the buffers
        InvalidateData();
//        InvalidCsc.lnSerialNo = SerialNumber.serial_no;

        // setup the read
        InvalidCsc.lnSerialNo = SerialNumber.serial_no;

/* split up the old "OcpHyfReadFields()" into "Ocp2ValidateCsc()" plus refined "OcpHyfReadFields()" */
//        RetVal = Ocp2ValidateCsc(&InvalidCsc, &SerialNumber);
/* NWFF OCP/POST - change to use OCP common code */
	RetVal = OcpControlPollCsc(&InvalidCsc, sizeof(OcpInvalidCsc), &SerialNumber);
    RetVal2 = OcpHyfReadFields(/*&InvalidCsc,*/ &HyfInfo, &GeneralInfo, &PersonalInfo);
    if ((RetVal != OCPERROR_READ_FAULT) && (RetVal2 != OCPERROR_READ_FAULT))
	{
		GeneralInfo.LastAvDate = InvalidCsc.cdtLastAddValueDate;
	}
//[webcall][#14036][add negative value CSC as valid CSC so invalid reads are also caught]
	if (((RetVal == ERROR_SUCCESS) || (RetVal == OCPERROR_ZERO_NEG_VALUE)) && RetVal2 != ERROR_SUCCESS)		// [webcall][#12766][ocp][post][q1: check for error from RetVal2]
	{
		RetVal = RetVal2;
	}

/* NWFF OCP/POST - change to use OCP common code */
#if 0  
        if (RetVal == OCPERROR_CSC_BLOCKED)
        {
            // if the csc is blacklisted and not blocked, then block it
            if (InvalidCsc.bBlacklistedCSC && !InvalidCsc.bBlockedCSC)
            {
                // block the CSC as it is on the blacklist
                if (InvalidCsc.nBListReason == NOTBLOCKED)
                {
                    COcpEventDataCscBlockingBitClear blockingBitClear;

                    RetVal = OcpBlockUnblockCsc(static_cast<_BlockingCode> (InvalidCsc.nBListReason), &blockingBitClear);
                    if(RetVal == ERROR_SUCCESS)
                    {
                        blockingBitClear.Commit();
                    }
                }
                else
                {
                    COcpEventDataCscBlockingBitSet blockingBitSet;

                    RetVal = OcpBlockUnblockCsc(static_cast<_BlockingCode> (InvalidCsc.nBListReason), &blockingBitSet);
                    if(RetVal == ERROR_SUCCESS)
                    {
                        blockingBitSet.Commit();
                    }
                }

                // re-read the CSC
                if (RetVal == ERROR_SUCCESS)
                {
                    // Re-read the CSC, just to be sure
                    RetVal = Ocp2ValidateCsc(&InvalidCsc, &SerialNumber);
                    /* Just re-validate the card should do, no need to re-read Hyf fields */
                    /* RetVal = OcpHyfReadFields(&InvalidCsc, &HyfInfo, &GeneralInfo, &PersonalInfo); */
                    if (RetVal == ERROR_SUCCESS)
                    {
                        CString tmp;

                        // tell the operator that the csc is blocked
                        tmp = gEod.BlockingStatusStr(InvalidCsc.nBListReason);
                        OcpMessageBox(OCPMSG_ERROR, OCPERROR_CSC_BLOCKED, tmp);
                    }
                }
            }
        }
#else
    if (RetVal == OCPERROR_CSC_BLOCKED) 
    {
		OcpHandleBlockedStatusMessage();
	}
	else
#endif
        if ( RetVal == ERROR_SUCCESS || RetVal == OCPERROR_ZERO_NEG_VALUE )
        {
			// Adjust the monthly pass start dates to real time.
			int         
				Hours   =  gEod.NextDayStartTime() / 60,
				Minutes = (gEod.NextDayStartTime() % 60);
			COleDateTimeSpan
				BusDayStartTime( 0, Hours, Minutes, 0 );

            HyfInfo.HyfPass1StartDate += BusDayStartTime;
            HyfInfo.HyfPass2StartDate += BusDayStartTime;

            CscDataValid = TRUE;
			gPid.SetPatronLanguage( (GeneralInfo.LanguageSel == 1 )? EnglishLanguage : ChineseLanguage );
        }
		else
        {
            /*
            ** Display the error message.
            */
			OcpMessageBox(OCPMSG_ERROR, RetVal);
        }
    }
    return CscDataValid;
}

CCscLogicalId CPostCsc::GetLogicalCscId()
{
	time_t atime = 0;

	InvalidCsc.cdtLastAddValueDate.GetTimeTFromLocalTime(&atime);
    return CCscLogicalId(InvalidCsc.lnSerialNo, GeneralInfo.AddValueType, TimetToJulian(atime), GeneralInfo.AddValueAccum);
}


void CPostCsc::InvalidateData()
{
    CscDataValid = FALSE;
    InvalidCsc   = OcpInvalidCsc();
    HyfInfo      = OcpHyfCscInfo();
    GeneralInfo  = OcpGeneralCscInfo();
    TransInfo    = TransactionLog();
    PersonalInfo = OcpPersonalCscInfo();
}


BOOL CPostCsc::IsDataValid()
{
    return CscDataValid;
}


OcpHyfCscInfo *CPostCsc::GetHyfInfo()
{
    return &HyfInfo;
}


OcpGeneralCscInfo *CPostCsc::GetGeneralInfo()
{
    return &GeneralInfo;
}


OcpPersonalCscInfo *CPostCsc::GetPersonalInfo()
{
    return &PersonalInfo;
}


CHyfProcessFareCalc::CHyfProcessFareCalc() : CPostCsc()
{
}


CHyfProcessFareCalc::~CHyfProcessFareCalc()
{
}

int CHyfProcessFareCalc::IsAutopayUsed()
{
    return AutopayUsed;
}

int CHyfProcessFareCalc::IsMaxAutopayExceeded()
{
    return MaxAutopayExceeded;
}

// get the actual autopay amount from URP Reader/Writer
// if that's 0, autopay is not performed by the last deduct txn, use this to check for actual autopay
int CHyfProcessFareCalc::GetAutopayAmount()
{
	return AutoPayAmt;
}

BOOL CHyfProcessFareCalc::Refresh()
{
    // Initialise this variable
    AutopayUsed = FALSE;
    MaxAutopayExceeded = FALSE;
	IslandNum = 0;
	FareClass =INVALID_FARE_CLASS;

    // do the refresh properly
    return CPostCsc::Refresh();
}

BOOL CHyfProcessFareCalc::IsSufficientValue(CCurrency Fare)
{
    CCurrency Threshold;
    CDateTime Now = CDateTime::GetCurrentTime();

    // If direct debit is enabled, set the threshold
    if (GeneralInfo.DirectDebit > CCurrency(0, 0))
    {
        // this should be set to the system autopay threshold
        Threshold = gEod.AutopayThreshold();
    }
    else
    {
        // no threshold as DD is not enabled
        Threshold = CCurrency(0, 0);
    }

	// Pre-5.8 code if (Fare != CCurrency(0,0) && (GeneralInfo.PurseValue < CCurrency(0, 0) || GeneralInfo.PurseValue < (Fare + Threshold)))
	// An autopay transaction is performed only if the purse value is less than the Threshold.
	if ( Fare != CCurrency(0,0) && ( (GeneralInfo.PurseValue + GeneralInfo.NegValLimit) < Fare  
		|| GeneralInfo.PurseValue < Threshold || GeneralInfo.PurseValue <= CCurrency(0,0) ))
    {
        // Check for Autopay condition:
		// if the fare is not zero and the purse <= fare + threshold or purse <= 0
        if ((GeneralInfo.DirectDebit > CCurrency(0, 0)) &&
			((GeneralInfo.PurseValue + GeneralInfo.NegValLimit + GeneralInfo.DirectDebit) >= Fare))
        {
			if ( GeneralInfo.nAutopayDisableFlag )	// williamto 30Sept2013: FAT test failure 4.8.1.7, autopay has been disabled by the flag so stop subsequent processing and says insufficient funds
			{
				AutopayUsed = FALSE;
				return FALSE;
			}
            if (GeneralInfo.LastAvDate.GetDayOfYear() != Now.GetDayOfYear())
            {
                GeneralInfo.DdCounter = 0;
            }
            if (GeneralInfo.DdCounter < gEod.DirectDebitLimit())
            {
                AutopayUsed = TRUE;
            }
            else
            {
                MaxAutopayExceeded = TRUE;
                return FALSE;
            }
        }
        else
        {
            return FALSE;
        }
    }
    return TRUE;
}


//
// Writes a monthly pass
//

BOOL CHyfProcessFareCalc::WriteMonthlyPass(CCurrency Fare, Pass_t Pass, int IsCash)
{
    DWORD Result;

    // Poll the CSC for the serial number
    if (!Poll(TRUE))
    {
        return FALSE;
    }

    // Check that the number is the same
    if (InvalidCsc.lnSerialNo != SerialNumber.serial_no)
    {
        OcpMessageBox(_R( IDS_OCP_EXCLAIM_DIFFERENT_CSC ));
        return FALSE;
    }

    //
	// Write the Monthly Pass sale transaction to the CSC
	// Note that this transaction is a surcharge.
    //
	fIncomplete = TRUE;
	while ( Result = OcpHyfPassSale(&HyfInfo, &InvalidCsc, &GeneralInfo, Fare, (Pass == Pass1) ? TRUE : FALSE, IsCash, AutopayUsed, 
		&fIncomplete, &AutoPayAmt, DeductUDSignature, ATPUDSignature, &AlertCode) != ERROR_SUCCESS )
	{
		if ( fIncomplete || Result == OCPERROR_NO_CARD_PRESENT || Result == OCPERROR_WRITE_FAULT )	// williamto 06Aug2013: include write fault as unconfirmed txn
		{
// BEGIN_UNCONFIRMED_AV			
			RedoAddValue	
				RedoAdd( NULL, SerialNumber, FALSE );

			if( RedoAdd.DoModal() == IDCANCEL )
			{
				OcpSetError(OcpMapPollErr(ERR_CSCWRITE));
				return FALSE;
			}
//	END_UNCONFIRMED_AV
		}
		//else if ( fIncomplete && Result == OCPERROR_TARGET_SEQERR )
		//{
		//	break;
		//}
		else
		{
			OcpSetError(OcpMapPollErr(Result));
			break;
		}
	}
    return (Result == ERROR_SUCCESS);
}

BOOL CHyfProcessFareCalc::WriteMonthlyPass(CCurrency Fare, Pass_t Pass, int IsCash, BOOL RetryAllowed)
{
    DWORD Result;

#if 0 /* move to outer shell before the whole combo (AV + MP)transaction */
    // Poll the CSC for the serial number
    if (!Poll(TRUE))
    {
        return FALSE;
    }

    // Check that the number is the same
    if (InvalidCsc.lnSerialNo != SerialNumber.serial_no)
    {
        OcpMessageBox(_R( IDS_OCP_EXCLAIM_DIFFERENT_CSC ));
        return FALSE;
    }
#endif /* move to outer shell before the whole combo (AV + MP)transaction */

    do
	{
		//
		// Write the Monthly Pass sale transaction to the CSC
		// Note that this transaction is a surcharge.
		//
		Result = OcpHyfPassSale(&HyfInfo, &InvalidCsc, &GeneralInfo, Fare, (Pass == Pass1) ? TRUE : FALSE, IsCash, AutopayUsed, 
								&fIncomplete, &AutoPayAmt, DeductUDSignature, ATPUDSignature, &AlertCode);

    	if ( fIncomplete || Result == OCPERROR_NO_CARD_PRESENT || Result == OCPERROR_WRITE_FAULT )
		{
			RedoAddValue RedoAdd( NULL, SerialNumber, FALSE );
			if (RedoAdd.DoModal() == IDCANCEL)
			{
				fIncomplete = TRUE;			// even for no card present case
				break;
			}
		}
		else if ( Result != ERROR_SUCCESS )
		{
			OcpMessageBox( OCPMSG_ERROR, Result );

			if ( Result != OCPERROR_CSC_REJECTED )		// CSC rejected means this CSC is not eligible for the MT issue transaction, so don't retry
			{
				if (OcpMessageBox(_R(IDS_OCP_HYF_MTWRITE_FAIL_PROMPT), MB_YESNO|MB_ICONQUESTION) != IDYES)
					break;
			}
			else
				break;
		}
/*
		if ((Poll(FALSE) == TRUE) && (InvalidCsc.lnSerialNo == SerialNumber.serial_no))
		{
			Result = OcpHyfPassSale(&HyfInfo, &InvalidCsc, &GeneralInfo, Fare, (Pass == Pass1) ? TRUE : FALSE, IsCash, AutopayUsed, 
									&fIncomplete, &AutoPayAmt, DeductUDSignature, ATPUDSignature, &AlertCode);
		} */
    }  while ((Result != ERROR_SUCCESS) && (RetryAllowed == TRUE));

    return ( Result == ERROR_SUCCESS /*|| Result == OCPERROR_TARGET_SEQERR */);
}

#if 0 /* not used */
BOOL CHyfProcessFareCalc::WriteMonthlyPass(CCurrency Fare, Pass_t Pass, int IsCash, BOOL CheckForExistingCsc)
{
    DWORD Result;

    // Poll the CSC for the serial number
    if (!Poll(CheckForExistingCsc))
    {
        return FALSE;
    }

    // Check that the number is the same
    if (InvalidCsc.lnSerialNo != SerialNumber.serial_no)
    {
        OcpMessageBox(_R( IDS_OCP_EXCLAIM_DIFFERENT_CSC ));
        return FALSE;
    }

    //
	// Write the Monthly Pass sale transaction to the CSC
	// Note that this transaction is a surcharge.
    //
    Result = OcpHyfPassSale(&HyfInfo, &InvalidCsc, &GeneralInfo, Fare, (Pass == Pass1) ? TRUE : FALSE, IsCash, AutopayUsed);

    return (Result == ERROR_SUCCESS);
}
#endif /* not used */

BOOL CHyfProcessFareCalc::EraseMonthlyPass( Pass_t Pass, BOOL RetryAllowed )
{
	HyfInfo.HyfPass1Group1 = 0;
	HyfInfo.HyfPass1Group2 = 0;
	HyfInfo.HyfPass1StartDate = GeneralInfo.CscBaseDate;
	HyfInfo.HyfPass1TripIndex = 0;
	HyfInfo.HyfPass1ValidityPeriod = 0;
	HyfInfo.HyfPass1UnlimitedRide = 0;
	
	return WriteMonthlyPass( CCurrency(0), Pass, 1, RetryAllowed );
}

// williamto 05May2014: [webcall][#12641][OCP] cancel pre-reg HRT
BOOL CHyfProcessFareCalc::WriteConcessionReturnTicket(CCurrency Fare, int CrtTxnType, BOOL RetryAllowed)
{
    DWORD Result;

    do
	{
		//
		// Write the Monthly Pass sale transaction to the CSC
		// Note that this transaction is a surcharge.
		//
		Result = ::OcpHyfWriteConcessionReturnTicket(&HyfInfo, &InvalidCsc, &GeneralInfo, Fare, 0, 0, 0, 0, 0, AutopayUsed, CrtTxnType, IslandNum, 
			&fIncomplete, &AutoPayAmt, DeductUDSignature, ATPUDSignature, &AlertCode);

    	if ( fIncomplete || Result == OCPERROR_NO_CARD_PRESENT || Result == OCPERROR_WRITE_FAULT )
		{
			RedoAddValue RedoAdd( NULL, SerialNumber, FALSE );
			if (RedoAdd.DoModal() == IDCANCEL)
			{
				fIncomplete = TRUE;			// even for no card present case
				break;
			}
		}
		else if ( Result != ERROR_SUCCESS )
		{
			OcpMessageBox( OCPMSG_ERROR, Result );

			if ( Result != OCPERROR_CSC_REJECTED )		// CSC rejected means this CSC is not eligible for the MT issue transaction, so don't retry
			{
				if (OcpMessageBox(_R(IDS_OCP_HYF_MTWRITE_FAIL_PROMPT), MB_YESNO|MB_ICONQUESTION) != IDYES)
					break;
			}
			else
				break;
		}

    }  while ((Result != ERROR_SUCCESS) && (RetryAllowed == TRUE));


    return ( Result == ERROR_SUCCESS );
}

// [webcall][#13953][ocp] cancel DP
BOOL CHyfProcessFareCalc::WriteDayPass(CCurrency Fare, int DpTxnType, BOOL RetryAllowed)
{
    DWORD Result;

    do
	{
		//
		// Write the Monthly Pass sale transaction to the CSC
		// Note that this transaction is a surcharge.
		//
		Result = ::OcpHyfWriteDayPass(&HyfInfo, 
			&InvalidCsc, 
			&GeneralInfo, 
			Fare, 
			0, 
			0, 
			0, 
			0, 
			0, 
			AutopayUsed, 
			DpTxnType,
			&fIncomplete, 
			&AutoPayAmt, 
			DeductUDSignature, 
			ATPUDSignature, 
			&AlertCode);

    	if ( fIncomplete || Result == OCPERROR_NO_CARD_PRESENT || Result == OCPERROR_WRITE_FAULT )
		{
			RedoAddValue RedoAdd( NULL, SerialNumber, FALSE );
			if (RedoAdd.DoModal() == IDCANCEL)
			{
				fIncomplete = TRUE;			// even for no card present case
				break;
			}
		}
		else if ( Result != ERROR_SUCCESS )
		{
			OcpMessageBox( OCPMSG_ERROR, Result );

			if ( Result != OCPERROR_CSC_REJECTED )		// CSC rejected means this CSC is not eligible for the MT issue transaction, so don't retry
			{
				if (OcpMessageBox(_R(IDS_OCP_HYF_MTWRITE_FAIL_PROMPT), MB_YESNO|MB_ICONQUESTION) != IDYES)
					break;
			}
			else
				break;
		}

    }  while ((Result != ERROR_SUCCESS) && (RetryAllowed == TRUE));


    return ( Result == ERROR_SUCCESS );
}
// [webcall][#13953][ocp] cancel DP

BOOL CHyfProcessFareCalc::EraseConcessionReturnTicket( int nIsland, BOOL RetryAllowed )
{
	switch ( nIsland )
	{
		case 1:
			HyfInfo.HyfIsland1HoldayNum = NOT_HOLIDAY_NUM;
			HyfInfo.HyfIsland1FareClass = 0;
			break;
		case 2:
			HyfInfo.HyfIsland2HoldayNum = NOT_HOLIDAY_NUM;
			HyfInfo.HyfIsland2FareClass = 0;
			break;
		case 3:
			HyfInfo.HyfIsland3HoldayNum = NOT_HOLIDAY_NUM;
			HyfInfo.HyfIsland3FareClass = 0;
			break;
		case 4:
			HyfInfo.HyfIsland4HoldayNum = NOT_HOLIDAY_NUM;
			HyfInfo.HyfIsland4FareClass = 0;
			break;

		default:
			return FALSE;
	}

	return this->WriteConcessionReturnTicket( CCurrency(0, 0), CrtPreRegCancel, RetryAllowed );
}
// williamto 05May2014: [webcall][#12641][OCP] cancel pre-reg HRT

// [webcall][#13953][ocp] cancel DP
BOOL CHyfProcessFareCalc::EraseDayPass(BOOL RetryAllowed)
{
	HyfInfo.HyfDayPassDeluxeClass = 0;
	HyfInfo.HyfDayPassId = 0;
	HyfInfo.HyfDayPassStartDate = this->GeneralInfo.CscBaseDate;
	HyfInfo.HyfDayPassSurcharge = 0;
	HyfInfo.HyfDayPassTripCnt = 0;
	HyfInfo.HyfDayPassValidityPeriod = 0;
	HyfInfo.HyfDayPassZone = (DayPassType_t)0;
	
	return this->WriteDayPass(CCurrency(0, 0), enumDpTxnType::DpCancel, TRUE);
}
// [webcall][#13953][ocp] cancel DP

//
// This function is used for a freight transaction
//
BOOL CHyfProcessFareCalc::WriteFreightFare(CCurrency Fare)
{
    DWORD Result;

    // Poll the CSC for the serial number
    if (!Poll(TRUE))
    {
        return FALSE;
    }

    // Check that the number is the same
    if (InvalidCsc.lnSerialNo != SerialNumber.serial_no)
    {
        OcpMessageBox(_R( IDS_OCP_EXCLAIM_DIFFERENT_CSC ));
        return FALSE;
    }

    //
	// Write the freight transaction to the CSC
	// Note that this transaction is a surcharge.
    //
    Result = OcpHyfFrieghtTransaction(&HyfInfo, &InvalidCsc, &GeneralInfo, Fare, AutopayUsed);

    return (Result == ERROR_SUCCESS);
}

//
// This function is used for the following
// transactions (same transaction type):
//
// 1. single journey
// 2. group normal
// 3. concession sale
//
//

BOOL CHyfProcessFareCalc::WriteTripFare(CCurrency Fare, int PassUsed, int IsDeluxe, 
										int TripIndex, int PatronClass, int FareIndicator, 
                                        bool CrtSale, bool CrtValidate, bool CrtUpgrade, bool CrtSingleTicket, DWORD *pResult)
{
	CHyfTrip	HyfTrip;
	DWORD				
		Result;

	// Poll the CSC for the serial number
    if (!Poll(TRUE))
    {
        return FALSE;
    }

    // Check that the number is the same
    if (InvalidCsc.lnSerialNo != SerialNumber.serial_no)
    {
        OcpMessageBox(_R( IDS_OCP_EXCLAIM_DIFFERENT_CSC ));
        return FALSE;
    }

    if (PassUsed)
    {
        CDateTime   
			StartDateTime,
			ExpiryDateTime,
			Now = CDateTime::GetCurrentTime();
		COleDateTimeSpan	
			ValidityPeriod;
		// Determine the start of the business day for this sailing.
        int         
			Hours   =  gEod.NextDayStartTime() / 60,
			Minutes = (gEod.NextDayStartTime() % 60);

        StartDateTime.SetDateTime( Now.GetYear(), Now.GetMonth(), Now.GetDay(), Hours, Minutes, 0 );

		// If the sailing time is before the StartDateTime, then 
		// it must still be the previous business day.
        if( HyfTrip.GetSailingTime() < StartDateTime )
        {
            StartDateTime -= COleDateTimeSpan( 1, 0, 0, 0 );
		}

		if ( PassUsed == CHyfFareCalculation::MonthlyPass1 )
        {
            // get the validity period and find the end date
            ValidityPeriod.SetDateTimeSpan(HyfInfo.HyfPass1ValidityPeriod, 0, 0, 0);
            ExpiryDateTime = HyfInfo.HyfPass1StartDate + ValidityPeriod;
            ValidityPeriod = ExpiryDateTime - StartDateTime;

            HyfInfo.HyfPass1ValidityPeriod = (unsigned short) ValidityPeriod.GetDays();
            HyfInfo.HyfPass1StartDate      = StartDateTime;
            HyfInfo.HyfPass1TripIndex      = TripIndex;
        }
		else if ( PassUsed == CHyfFareCalculation::MonthlyPass2 )
        {
            ValidityPeriod.SetDateTimeSpan(HyfInfo.HyfPass2ValidityPeriod, 0, 0, 0);
            ExpiryDateTime = HyfInfo.HyfPass2StartDate + ValidityPeriod;
            ValidityPeriod = ExpiryDateTime - StartDateTime;

            HyfInfo.HyfPass2ValidityPeriod = (unsigned short) ValidityPeriod.GetDays();
            HyfInfo.HyfPass2StartDate      = StartDateTime;
            HyfInfo.HyfPass2TripIndex      = TripIndex;
        }
		else if ( PassUsed >= CHyfFareCalculation::DayPass1  )
		{
			if ( TripIndex == CHyfFareCalculation::DayPassFixedTrips )		// williamto 13Nov2013: for day pass, TripIndex = current day pass ride rule
				HyfInfo.HyfDayPassTripCnt += 1;
		}
    }

	if ( ( CrtSale || CrtValidate || CrtUpgrade ) && CrtSingleTicket )		// willimato 21Nov2013: [TA][POST] holiday data on CSC should only be set when HRT-related transactions are set
	{
		if ( CrtSale )	// FAT failure *********, record down "trip index" in the "FareClass"
		{
			FareClass = 0x1;
		}
		else if ( CrtValidate || CrtUpgrade )
		{
			FareClass = 0x2;
		}
		SetHolidayCscData(HyfTrip.GetHolidayNum());
	}

	// williamto 29Nov2013: SIT failure [POST][*******] POST should set deluxe bit indicator as GAK do (that means everything with deluxe class)
	HyfInfo.HyfDeluxeCabin = (IsDeluxe) ? 1 : 0;		// williamto 13Nov2013: NWFF says this bit should be set when MT/DP deluxe class upgrade paid

	//
    // Write to the CSC
    //
    Result = OcpHyfFareTransaction(&HyfInfo, &InvalidCsc, &GeneralInfo, Fare, 
								   HyfTrip.GetOriginTerminal(), HyfTrip.GetDestinationTerminal(), 
								   PatronClass, HyfTrip.GetFerryType(), PassUsed, IsDeluxe, 
								   (short) FareIndicator, AutopayUsed, CrtSale, CrtUpgrade, IslandNum, 
								&fIncomplete, &AutoPayAmt, DeductUDSignature, ATPUDSignature, &AlertCode);	// extended for OCP v7 card stack

    *pResult = Result;
    return (Result == ERROR_SUCCESS);
}

int CHyfProcessFareCalc::CheckForAntipassBack()
{
	OcpTransactionCscInfo TInfo;
	CDateTime			  Now = CDateTime::GetCurrentTime();
	COleDateTimeSpan	  AntiPassbackTime(0, 0, 0, gEod.AntipassbackTime());
    COleDateTimeSpan      SpanDay(1, 3, 0, 0);
    CDateTime             ExpiryTime(HyfInfo.HyfEntExp.GetYear(), HyfInfo.HyfEntExp.GetMonth(), HyfInfo.HyfEntExp.GetDay());
    DWORD                 Result;
	int                   Count;

    // The expiry time has to be extended to the next day at 3:00 am.
    // Note that this assumes that the expiry day is inclusive in
    // regards to validity.

    ExpiryTime += SpanDay;

    // Check for anti-passback
    // if we are not staff or staff has expired
/* CSC with staff entitlement as 1 skip checking staff entitlement expiry date */
    if (HyfInfo.HyfStaffEnt != 1 /*|| (HyfInfo.HyfStaffEnt == 1 &&  Now > ExpiryTime)*/)
    {
        Result = OcpGetTransactionCscInfo(&TInfo);
        if (Result == ERROR_SUCCESS)
        {
            for (Count = 0; Count < min(3, TInfo.NoOfTransactions); Count++)
            {
                if (Now >= TInfo.TransactionLogRecords[Count].DateTime && Now - TInfo.TransactionLogRecords[Count].DateTime < AntiPassbackTime)
                {
                    // we are interested in this record
                    if (TInfo.TransactionLogRecords[Count].SpId == HYF && TInfo.TransactionLogRecords[Count].Type == POST_DEBIT_TRANSACTION)
                    {
                        if (TInfo.TransactionLogRecords[Count].Details[1] == 1 ||
                            TInfo.TransactionLogRecords[Count].Details[1] == 3 ||
                            TInfo.TransactionLogRecords[Count].Details[1] == 6 ||
							TInfo.TransactionLogRecords[Count].Details[1] == 8 )	// williamto 13Nov2013: FAT failure 4.28.9 4.28.10, day pass usage checking
                        {
                            // make sure we only check these three kinds of post debit
                            // transactions (fare, monthly pass usage, concession sale).
                            return TRUE;
                        }
                    }
                    else
                    {
                        if (TInfo.TransactionLogRecords[Count].SpId == HYF && TInfo.TransactionLogRecords[Count].Type == HYF_TRANSACTION)
                        {
                            // no details to check
                            return TRUE;
                        }
					}
                }
            }
        }
	}
	return FALSE;
}

BOOL CHyfProcessFareCalc::IsPassExtended(Pass_t pass)
{
    CDateTime 
		dtNextMonth = CDateTime::GetFirstOfMonth();
	int         
		Hours   =  gEod.NextDayStartTime() / 60,
		Minutes = (gEod.NextDayStartTime() % 60);
	COleDateTimeSpan
		BusDayStartTime( 0, Hours, Minutes, 0 );

    dtNextMonth += BusDayStartTime;

    //
    // We try to work out if the pass has
    // been extended into the next month by
    // seeing if the startdate + valid days
    // is greater than the first day of the
    // next month.
    //
	if (Pass1 == pass)
    {
        COleDateTimeSpan spanValidDays(HyfInfo.HyfPass1ValidityPeriod, 0, 0, 0);

        if (dtNextMonth < (HyfInfo.HyfPass1StartDate + spanValidDays))
        {
            return TRUE;
        }
    }
    else
    {
        COleDateTimeSpan spanValidDays(HyfInfo.HyfPass2ValidityPeriod, 0, 0, 0);

        if (dtNextMonth < (HyfInfo.HyfPass2StartDate + spanValidDays))
        {
            return TRUE;
        }
    }
    return FALSE;
}

BOOL CHyfProcessFareCalc::PassValidAndNotExtended(Pass_t pass, int OriginGrp, int DestGrp)
{
	BOOL	  
		bRet	  = FALSE;
    CDateTime 
		BusDateTime,
		Now = CDateTime::GetCurrentTime();

	PRINT_DEBUG_TRACE( _T("\r\nOrigin=%d, Destination=%d"), OriginGrp, DestGrp);

	// Update to the start of the current business day;
	int         
		Hours   =  gEod.NextDayStartTime() / 60,
		Minutes = (gEod.NextDayStartTime() % 60);

    BusDateTime.SetDateTime( Now.GetYear(), Now.GetMonth(), Now.GetDay(), Hours, Minutes, 0 );

    if( Now < BusDateTime )
    {
        BusDateTime -= COleDateTimeSpan( 1, 0, 0, 0 );
	}

	switch (pass)
	{
		case Pass1:
			if (ValidateDates( BusDateTime, HyfInfo.HyfPass1StartDate, HyfInfo.HyfPass1ValidityPeriod))
			{
				// pass is valid and current
				if ((HyfInfo.HyfPass1Group2 == DestGrp && HyfInfo.HyfPass1Group1 == OriginGrp) || 
					(HyfInfo.HyfPass1Group2 == OriginGrp && HyfInfo.HyfPass1Group1 == DestGrp))
				{
					// pass groups match the ones we are selling
					if (!IsPassExtended(Pass1))
					{
						// pass has not already been extended so we can extend it
						bRet = TRUE;
					}
				}
			}
			break;
#if 1
		case Pass2:
			if (ValidateDates( BusDateTime, HyfInfo.HyfPass2StartDate, HyfInfo.HyfPass2ValidityPeriod))
			{
				// pass is valid and current
				if ((HyfInfo.HyfPass2Group2 == DestGrp && HyfInfo.HyfPass2Group1 == OriginGrp) || 
					(HyfInfo.HyfPass2Group2 == OriginGrp && HyfInfo.HyfPass2Group1 == DestGrp))
				{
					// pass groups match the ones we are selling
					if (!IsPassExtended(Pass2))
					{
						// pass has not already been extended so we can extend it
						bRet = TRUE;
					}
				}
			}
			break;
#endif
	}
    return bRet;
}

void CHyfProcessFareCalc::CreatePass(Pass_t pass, int OriginGrp, int DestGrp, int bCurrentMonth, int bExtendPass, int RideRule, CDateTime& StartDate, CDateTime& EndDate)
{
    int bCurrentMonthRollover;
    CDateTime
		TmpNow = CDateTime::GetCurrentTime(),
		TmpStartDate;
    COleDateTimeSpan    
		spanValidDays,
		spanDay(1, 0, 0, 0);

// williamto 27Oct2013: change request, NWFF says that org/dest groups should always be encoeded in ascending order
	int EncOriginGrp	= OriginGrp;
	int EncDestGrp		= DestGrp;
	BOOL bInverseTripIdx = FALSE;	// williamto 11Mar2014: [OP][OCP][POST] need to swap the the group 1/2 

	int         
		Hours   =  gEod.NextDayStartTime() / 60,
		Minutes = (gEod.NextDayStartTime() % 60);
    COleDateTimeSpan    
		BusDayStartTime( 0, Hours, Minutes, 0 );

    // Calculate the start of the current business day.
    CDateTime  Current;
    CDateTime NextMonth;
	Current.SetDateTime( TmpNow.GetYear(), TmpNow.GetMonth(), TmpNow.GetDay(), Hours, Minutes, 0 );
    if( TmpNow < Current )
    {
        Current -= spanDay;
    }

/* Fix to take into account the time interval between 1st of month 0:0:0 to NextDayStartTime */
    bCurrentMonthRollover = FALSE;
    if (TmpNow.GetMonth() != TmpNow.BusinessDate().GetMonth())
      bCurrentMonthRollover = TRUE;

    if (bCurrentMonthRollover)
		NextMonth = CDateTime::GetFirstOfMonth(&TmpNow, 1);
    else
		NextMonth = CDateTime::GetFirstOfMonth(&TmpNow, bCurrentMonth ? 1 : 2);
    NextMonth += BusDayStartTime;
	NextMonth -= spanDay;

    // Get the start date on the CSC
    switch (pass)
    {
		case Pass1:
			TmpStartDate = HyfInfo.HyfPass1StartDate;
            break;

		case Pass2:
			TmpStartDate = HyfInfo.HyfPass2StartDate;
            break;
    }
/* williamto 27Oct2013: change request, NWFF says that org/dest groups should always be encoeded in ascending order */
	if ( EncOriginGrp > EncDestGrp  )
	{
		EncOriginGrp = DestGrp; 
		EncDestGrp = OriginGrp;
		PRINT_DEBUG_TRACE(_T("\r\nEOD MT Destination Index Inversion!\r\nFareTable Origin=%d, Dest=%d\r\nSwapped Origin=%d, Dest=%d"), OriginGrp, DestGrp, EncOriginGrp, EncDestGrp);
	}	// williamto 28May2014: [webcall[#12726][OCP][POST] destination index from EOD should be swapped when index 1 > index 2

// williamto 28May2014: [webcall[#12726][OCP][POST] trip index swap should only consider the value of CSC	
	if ( EncOriginGrp == HyfInfo.HyfPass1Group2 && EncDestGrp == HyfInfo.HyfPass1Group1 )	// williamto 28Mar2014: [webcall #12519][OCP][POST] need to swap the the group 1/2 except for case where the origin/destination index already matches the swapped index if swapped
	{
		bInverseTripIdx = TRUE;	// williamto 11Mar2014: [OP][OCP][POST] need to swap the the group 1/2 
		PRINT_DEBUG_TRACE(_T("\r\nCSC MT Trip Index Inversion!\r\nFaretable Origin=%d, Dest=%d\r\nEncoded Origin=%d, Dest=%d"), EncOriginGrp, EncDestGrp, HyfInfo.HyfPass1Group1, HyfInfo.HyfPass1Group2);
	}
// williamto 28May2014: [webcall[#12726][OCP][POST] trip index swap should only consider the value of CSC	
    //
    // Set up all the pass fields. This is quite complex: see
    // the SRS for how it works.
    //
    switch (pass)
    {
      case Pass1:
            if (!bCurrentMonth)
            {
                if (bExtendPass)
                {
                    // If the pass is to be extended and the pass start date
                    // is equal to the current date, then we leave it alone,
                    // otherwise we set it to the current date and reset the
                    // trip index.
                    if (CompareDates(TmpStartDate, Current))
                    {
						TmpStartDate = Current;
                        HyfInfo.HyfPass1TripIndex = 0;
						HyfInfo.HyfPass1Group1 = EncOriginGrp;
						HyfInfo.HyfPass1Group2 = EncDestGrp;
                    }
					else
					{
						// williamto 11Mar2014: [OP][OCP][POST] need to swap the the group 1/2 except for case where the origin/destination index already matches the swapped index if swapped
						if ( bInverseTripIdx )
						{
							if ( HyfInfo.HyfPass1TripIndex == 1 )
								HyfInfo.HyfPass1TripIndex = 2;
							else if ( HyfInfo.HyfPass1TripIndex == 2 )
								HyfInfo.HyfPass1TripIndex = 1;
						}
						HyfInfo.HyfPass1Group1 = EncOriginGrp;
						HyfInfo.HyfPass1Group2 = EncDestGrp;
					}
                }
                else
                {
                    // we are purchasing a new pass for the next month
                    HyfInfo.HyfPass1Group1 = EncOriginGrp;
                    HyfInfo.HyfPass1Group2 = EncDestGrp;
                    HyfInfo.HyfPass1TripIndex = 0;
					
                    // in this case the start date has to be
                    // the beginning of the next month ie: not
                    // activated until the next month!
//                    TmpStartDate = Current.GetFirstOfMonth(&TmpNow);
                    TmpStartDate = Current.GetFirstOfMonth(&TmpNow.BusinessDate());
                }
            }
            else
            {
                // we are purchasing a new pass for the current month
                HyfInfo.HyfPass1Group1 = EncOriginGrp;
                HyfInfo.HyfPass1Group2 = EncDestGrp;
                TmpStartDate = Current;
                HyfInfo.HyfPass1TripIndex = 0;
            }

            spanValidDays = (NextMonth - TmpStartDate) ;
            HyfInfo.HyfPass1ValidityPeriod = (unsigned short) spanValidDays.GetDays();
            HyfInfo.HyfPass1StartDate = TmpStartDate;
			HyfInfo.HyfPass1UnlimitedRide = (BYTE)RideRule;
            break;
#if 0
          case Pass2:
            if (!bCurrentMonth)
            {
                if (bExtendPass)
                {
                    // If the pass is to be extended and the pass start date
                    // is equal to the current date, then we leave it alone,
                    // otherwise we set it to the current date and reset the
                    // trip index.
                    if (CompareDates(TmpStartDate, Current))
                    {
                        TmpStartDate = Current;
                        HyfInfo.HyfPass2TripIndex = 0;
                    }
                }
                else
                {
                    // we are purchasing a new pass for the next month
                    HyfInfo.HyfPass2Group1 = OriginGrp;
                    HyfInfo.HyfPass2Group2 = DestGrp;
                    HyfInfo.HyfPass2TripIndex = 0;

                    // in this case the start date has to be
                    // the beginning of the next month ie: not
                    // activated until the next month!
//                    TmpStartDate = Current.GetFirstOfMonth( &Current );
                    TmpStartDate = Current.GetFirstOfMonth(&TmpNow.BusinessDate() );
                }
            }
            else
            {
                // we are purchasing a new pass for the current month
                HyfInfo.HyfPass2Group1 = OriginGrp;
                HyfInfo.HyfPass2Group2 = DestGrp;
                TmpStartDate = Current;
                HyfInfo.HyfPass2TripIndex = 0;
            }

            spanValidDays = (NextMonth - TmpStartDate) ;
            HyfInfo.HyfPass2ValidityPeriod = (unsigned short) spanValidDays.GetDays();
            HyfInfo.HyfPass2StartDate = TmpStartDate;
            break;
#endif
    }  /* end of switch(pass) */

    StartDate = TmpStartDate;
    EndDate   = NextMonth;
}

CHyfProcessFareCalc::Pass_t CHyfProcessFareCalc::SellMonthlyPass(int Origin, int Destination, CDateTime& Start, CDateTime& End, 
	int& PassExtended, int& RideRule, BOOL ReissueMT, BOOL ThisMonth)
{
	int 		isFirstWindow;
	CString 	Text;
	BOOL		ValidPass;

	PassExtended = FALSE;

	// Student Monthly Ticket student status	
	StdmtAllowedInput_t stdmtInput;
	stdmtInput.cscId = this->SerialNumber.serial_no;
	stdmtInput.OriginGroup= Origin;
	stdmtInput.DestinationGroup = Destination;
	stdmtInput.bReissue = ReissueMT ? true:false;
	stdmtInput.bThisMonth = ThisMonth ? true:false;

	bool bStudent = CHyfFareCalculation::IsStudentStdmtAllowed(stdmtInput);

    CDateTime   
		CurrentDate = CDateTime::GetCurrentTime();
	int         
		Hours   =  gEod.NextDayStartTime() / 60,
		Minutes = (gEod.NextDayStartTime() % 60);
    COleDateTimeSpan    
		BusDayStartTime( 0, Hours, Minutes, 0 );

	// Strip off the time from the date (we don't need it)
	CurrentDate.ConvertToDateOnly();
	
	// If ReissueMT is true, then need to know whether this month's or
	// next month's pass is reissued.

	if (ReissueMT || IsInsideSellWindow(isFirstWindow))
	{
		if (ReissueMT)
		{
			if (ThisMonth)
				isFirstWindow = TRUE;
			
			else
				isFirstWindow = FALSE;
		}

		// Note that we can't sell a pass if
        // we're in the first window.
        if (!isFirstWindow) 
        {
			//if ( PassValidAndNotExtended( Pass1, Origin, Destination ) && PassValidAndNotExtended( Pass2, Origin, Destination ) ) 
			//{
			//	return NoPassesAvailable;
			//}
			if (PassValidAndNotExtended(Pass1, Origin, Destination))
			{
				COLORREF nBgColor;
				if (ReissueMT) {
					//AfxFormatString1( Text, IDS_OCP_RP_MT_REISSUED); //_T("EXTEND Pass 1?");
					//Text = MAKEINTRESOURCE(IDS_OCP_RP_MT_REISSUED);
					//Text += _T("1");
					Text = _R(IDS_OCP_RP_MT_REISSUED) + _T("1?");
					nBgColor = RGB_ORANGE;
				}
				else {
					AfxFormatString1( Text, IDS_OCP_HYF_MT_EXTEND, _T("1")); //_T("EXTEND Pass 1?");
					nBgColor = RGB_PALETURQUOISE;
				}

				//Student Monthly Ticket
				if (bStudent) {
					Text = _R(IDS_OCP_RP_STUDENT) + Text;
					nBgColor = RGB_GREEN;
				}
				
				if (OcpMessageBoxEx(NULL, Text, MB_YESNO|MB_ICONQUESTION, nBgColor, RGB_NAVY, 20) == IDYES)
				{
					Pass_t Ret;
					CreatePass(Pass1, Origin, Destination, FALSE, TRUE, RideRule, Start, End);
                    Ret = PassDateOverflow;
                    if ((IsPassDateOverflow(Start) == FALSE) && (IsPassDateOverflow(End) == FALSE))
                    {
						PassExtended = TRUE;
                        Ret = Pass1;
                    }
                    return Ret;
				}
				else	// sale is aborted
				{
					return SaleAborted;
				}
			}
	// [FAT][OCP][POST][Issue #57] extend valid MT 2 as MT 1 
			if (PassValidAndNotExtended(Pass2, Origin, Destination))	
			{
				COLORREF nBgColor;
				if (ReissueMT) {
					//AfxFormatString1( Text, IDS_OCP_RP_MT_REISSUED); //_T("EXTEND Pass 1?");
					//Text = MAKEINTRESOURCE(IDS_OCP_RP_MT_REISSUED);
					//Text += _T("2");
					Text = _R(IDS_OCP_RP_MT_REISSUED) + _T("2?");
					nBgColor = RGB_ORANGE;
				}
				else {
					//Text = "Extend Pass 2?";
					AfxFormatString1( Text, IDS_OCP_HYF_MT_EXTEND, _T("2")); //_T("EXTEND Pass 2?");
					nBgColor = RGB_PALETURQUOISE;
				}

				//Student Monthly Ticket
				if (bStudent) {
					Text = _R(IDS_OCP_RP_STUDENT) + Text;
					nBgColor = RGB_GREEN;
				}
                
				if (OcpMessageBoxEx(NULL, Text, MB_YESNO|MB_ICONQUESTION, nBgColor, RGB_NAVY, 20) == IDYES)
				{
                    Pass_t Ret;
                    CreatePass(Pass1, Origin, Destination, FALSE, TRUE, RideRule, Start, End);		
                    Ret = PassDateOverflow;
                    if ((IsPassDateOverflow(Start) == FALSE) && (IsPassDateOverflow(End) == FALSE))
                    {
                        PassExtended = TRUE;
                        Ret = Pass1;	// [FAT][OCP][POST][Issue #57] extend valid MT 2 as MT 1 
                    }
                    return Ret;
				}
				else	// sale is aborted
				{
					return SaleAborted;
				}
			}
	// [FAT][OCP][POST][Issue #57] extend valid MT 2 as MT 1 
        }
		// Create MT of this month
		ValidPass = ValidateDates(CurrentDate, HyfInfo.HyfPass1StartDate, HyfInfo.HyfPass1ValidityPeriod);

		// It is impossible to have 2 MT of the same route in the CSC.
		// If there are really 2 MT of the same route, it will always reissue the PASS 1.
		if ((HyfInfo.HyfPass1StartDate <= CurrentDate) && !ValidPass)
		{
			COLORREF nBgColor;
			if (ReissueMT) {
				//AfxFormatString1( Text, IDS_OCP_RP_MT_REISSUED);
				//Text = MAKEINTRESOURCE(IDS_OCP_RP_MT_REISSUED);
				//Text += _T("1");
				Text = _R(IDS_OCP_RP_MT_REISSUED) + _T("1?");
				nBgColor = RGB_ORANGE;

				
			}
			else {					
				AfxFormatString1( Text, IDS_OCP_HYF_MT_CREATE, _T("1")); //_T("CREATE Pass 1?");
				nBgColor = RGB_PALETURQUOISE;
			}

			//Student Monthly Ticket
			if (bStudent) {
				Text = _R(IDS_OCP_RP_STUDENT) + Text;
				nBgColor = RGB_GREEN;
			}
            
            if (OcpMessageBoxEx(NULL, Text, MB_YESNO|MB_ICONQUESTION, nBgColor, RGB_NAVY, 20) == IDYES)
            {
				// create pass 1
                Pass_t Ret;
                CreatePass(Pass1, Origin, Destination, isFirstWindow, FALSE, RideRule, Start, End);
                Ret = PassDateOverflow;
                if ((IsPassDateOverflow(Start) == FALSE) && (IsPassDateOverflow(End) == FALSE))
                {
					PassExtended = FALSE;
					Ret = Pass1;
                }
                return Ret;
            }
			else	// sale is aborted
			{
				return SaleAborted;
			}
		}
#if 0	// williamto 20121018: disable pass 2 usage
		ValidPass = ValidateDates(CurrentDate, HyfInfo.HyfPass2StartDate, HyfInfo.HyfPass2ValidityPeriod);

		if ((HyfInfo.HyfPass2StartDate <= CurrentDate) && !ValidPass)
		{
            Text = "Create Pass 2?";
// wc9039
//          if (OcpMessageBox(Text, MB_YESNO|MB_ICONQUESTION) == IDYES)
		  /* wc10209 bugfix */
            if (OcpMessageBoxEx(/*AfxGetMainWnd()->GetSafeHwnd()*/NULL, Text, MB_YESNO|MB_ICONQUESTION, RGB(255,153,0),RGB(0,102,0), 20) == IDYES)
            {
                Pass_t Ret;
        // create pass 2
                CreatePass(Pass2, Origin, Destination, isFirstWindow, FALSE, 0, Start, End);
                Ret = PassDateOverflow;
                if ((IsPassDateOverflow(Start) == FALSE) && (IsPassDateOverflow(End) == FALSE))
                {
					PassExtended = FALSE;
					Ret = Pass2;
                }
                return Ret;
            }
		}
#endif
    }
	else
	{
		return NotInSellWindow;
	}
    return NoPassesAvailable;
}

BOOL CHyfProcessFareCalc::ValidateDates(CDateTime Current, CDateTime Start, int ValidPeriod)
{
/* From PTWebCall#5674 onwards, now MP permit ValidityPeriod == 0 */
    if (ValidPeriod >= 0)
    {
        COleDateTimeSpan span(ValidPeriod, 0, 0, 0);

        // First the general range check
        if (Current >= Start && Current <= (Start + span))
        {
			return TRUE;
        }
        else
        {
            //  Now the exception to the rule check
			if (Current == Start)
            {
				return TRUE;
            }
        }
    }
	return FALSE;
}

int CHyfProcessFareCalc::CompareDates(CDateTime dt1, CDateTime dt2)
{
    DWORD dwDayInTime1;
    DWORD dwDayInTime2;
	long  lRes;

	dwDayInTime1  = dt1.GetYear() * 1000;
	dwDayInTime2  = dt2.GetYear() * 1000;
	dwDayInTime1 += dt1.GetDayOfYear();
	dwDayInTime2 += dt2.GetDayOfYear();

    lRes = dwDayInTime1 - dwDayInTime2;

	if (lRes > 0)
    {
        // Date 1 later
        return 1;
    }
    else if (lRes < 0)
    {
        // Date 2 later
        return -1;
    }
    else
    {
        return 0;
    }
}

BOOL CHyfProcessFareCalc::IsInsideSellWindow()
{
    BOOL dummy;
    return IsInsideSellWindow(dummy);
}

BOOL CHyfProcessFareCalc::IsInsideSellWindow(BOOL& inFirstWindow)
{
	BOOL			 bInside = FALSE;
	int				 nStartMargin;
	int				 nEndMargin;
    CDateTime        dtCurrent = CDateTime::GetCurrentTime();

    // Reset this first
    inFirstWindow = FALSE;

    //
    // We can only allow selling of monthly passes
    // between certain configurable ini entry times,
    // ie: so many days into the month and so many
    // days before the end of the month.  Here we
    // find out if we are currently within those times.
    //
	nStartMargin = gEod.PassSaleStartOfMonth();
	nEndMargin	 = gEod.PassSaleEndOfMonth();

	COleDateTimeSpan spanStartMargin(nStartMargin, 0, 0, 0);
	COleDateTimeSpan spanEndMargin(nEndMargin, 0, 0, 0);
	COleDateTimeSpan spanDay(1, 0, 0, 0);
/*        CDateTime        dtLocalDate(dtCurrent.GetYear(), dtCurrent.GetMonth(), dtCurrent.GetDay()); */
        CDateTime        dtLocalDate, dtStartCurrentMonthDate, dtEndCurrentMonthDate;
        //int              BussStartTimeHr, BussStartTimeMin;

/* Fix dtLocateDate with BussStartTime */
        dtLocalDate = dtCurrent.BusinessDate();
        dtEndCurrentMonthDate   = CDateTime::GetLastOfMonth(&dtLocalDate, 1);
        dtStartCurrentMonthDate = CDateTime::GetFirstOfMonth(&dtLocalDate, 0);

		if ((dtLocalDate < (dtStartCurrentMonthDate + spanStartMargin)) || (dtLocalDate > (dtEndCurrentMonthDate - spanEndMargin)))
		{
			// In first Window ????
			if (dtLocalDate < (dtStartCurrentMonthDate + spanStartMargin))
			{
				// Let the caller know which window we are in.
				inFirstWindow = TRUE;
			}
			bInside = TRUE;
		}
		return bInside;
}

void CHyfProcessFareCalc::SetHolidayCscData(int CurHolidayNum)
{
	if (FareClass != INVALID_FARE_CLASS)
	{
		switch (IslandNum)
		{
			case 1:
				HyfInfo.HyfIsland1HoldayNum = CurHolidayNum;
				HyfInfo.HyfIsland1FareClass = FareClass;
				//HyfInfo.HyfIsland2HoldayNum = HyfInfo.HyfIsland3HoldayNum = HyfInfo.HyfIsland4HoldayNum = 0;	// don't wipe 
				//HyfInfo.HyfIsland2FareClass = HyfInfo.HyfIsland3FareClass = HyfInfo.HyfIsland4FareClass = 0;
				break;
			case 2:
				HyfInfo.HyfIsland2HoldayNum = CurHolidayNum;
				HyfInfo.HyfIsland2FareClass = FareClass;
				//HyfInfo.HyfIsland1HoldayNum = HyfInfo.HyfIsland3HoldayNum = HyfInfo.HyfIsland4HoldayNum = 0; // don't wipe 
				//HyfInfo.HyfIsland1FareClass = HyfInfo.HyfIsland3FareClass = HyfInfo.HyfIsland4FareClass = 0;
				break;
			case 3:
				HyfInfo.HyfIsland3HoldayNum = CurHolidayNum;
				HyfInfo.HyfIsland3FareClass = FareClass;
				//HyfInfo.HyfIsland1HoldayNum = HyfInfo.HyfIsland2HoldayNum = HyfInfo.HyfIsland4HoldayNum = 0; // don't wipe 
				//HyfInfo.HyfIsland1FareClass = HyfInfo.HyfIsland2FareClass = HyfInfo.HyfIsland4FareClass = 0;
				break;
			case 4:
				HyfInfo.HyfIsland4HoldayNum = CurHolidayNum;
				HyfInfo.HyfIsland4FareClass = FareClass;
				//HyfInfo.HyfIsland1HoldayNum = HyfInfo.HyfIsland2HoldayNum = HyfInfo.HyfIsland3HoldayNum = 0; // don't wipe 
				//HyfInfo.HyfIsland1FareClass = HyfInfo.HyfIsland2FareClass = HyfInfo.HyfIsland3FareClass = 0;
				break;
		}
	}
	else
	{
		switch (IslandNum)
		{
			case 1:
				HyfInfo.HyfIsland1HoldayNum = NOT_HOLIDAY_NUM;
				HyfInfo.HyfIsland1FareClass = 0;
				break;
			case 2:
				HyfInfo.HyfIsland2HoldayNum = NOT_HOLIDAY_NUM;
				HyfInfo.HyfIsland2FareClass = 0;
				break;
			case 3:
				HyfInfo.HyfIsland3HoldayNum = NOT_HOLIDAY_NUM;
				HyfInfo.HyfIsland3FareClass = 0;
				break;
			case 4:
				HyfInfo.HyfIsland4HoldayNum = NOT_HOLIDAY_NUM;
				HyfInfo.HyfIsland4FareClass = 0;
				break;
		}
	}
}

/* Check whether the Pass Date overflow 13bit or not */
BOOL CHyfProcessFareCalc::IsPassDateOverflow(CDateTime PassDate)
{
  time_t tmpTimet;
  S16    s16DateTime;
  U16    u16DateTime;

  PassDate.GetTimeTFromDatePreserve(&tmpTimet);
  s16DateTime = TimetToCSCdate(tmpTimet);
  s16DateTime -= GeneralInfo.nCscBaseDate;
  if (s16DateTime < 0)
    return TRUE;
  u16DateTime = (U16) s16DateTime;
  if (u16DateTime > GetNWFFPassDateOverflowMax())
    return TRUE;

  return FALSE;
}
/* PTWebCall#3809 */
BOOL CHyfProcessFareCalc::CheckMonthlyPassValidPeriod()
{
/* We don't check the validity of HyInfo.HyfPass?StartDate because
   it can start either this month or next month, according to SRS of MT 
   (or even furture month in practice!)
*/

/*  CDateTime BusinessDateTime;
  CDateTime CurrentDateTime =CDateTime::GetCurrentTime();

  int Hours = gEod.NextDayStartTime()/60;
  int Minutes = (gEod.NextDayStartTime() % 60);
  
  BusinessDateTime.SetDateTime(CurrentDateTime.GetYear(), CurrentDateTime.GetMonth(), CurrentDateTime.GetDay(), Hours, Minutes, 0);  
    
  if (CurrentDateTime < BusinessDateTime)
    BusinessDateTime -= COleDateTimeSpan(1,0,0,0);
*/
    
/*  if ((HyfInfo.HyfPass1StartDate <= BusinessDateTime) && (HyfInfo.HyfPass1ValidityPeriod < 40))
   if ((HyfInfo.HyfPass2StartDate <= BusinessDateTime) && (HyfInfo.HyfPass2ValidityPeriod < 40))
*/
  if ((HyfInfo.HyfPass1ValidityPeriod < MAX_HYF_MONTHLYPASS_VALIDPERIOD) && (HyfInfo.HyfPass2ValidityPeriod < MAX_HYF_MONTHLYPASS_VALIDPERIOD))
    return TRUE;
  else
    return FALSE;
  
}
/* PTWebCall#5285 */
/*
 - Duplicate this check from OcpAddValue,
   perform this check before OcpAddValue so that UD & event log
   log operation in actual sequence

 - because the similar check in OcpAddValue make use of total purse
   (ie. purse + negative limit), so we follow it here.
 */

/* 03-Dec-2019
As OCL announce to allow CSC max. RV up to $3000 on 01-Dec, we set the OCP EOD maximum RV to $3000 in workshop and purchase a Monthly Ticket on a normal Octopus (Max. Stored Value on CSC MSVC = $1000) with RV $921.6

However, the operations fail as shown on attached pics. The main reason is that there is no way to determine the MSVC of Octopus as mentioned by William.

A quick solution is supposed as followed

Add an options in ��ocp.ini��

MT_MSVC1=1000
MT_MSVC2=3000
MT_MSVC3=5000
MT_MSVC4=10000

For OCP MT purchase (paid by Cash), retrieve the CSC_RV,
If CSC_RV <= MT_MSVC1 then assume MSVC = MT_MSVC1,
If CSC_RV > MT_MSC1 and CSC RV <= MT_MSVC2 then assume MSVC = MT_MSVC2
If CSC_RV > MT_MSC2 and CSC RV <= MT_MSVC3 then assume MSVC = MT_MSVC3
If CSC_RV > MT_MSC3 and CSC RV <= MT_MSVC4 then assume MSVC = MT_MSVC4

*/
BOOL CHyfProcessFareCalc::IsPurseOverflow(CCurrency AddPurseValue)
{
	CCurrency maxPurseValue;
	CCurrency purseValue = GeneralInfo.PurseValue;
	CCurrency eodMaxPurseValue = Ocp2GetMaxPurseValue(GeneralInfo.CardType, GeneralInfo.ConcessionFlag);

	ULONG msvc1 = gEod.MtMsvc1();
	ULONG msvc2 = gEod.MtMsvc2();
	ULONG msvc3 = gEod.MtMsvc3();
	ULONG msvc4 = gEod.MtMsvc4();

	CCurrency MT_MSVC1(msvc1*10);//MT_MSVC1=1000
	CCurrency MT_MSVC2(msvc2*10);//MT_MSVC2=3000
	CCurrency MT_MSVC3(msvc3*10);//MT_MSVC3=5000
	CCurrency MT_MSVC4(msvc4*10);//MT_MSVC4=10000
	
	if (purseValue <= MT_MSVC1) {
		maxPurseValue = MT_MSVC1;
	}
	else if (purseValue <= MT_MSVC2) {
		maxPurseValue = MT_MSVC2;
	}
	else if (purseValue <= MT_MSVC3) {
		maxPurseValue = MT_MSVC3;
	}
	else if (purseValue <= MT_MSVC4) {
		maxPurseValue = MT_MSVC4;
	}
	else {
		maxPurseValue = eodMaxPurseValue;
	}
    
	//This should not happen if logic is correct. Just in case.
	if (maxPurseValue > eodMaxPurseValue) {
		maxPurseValue = eodMaxPurseValue;
	}

  if (OcpIsPurseOverflow( maxPurseValue,
                          AddPurseValue,
                          (GeneralInfo.PurseValue + GeneralInfo.NegValLimit),
                          GeneralInfo.NegValLimit ) )
    return TRUE;
  else
    return FALSE;
}

// [webcall][#14688] Check whether the deduction amount (particularly for MT) would cause the RV to underflow (lower than negative limit) 
BOOL CHyfProcessFareCalc::IsPurseUnderflow(CCurrency DeductValue)
{
	return (BOOL)(::OcpIsPurseUnderflow( DeductValue,
                          GeneralInfo.PurseValue,
                          CCurrency(-1 * GeneralInfo.NegValLimit)));
}

// [webcall][#12728] Check whether the current purse value is sufficient to cover the fare disregarding autopay
BOOL CHyfProcessFareCalc::IsSufficientValueNoAutopay(CCurrency Fare)
{
    CDateTime Now = CDateTime::GetCurrentTime();

	// An autopay transaction is performed only if the purse value is less than the Threshold.
	if ( Fare != CCurrency(0,0) && ( (GeneralInfo.PurseValue + GeneralInfo.NegValLimit) < Fare ) )
    {
		return FALSE;
    }
    return TRUE;
}


//===========================================================================
//  Function:  CAddValue::AlertOperatorAddValue( CCurrency amount,
//				    						     CUdCscAddValue *pUD,
//												 CscCardSerialNum_t CurrentSerialNum )
//
//   Description:
//
//		Added on 4-12-97 in response to correspondence from CREATIVE STAR
//		ref: C901/97/L/1270.
//
//		This function has been introduced to ALERT the operator that a CSC
//		write failure has occurred and prompts the operator to re-present
//		the CSC.
//
//		The ALERT mechanism is series of calls to "beep" for at most 30 seconds.
//		During which time if the same CSC is re-presented the beeping stops and
//		if the faulty write has been corrected the ADDVALUE procedure completes
//		as normal.
//
//   Parameter(s):
//
//     	CCurrency		amount	- As per function OcpAddValue.
//     	CUdCscAddValue	*pUD	- As per function OcpAddValue.
//
//   Returns:
//
//		int		As per function OcpAddValue.
//
//  Comments:      None
//
//===========================================================================
DWORD	CHyfProcessFareCalc::AlertOperatorAddValue(CCurrency amount,
										  COcpEventDataCscAddValueBase *pEventData,
										  //CUdCscAddValue *pUD,
										  CscCardSerialNum_t CurrentSerialNum,
										  int &cancelOperation, // cs security fix
										  int &WriteUDResult)  // cs security fix
{
	CString			text;
	int				CscAPIError = 0;
	//BOOL			incomplete = TRUE;
	DWORD			res;
	//	BEGIN_UNCONFIRMED_AV_20030106
	DWORD			resbak;
	//	END_UNCONFIRMED_AV_20030106
	DWORD			DialogRes;
//	BEGIN_UNCONFIRMED_AV
//	RedoAddValue	RedoAdd(NULL,CurrentSerialNum);
	CString			UnconfirmedAVType;
	RedoAddValue	RedoAdd(NULL,CurrentSerialNum,TRUE);
//	END_UNCONFIRMED_AV
/*        COCPAddValueDlg parent; */
    COcpEventDataCscAddValueBase *pCopyEventData;  // cs security fix

	// *** Imp Audit / Shift Registers / COB 29/05/97
	//AuditRegisters	Audit;
	//AuditInfo			AudInfo;
	//ShiftInfo *		pShift = AudInfo.GetCurrentShiftInfo();
	//CAddValue			Add;
	//COcpEventDataCscUnconfirmedAddValue UnconfirmedEventData;

	//	BEGIN_UNCONFIRMED_AV_20030106
	DialogRes = IDOK;
	//	END_UNCONFIRMED_AV_20030106
    pCopyEventData = pEventData;  // cs security fix - use the copy only
	cancelOperation = FALSE;
	do
	{
        res = OcpAddValue( amount, &fIncomplete, ATPUDSignature, &AlertCode, pCopyEventData, &CscAPIError, &WriteUDResult );  // cs security fix
		//	BEGIN_UNCONFIRMED_AV_20030106
/* if retry add value, skip condition check to quit, must ask to retry,
   ensuring there is a unconfirmed add value UD before cancel & quit
*/
		if (DialogRes == IDRETRY) {
			if (!( (res == ERROR_SUCCESS) || (CscAPIError == CSC_CARDREQREPAIR) )) {
				resbak = res;
				res = OCPERROR_WRITE_FAULT;
			}
		}
		//	END_UNCONFIRMED_AV_20030106

        if (res == OCPERROR_NO_CARD_PRESENT) {  // cs security fix - start
            fIncomplete = FALSE;
            cancelOperation = TRUE;
        }  // cs security fix - end

//	BEGIN_UNCONFIRMED_AV
		//	BEGIN_UNCONFIRMED_AV_20030106
//		else if ( pEventData == NULL || res == OCPERROR_CSC_SYSTEM_CORRUPT || 
//			      res == OCPERROR_READ_FAULT || res == OCPERROR_PURSE_EXCEEDED )
/* PTWebCall#6913, For all other errors from Ocp2Repoll() from 1st addvalue, we'll quit */
		else if ( pEventData == NULL || res == OCPERROR_CSC_SYSTEM_CORRUPT || 
			      res == OCPERROR_READ_FAULT || res == OCPERROR_PURSE_EXCEEDED ||
				  res == OCPERROR_CSC_FAULTY || res == OCPERROR_INV_CARD_KEY ||
				  res == OCPERROR_CSC_CORRUPT || res == OCPERROR_TARGET_BUSY ||
				  res == OCPERROR_TARGET )
		//	END_UNCONFIRMED_AV_20030106
		{
			if ( pEventData == NULL )
				res = OcpSetError( OCPERROR_CSC_SYSTEM_CORRUPT );

			fIncomplete = FALSE;
			cancelOperation = TRUE;
		}
//	END_UNCONFIRMED_AV
//	BEGIN_MULTIPLE_CSC_DETECT
		else if ( res == OCPERROR_MULTIPLE_CARD )
		{
			res = OcpSetError( res );
			fIncomplete = FALSE;
			cancelOperation = TRUE;
		}
//	END_MULTIPLE_CSC_DETECT
		else if ( res == OCPERROR_CSC_REJECTED )
		{
			res  = OcpSetError( res );
			fIncomplete = FALSE;
			cancelOperation = TRUE;
		}
		else if ( res == OCPERROR_TARGET_SEQERR )
		{
			res = OcpSetError( res );
			fIncomplete = FALSE;
			cancelOperation = TRUE;
			// successful, reset the UD and send again
			//pEventData->Reset();
			//pEventData->Commit();			
		} 

		else if ( !( ( res == ERROR_SUCCESS )||(CscAPIError == CSC_CARDREQREPAIR) ) || fIncomplete )  // cs security fix - change from if to else if
		{
			//	BEGIN_UNCONFIRMED_AV_20030106
			if ( DialogRes == IDRETRY ) {
				res = resbak;
			}
			//	END_UNCONFIRMED_AV_20030106

			DialogRes = RedoAdd.DoModal();
			if ( DialogRes == IDRETRY )
			{
				fIncomplete = TRUE;
			}
//	BEGIN_UNCONFIRMED_AV
//			else if	( DialogRes == IDCANCEL )
			else if	( DialogRes == IDCANCEL || DialogRes == IDC_REDOAV_RETAIN )
//	END_UNCONFIRMED_AV
			{
				fIncomplete = FALSE;
				cancelOperation = TRUE;
				res = ERROR_SUCCESS;

				//	Add.DisplayCancelledAddValue();

				// Increment "Unconfirmed Txn Counters"
				pEventData->ConfirmationFailure();

				// cs security fix - start - log event for unconfirmed add value *** Start ***
				CLogEvtCSC logevt;
				logevt.SetCSCEntry(TRUE, IDS_OCP_EVT_UNCONFIRMED_ADD_VALUE,CurrentSerialNum.serial_no, amount, 0);
				COcpEventDataOcpEventLogI::WriteLog(logevt);
				CNonVolatile	nv;
				ShiftInfo		oldShiftInfo;
				DailyInfo		oldDailyInfo;
				if( nv.Read( &oldShiftInfo, CNonVolatile::OCP_SHIFT_INFO, CNonVolatile::READ_LOCK ) )
				{
					oldShiftInfo.UnconfirmedAddValue.Add( 1, amount );
					nv.Write( &oldShiftInfo );
				}
				if( nv.Read( &oldDailyInfo, CNonVolatile::OCP_DAILY_INFO, CNonVolatile::READ_LOCK ) )
				{
					oldDailyInfo.UnconfirmedAddValue.Add( 1, amount );
//	BEGIN_UNCONFIRMED_AV
					if ( DialogRes == IDCANCEL )
						oldDailyInfo.TotalUnconfirmedAV_Cancel.Add( 1, amount );
					else
						oldDailyInfo.TotalUnconfirmedAV_Retain.Add( 1, amount );
//	END_UNCONFIRMED_AV
					nv.Write( &oldDailyInfo );
				}
				// cs security fix - end
				// UnconfirmedEventData.Commit(amount);
//	BEGIN_UNCONFIRMED_AV
#if 0  /* not in NWFF */
				if ( DialogRes == IDC_REDOAV_RETAIN )
				{
					UnconfirmedAVRetain( CurrentSerialNum.serial_no );
					EndDialog( IDC_REDOAV_RETAIN );
				}
#endif /* not in NWFF */
				//CRepPrn crep;
				CCurrency CurRV;

				CurRV = ULONG(UnconfirmedAV_CSCData.PurseValue.GetValueIn10th()) - ULONG(UnconfirmedAV_CSCData.NegValLimit.GetValueInCents());
#if 0 /* not in NWFF */
				if ( gEod.ServiceProviderId() == MTRC )
				{
					if ( DialogRes == IDC_REDOAV_RETAIN )
						UnconfirmedAVType = "[R]";
					else
						UnconfirmedAVType = "[C]";
				}
				else
#endif /* not in NWFF */
				{
					UnconfirmedAVType.Empty();
				}

				CRepPrn::DiskPrintUnconfirmedAV(	pEventData, UnconfirmedAV_CSCData.PatronPrefer,
												CurRV,
												UnconfirmedAV_CSCData.DirectDebit,
												UnconfirmedAV_CSCData.TransactionNum, UnconfirmedAVType );
//	END_UNCONFIRMED_AV
			}
		}
		else
		{
			fIncomplete = FALSE;
			// successful, reset the UD and send again
			pEventData->Reset();
			pEventData->SetConfirmationState(TRUE);
			pEventData->Commit();
		}
		pCopyEventData = NULL;  // cs security fix - never attempt to write UD twice
	}
	while( fIncomplete );

	if ( CscAPIError == CSC_CARDREQREPAIR )
	{
		// Removed to stop Operator worrying about it.
		//text = "A partial CSC write has occurred.\n"
		//	   "This will be fixed when CSC is next used.";
		//OcpMessageBox( text, MB_OK );

		res = ERROR_SUCCESS;
	}

	return ( res );
}

BOOL CHyfProcessFareCalc::IsV2MTBothValid( void )
{
	CDateTime	Now = CDateTime::GetCurrentTime(), 
		BusinessDate;
	// Update to the start of the current business day;
	int         
		Hours   =  gEod.NextDayStartTime() / 60,
		Minutes = (gEod.NextDayStartTime() % 60);

    BusinessDate.SetDateTime( Now.GetYear(), Now.GetMonth(), Now.GetDay(), Hours, Minutes, 0 );

	if( Now < BusinessDate )
    {
        BusinessDate -= COleDateTimeSpan( 1, 0, 0, 0 );
	}

	if ( ( HyfInfo.HyfVersion <= NWFF_EXPIRY_CSCDEF_VER ) && 
		ValidateDates( BusinessDate, HyfInfo.HyfPass1StartDate, HyfInfo.HyfPass1ValidityPeriod) && 
		ValidateDates(BusinessDate, HyfInfo.HyfPass2StartDate, HyfInfo.HyfPass2ValidityPeriod ) )
	{
		return TRUE;
	}
	
	return FALSE;
}

/* end of file */
