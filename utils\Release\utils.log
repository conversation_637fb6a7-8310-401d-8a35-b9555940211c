﻿Build started 2/7/2025 2:51:28 pm.
     1>Project "D:\GIT_CLONE\ocp_nwff_upgrade\utils\utils.vcxproj" on node 4 (build target(s)).
     1>InitializeBuildStatus:
         Creating ".\Release\utils.unsuccessfulbuild" because "AlwaysCreate" was specified.
       ClCompile:
         All outputs are up-to-date.
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I..\inc /Zi /nologo /W3 /WX- /O2 /Ob1 /Oy- /D _AFXDLL /D _USE_32BIT_TIME_T /D _WIN32_WINNT=0x0501 /D UNICODE /D _UNICODE /GF /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Fo".\Release\\" /Fd".\Release\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt HexEdit.cpp Serialno.cpp
         HexEdit.cpp
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(255): warning C4005: 'MSG_UNCONF_TRANSACTION_CSC' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(666) : see previous definition of 'MSG_UNCONF_TRANSACTION_CSC'
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(264): warning C4005: 'MSG_UNCONF_AUTOPAY' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(674) : see previous definition of 'MSG_UNCONF_AUTOPAY'
         Serialno.cpp
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(255): warning C4005: 'MSG_UNCONF_TRANSACTION_CSC' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(666) : see previous definition of 'MSG_UNCONF_TRANSACTION_CSC'
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(264): warning C4005: 'MSG_UNCONF_AUTOPAY' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(674) : see previous definition of 'MSG_UNCONF_AUTOPAY'
     1>Serialno.cpp(270): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         Generating Code...
       Lib:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\Lib.exe /OUT:".\Release\utils.lib" /NOLOGO .\Release\crc32.obj
         .\Release\Currency.obj
         .\Release\Datetime.obj
         .\Release\filever.obj
         .\Release\HexEdit.obj
         .\Release\hk1util.obj
         .\Release\Lowu.obj
         .\Release\md5.obj
         .\Release\RegistrationNumber.obj
         .\Release\RegistrationNumberGenerator.obj
         .\Release\Serialno.obj
         .\Release\sha_1.obj
         .\Release\WinUtil.obj
         utils.vcxproj -> D:\GIT_CLONE\ocp_nwff_upgrade\utils\.\Release\utils.lib
       FinalizeBuildStatus:
         Deleting file ".\Release\utils.unsuccessfulbuild".
         Touching ".\Release\utils.lastbuildstate".
     1>Done Building Project "D:\GIT_CLONE\ocp_nwff_upgrade\utils\utils.vcxproj" (build target(s)).

Build succeeded.

Time Elapsed 00:00:03.92
