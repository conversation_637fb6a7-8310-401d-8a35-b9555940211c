// GroupTicketDeductionTest.cpp : Test program to simulate group ticket deductions using Octopus
//
// This test program demonstrates the simulation of group ticket item deductions
// using the existing Octopus CSC infrastructure. It creates mock ticket items
// and simulates the deduction process without requiring actual hardware.

#include "stdafx.h"
#include "GroupTicketDeductionTest.h"
#include "eod.h"
#include "ocpcsc.h"
#include "Currency.h"
#include "PosItem.h"
#include <vector>
#include <iostream>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

using namespace std;

// Mock CSC data structure for simulation
struct MockCSCData {
    CCurrency purseValue;
    CCurrency negativeValueLimit;
    ULONG physicalId;
    bool isAutopayEnabled;
    CCurrency autopayAmount;
    bool fIncomplete;
    BYTE alertCode;
    BYTE deductUDSignature[4];
    BYTE atpUDSignature[4];
};

// Global mock CSC for testing
MockCSCData g_mockCSC;

// Mock function to simulate OcpDeduct for testing
DWORD MockOcpDeduct(CCurrency ccyAmount, BYTE* fIncomplete, BYTE* pATPAmount,
                   BYTE* DeductUDSignature, BYTE* ATPUDSignature, BYTE* pAlertCode,
                   bool bAutopayTriggered, transact_t* pDetails = NULL)
{
    cout << "MockOcpDeduct: Attempting to deduct " << ccyAmount.Format() << endl;

    // Validate input parameters
    if (!fIncomplete || !pATPAmount || !DeductUDSignature || !ATPUDSignature || !pAlertCode) {
        cout << "MockOcpDeduct: Invalid parameters" << endl;
        return OCPERROR_INVALID_FIELDS;
    }

    // Check for zero or negative amount
    if (ccyAmount.GetValueIn10th() <= 0) {
        cout << "MockOcpDeduct: Invalid amount: " << ccyAmount.Format() << endl;
        *fIncomplete = TRUE;
        return OCPERROR_INVALID_FIELDS;
    }

    // Check if sufficient funds (including negative value limit)
    CCurrency availableFunds = g_mockCSC.purseValue + g_mockCSC.negativeValueLimit;
    if (availableFunds < ccyAmount) {
        cout << "MockOcpDeduct: Insufficient funds. Available: " << availableFunds.Format()
             << ", Required: " << ccyAmount.Format() << endl;
        *fIncomplete = TRUE;
        return OCPERROR_INSUFFICIENT_FUNDS;
    }

    // Simulate autopay if enabled and needed
    if (bAutopayTriggered && g_mockCSC.isAutopayEnabled) {
        cout << "MockOcpDeduct: Autopay triggered" << endl;
        g_mockCSC.autopayAmount = CCurrency(100, 0); // $10.00 autopay
        g_mockCSC.purseValue = g_mockCSC.purseValue + g_mockCSC.autopayAmount;
        cout << "MockOcpDeduct: Autopay added " << g_mockCSC.autopayAmount.Format() << endl;
    }

    // Simulate successful deduction
    g_mockCSC.purseValue = g_mockCSC.purseValue - ccyAmount;
    *fIncomplete = FALSE;
    *pATPAmount = (BYTE)(g_mockCSC.autopayAmount.GetValueIn10th() & 0xFF);
    *pAlertCode = 0;

    // Mock signature data (simulating cryptographic signatures)
    memset(DeductUDSignature, 0xAB, 4);
    memset(ATPUDSignature, 0xCD, 4);

    cout << "MockOcpDeduct: Success! New purse value: " << g_mockCSC.purseValue.Format() << endl;
    return ERROR_SUCCESS;
}

// Utility function to format currency for display
string FormatCurrencyForDisplay(const CCurrency& amount) {
    return string(amount.Format());
}

// Utility function to validate CSC state
bool ValidateCSCState() {
    if (g_mockCSC.purseValue.GetStatus() != COleCurrency::valid) {
        cout << "ERROR: Invalid purse value" << endl;
        return false;
    }

    if (g_mockCSC.negativeValueLimit.GetStatus() != COleCurrency::valid) {
        cout << "ERROR: Invalid negative value limit" << endl;
        return false;
    }

    if (g_mockCSC.physicalId == 0) {
        cout << "WARNING: Physical ID is zero" << endl;
    }

    return true;
}

// Test class to simulate group ticket deduction functionality
class GroupTicketDeductionSimulator {
private:
    vector<CPosTicketItem> mItems;
    MockCSCData* csc;
    
public:
    GroupTicketDeductionSimulator() : csc(&g_mockCSC) {}
    
    // Add a ticket item to the group
    void AddTicketItem(int itemId, bool isDeluxe, int quantity, CCurrency price) {
        CPosTicketItem item(itemId, isDeluxe, quantity, price);
        mItems.push_back(item);
        cout << "Added ticket item: ID=" << itemId 
             << ", Deluxe=" << (isDeluxe ? "Yes" : "No")
             << ", Qty=" << quantity 
             << ", Price=" << price.Format()
             << ", Subtotal=" << item.GetItemSubtotal().Format() << endl;
    }
    
    // Simulate the DeductGroupTicketsByItem functionality
    bool DeductGroupTicketsByItem(bool groupSingleTicket = false) {
        cout << "\n=== Starting Group Ticket Deduction Simulation ===" << endl;
        cout << "Initial purse value: " << csc->purseValue.Format() << endl;
        cout << "Negative value limit: " << csc->negativeValueLimit.Format() << endl;
        cout << "Total items to process: " << mItems.size() << endl;
        
        bool bOk = true;
        
        for (int i = 0; i < (int)mItems.size(); i++) {
            CCurrency itemFare = mItems[i].GetItemSubtotal();
            int itemPatronClass = mItems[i].GetItemId();
            BOOL itemIsDeluxe = mItems[i].GetDeluxeTicket();
            CCurrency purse = csc->purseValue;
            
            cout << "\n--- Processing item " << (i+1) << " of " << mItems.size() << " ---" << endl;
            cout << "Item fare: " << itemFare.Format() << endl;
            cout << "Patron class: " << itemPatronClass << endl;
            cout << "Is deluxe: " << (itemIsDeluxe ? "Yes" : "No") << endl;
            
            csc->fIncomplete = TRUE;
            int retryCount = 0;
            const int maxRetries = 3;
            
            while (csc->fIncomplete && retryCount < maxRetries) {
                cout << "Deduction attempt " << (retryCount + 1) << ", fIncomplete=" << (csc->fIncomplete ? "TRUE" : "FALSE") << endl;
                
                DWORD res = MockOcpDeduct(itemFare, &csc->fIncomplete, 
                                        &csc->alertCode, csc->deductUDSignature, 
                                        csc->atpUDSignature, &csc->alertCode, 
                                        csc->isAutopayEnabled);
                
                if (res != ERROR_SUCCESS) {
                    cout << "Deduction failed with error code: " << res << endl;
                    if (res == OCPERROR_INSUFFICIENT_FUNDS) {
                        cout << "Insufficient funds for item " << (i+1) << endl;
                        bOk = false;
                        break;
                    }
                } else if (!csc->fIncomplete) {
                    cout << "Deduction successful for item " << (i+1) << endl;
                    break;
                }
                
                retryCount++;
                if (retryCount >= maxRetries) {
                    cout << "Maximum retries exceeded for item " << (i+1) << endl;
                    bOk = false;
                    break;
                }
            }
            
            if (!bOk) {
                break;
            }
        }
        
        cout << "\n=== Group Ticket Deduction Complete ===" << endl;
        cout << "Final purse value: " << csc->purseValue.Format() << endl;
        cout << "Result: " << (bOk ? "SUCCESS" : "FAILED") << endl;
        
        return bOk;
    }
    
    // Calculate total fare for all items
    CCurrency GetTotalFare() {
        CCurrency total(0, 0);
        for (const auto& item : mItems) {
            total = total + item.GetItemSubtotal();
        }
        return total;
    }
    
    // Clear all items
    void ClearItems() {
        mItems.clear();
        cout << "All ticket items cleared." << endl;
    }
    
    // Display current items
    void DisplayItems() {
        cout << "\n=== Current Ticket Items ===" << endl;
        if (mItems.empty()) {
            cout << "No items in the list." << endl;
            return;
        }
        
        for (size_t i = 0; i < mItems.size(); i++) {
            const auto& item = mItems[i];
            cout << "Item " << (i+1) << ": "
                 << "ID=" << item.GetItemId()
                 << ", Deluxe=" << (item.GetDeluxeTicket() ? "Yes" : "No")
                 << ", Qty=" << item.GetItemQty()
                 << ", Price=" << item.GetItemPrice().Format()
                 << ", Subtotal=" << item.GetItemSubtotal().Format() << endl;
        }
        cout << "Total fare: " << GetTotalFare().Format() << endl;
    }
};

// Initialize mock CSC with test data
void InitializeMockCSC() {
    g_mockCSC.purseValue = CCurrency(500, 0);  // $50.00
    g_mockCSC.negativeValueLimit = CCurrency(350, 0);  // $35.00 negative limit
    g_mockCSC.physicalId = 12345678;
    g_mockCSC.isAutopayEnabled = false;
    g_mockCSC.autopayAmount = CCurrency(0, 0);
    g_mockCSC.fIncomplete = FALSE;
    g_mockCSC.alertCode = 0;
    memset(g_mockCSC.deductUDSignature, 0, 4);
    memset(g_mockCSC.atpUDSignature, 0, 4);
    
    cout << "Mock CSC initialized:" << endl;
    cout << "  Purse value: " << g_mockCSC.purseValue.Format() << endl;
    cout << "  Negative limit: " << g_mockCSC.negativeValueLimit.Format() << endl;
    cout << "  Physical ID: " << g_mockCSC.physicalId << endl;
}

// Test scenarios
void RunTestScenarios() {
    GroupTicketDeductionSimulator simulator;
    
    cout << "\n========================================" << endl;
    cout << "Group Ticket Deduction Test Scenarios" << endl;
    cout << "========================================" << endl;
    
    // Test Scenario 1: Normal group ticket with sufficient funds
    cout << "\n--- Test Scenario 1: Normal Group Ticket (Sufficient Funds) ---" << endl;
    simulator.AddTicketItem(1, false, 2, CCurrency(15, 0));  // 2 ordinary tickets @ $1.50 each
    simulator.AddTicketItem(2, true, 1, CCurrency(25, 0));   // 1 deluxe ticket @ $2.50
    simulator.DisplayItems();
    
    bool result1 = simulator.DeductGroupTicketsByItem();
    cout << "Scenario 1 result: " << (result1 ? "PASS" : "FAIL") << endl;
    
    simulator.ClearItems();
    
    // Test Scenario 2: Group ticket with insufficient funds
    cout << "\n--- Test Scenario 2: Group Ticket (Insufficient Funds) ---" << endl;
    simulator.AddTicketItem(3, false, 10, CCurrency(60, 0));  // 10 tickets @ $6.00 each = $60.00
    simulator.DisplayItems();
    
    bool result2 = simulator.DeductGroupTicketsByItem();
    cout << "Scenario 2 result: " << (result2 ? "PASS (unexpected)" : "FAIL (expected)") << endl;
    
    simulator.ClearItems();
    
    // Test Scenario 3: Mixed group with edge case (using negative limit)
    cout << "\n--- Test Scenario 3: Mixed Group (Using Negative Limit) ---" << endl;
    // Reset CSC to lower amount to test negative limit usage
    g_mockCSC.purseValue = CCurrency(10, 0);  // $1.00

    simulator.AddTicketItem(4, false, 1, CCurrency(30, 0));  // 1 ticket @ $3.00
    simulator.AddTicketItem(5, true, 1, CCurrency(15, 0));   // 1 deluxe @ $1.50
    simulator.DisplayItems();

    bool result3 = simulator.DeductGroupTicketsByItem();
    cout << "Scenario 3 result: " << (result3 ? "PASS" : "FAIL") << endl;

    // Test Scenario 4: Single expensive item
    cout << "\n--- Test Scenario 4: Single Expensive Item ---" << endl;
    // Reset CSC for this test
    g_mockCSC.purseValue = CCurrency(200, 0);  // $20.00

    simulator.ClearItems();
    simulator.AddTicketItem(6, true, 1, CCurrency(150, 0));  // 1 expensive deluxe @ $15.00
    simulator.DisplayItems();

    bool result4 = simulator.DeductGroupTicketsByItem();
    cout << "Scenario 4 result: " << (result4 ? "PASS" : "FAIL") << endl;

    // Display summary
    cout << "\n========================================" << endl;
    cout << "Test Summary:" << endl;
    cout << "  Scenario 1 (Sufficient Funds): " << (result1 ? "PASS" : "FAIL") << endl;
    cout << "  Scenario 2 (Insufficient Funds): " << (result2 ? "FAIL (expected)" : "PASS (expected)") << endl;
    cout << "  Scenario 3 (Negative Limit): " << (result3 ? "PASS" : "FAIL") << endl;
    cout << "  Scenario 4 (Expensive Item): " << (result4 ? "PASS" : "FAIL") << endl;
    cout << "========================================" << endl;
}

// Interactive test mode
void RunInteractiveMode() {
    GroupTicketDeductionSimulator simulator;
    char choice;

    cout << "\n=== Interactive Test Mode ===" << endl;
    cout << "Current CSC Status:" << endl;
    cout << "  Purse value: " << g_mockCSC.purseValue.Format() << endl;
    cout << "  Negative limit: " << g_mockCSC.negativeValueLimit.Format() << endl;

    do {
        cout << "\nOptions:" << endl;
        cout << "  1. Add ordinary ticket" << endl;
        cout << "  2. Add deluxe ticket" << endl;
        cout << "  3. Display current items" << endl;
        cout << "  4. Process deductions" << endl;
        cout << "  5. Clear all items" << endl;
        cout << "  6. Reset CSC" << endl;
        cout << "  q. Quit interactive mode" << endl;
        cout << "Enter choice: ";

        cin >> choice;

        switch (choice) {
        case '1': {
            int qty;
            double price;
            cout << "Enter quantity: ";
            cin >> qty;
            cout << "Enter price (dollars): ";
            cin >> price;
            simulator.AddTicketItem(1, false, qty, CCurrency((long)(price * 10), 0));
            break;
        }
        case '2': {
            int qty;
            double price;
            cout << "Enter quantity: ";
            cin >> qty;
            cout << "Enter price (dollars): ";
            cin >> price;
            simulator.AddTicketItem(2, true, qty, CCurrency((long)(price * 10), 0));
            break;
        }
        case '3':
            simulator.DisplayItems();
            break;
        case '4': {
            bool result = simulator.DeductGroupTicketsByItem();
            cout << "Deduction result: " << (result ? "SUCCESS" : "FAILED") << endl;
            break;
        }
        case '5':
            simulator.ClearItems();
            break;
        case '6':
            InitializeMockCSC();
            cout << "CSC reset to default values." << endl;
            break;
        case 'q':
        case 'Q':
            cout << "Exiting interactive mode." << endl;
            break;
        default:
            cout << "Invalid choice. Please try again." << endl;
            break;
        }
    } while (choice != 'q' && choice != 'Q');
}

int main() {
    cout << "Group Ticket Deduction Test Program" << endl;
    cout << "===================================" << endl;

    // Initialize the mock CSC
    InitializeMockCSC();

    cout << "\nSelect test mode:" << endl;
    cout << "  1. Run automated test scenarios" << endl;
    cout << "  2. Interactive test mode" << endl;
    cout << "Enter choice (1 or 2): ";

    char mode;
    cin >> mode;

    if (mode == '1') {
        // Run automated test scenarios
        RunTestScenarios();
    } else if (mode == '2') {
        // Run interactive mode
        RunInteractiveMode();
    } else {
        cout << "Invalid choice. Running automated tests by default." << endl;
        RunTestScenarios();
    }

    cout << "\nTest program completed. Press Enter to exit..." << endl;
    cin.ignore();
    cin.get();

    return 0;
}
