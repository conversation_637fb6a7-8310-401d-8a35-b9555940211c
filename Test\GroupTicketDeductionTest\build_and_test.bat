@echo off
REM Build and Test Script for Group Ticket Deduction Test Program
REM This script builds the test program and optionally runs it

echo ========================================
echo Group Ticket Deduction Test Builder
echo ========================================

REM Check if Visual Studio environment is set up
if "%VCINSTALLDIR%"=="" (
    echo ERROR: Visual Studio environment not detected.
    echo Please run this script from a Visual Studio Command Prompt.
    echo Or run vcvars32.bat first to set up the environment.
    pause
    exit /b 1
)

REM Set up paths
set PROJECT_DIR=%~dp0
set ROOT_DIR=%PROJECT_DIR%..\..\

echo Project Directory: %PROJECT_DIR%
echo Root Directory: %ROOT_DIR%

REM Check if required directories exist
if not exist "%ROOT_DIR%INC" (
    echo ERROR: INC directory not found at %ROOT_DIR%INC
    echo Please ensure you are running from the correct location.
    pause
    exit /b 1
)

if not exist "%ROOT_DIR%OCP" (
    echo ERROR: OCP directory not found at %ROOT_DIR%OCP
    echo Please ensure you are running from the correct location.
    pause
    exit /b 1
)

echo.
echo Building test program...
echo.

REM Clean previous build
if exist "*.obj" del /Q *.obj
if exist "*.pdb" del /Q *.pdb
if exist "*.ilk" del /Q *.ilk
if exist "GroupTicketDeductionTest.exe" del /Q GroupTicketDeductionTest.exe

REM Compile source files
echo Compiling stdafx.cpp...
cl.exe /nologo /MTd /W3 /Gm /GX /ZI /Od ^
    /I "%ROOT_DIR%INC" /I "%ROOT_DIR%OCP" /I "%ROOT_DIR%utils" /I "%ROOT_DIR%eod" /I "%ROOT_DIR%csc" ^
    /D "WIN32" /D "_DEBUG" /D "_CONSOLE" /D "_MBCS" /D "_AFXDLL" ^
    /c stdafx.cpp

if errorlevel 1 (
    echo ERROR: Failed to compile stdafx.cpp
    pause
    exit /b 1
)

echo Compiling GroupTicketDeductionTest.cpp...
cl.exe /nologo /MTd /W3 /Gm /GX /ZI /Od ^
    /I "%ROOT_DIR%INC" /I "%ROOT_DIR%OCP" /I "%ROOT_DIR%utils" /I "%ROOT_DIR%eod" /I "%ROOT_DIR%csc" ^
    /D "WIN32" /D "_DEBUG" /D "_CONSOLE" /D "_MBCS" /D "_AFXDLL" ^
    /c GroupTicketDeductionTest.cpp

if errorlevel 1 (
    echo ERROR: Failed to compile GroupTicketDeductionTest.cpp
    pause
    exit /b 1
)

REM Link the executable
echo Linking executable...
link.exe /nologo /subsystem:console /debug /machine:I386 /pdbtype:sept ^
    /libpath:"%ROOT_DIR%utils\Debug" /libpath:"%ROOT_DIR%eod\Debug" /libpath:"%ROOT_DIR%csc\Debug" /libpath:"%ROOT_DIR%OCP\Debug" ^
    /out:GroupTicketDeductionTest.exe ^
    stdafx.obj GroupTicketDeductionTest.obj ^
    kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib

if errorlevel 1 (
    echo ERROR: Failed to link executable
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo.

REM Ask user if they want to run the test
set /p RUN_TEST="Do you want to run the test program now? (y/n): "
if /i "%RUN_TEST%"=="y" (
    echo.
    echo Running test program...
    echo ========================================
    GroupTicketDeductionTest.exe
    echo ========================================
    echo Test program completed.
) else (
    echo.
    echo Test program built successfully.
    echo Run 'GroupTicketDeductionTest.exe' to execute the tests.
)

echo.
pause
