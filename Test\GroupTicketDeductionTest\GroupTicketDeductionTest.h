// GroupTicketDeductionTest.h : Header file for Group Ticket Deduction Test Program
//
// This header defines the interface and constants for testing group ticket
// deductions using Octopus CSC simulation.

#pragma once

#include "stdafx.h"
#include "Currency.h"
#include "PosItem.h"
#include <vector>

// Error codes for testing (matching existing OCP error codes)
#ifndef OCPERROR_INSUFFICIENT_FUNDS
#define OCPERROR_INSUFFICIENT_FUNDS     0x80040001L
#endif

#ifndef ERROR_SUCCESS
#define ERROR_SUCCESS                   0L
#endif

// Forward declarations
class GroupTicketDeductionSimulator;
struct MockCSCData;

// Mock CSC data structure for simulation
struct MockCSCData {
    CCurrency purseValue;           // Current purse value
    CCurrency negativeValueLimit;   // Negative value limit
    ULONG physicalId;              // CSC physical ID
    bool isAutopayEnabled;         // Autopay status
    CCurrency autopayAmount;       // Autopay amount
    bool fIncomplete;              // Transaction incomplete flag
    BYTE alertCode;                // Alert code
    BYTE deductUDSignature[4];     // Deduct UD signature
    BYTE atpUDSignature[4];        // ATP UD signature
};

// Test configuration constants
namespace TestConfig {
    const CCurrency DEFAULT_PURSE_VALUE(500, 0);        // $50.00
    const CCurrency DEFAULT_NEG_LIMIT(350, 0);          // $35.00
    const ULONG DEFAULT_PHYSICAL_ID = 12345678;
    const int MAX_RETRY_ATTEMPTS = 3;
    
    // Test ticket prices (in 10 cents)
    const CCurrency ORDINARY_TICKET_PRICE(15, 0);       // $1.50
    const CCurrency DELUXE_TICKET_PRICE(25, 0);         // $2.50
    const CCurrency EXPENSIVE_TICKET_PRICE(60, 0);      // $6.00
}

// Test scenario definitions
enum TestScenario {
    SCENARIO_SUFFICIENT_FUNDS = 1,
    SCENARIO_INSUFFICIENT_FUNDS = 2,
    SCENARIO_NEGATIVE_LIMIT_USAGE = 3,
    SCENARIO_MIXED_GROUP = 4,
    SCENARIO_SINGLE_EXPENSIVE_ITEM = 5
};

// Mock function declarations
DWORD MockOcpDeduct(CCurrency ccyAmount, BYTE* fIncomplete, BYTE* pATPAmount, 
                   BYTE* DeductUDSignature, BYTE* ATPUDSignature, BYTE* pAlertCode, 
                   bool bAutopayTriggered, transact_t* pDetails = NULL);

// Test utility functions
void InitializeMockCSC();
void RunTestScenarios();
void DisplayTestHeader();
void DisplayTestResults(const std::vector<bool>& results);

// Group Ticket Deduction Simulator Class
class GroupTicketDeductionSimulator {
private:
    std::vector<CPosTicketItem> mItems;
    MockCSCData* csc;
    
public:
    // Constructor
    GroupTicketDeductionSimulator();
    
    // Destructor
    ~GroupTicketDeductionSimulator() = default;
    
    // Item management methods
    void AddTicketItem(int itemId, bool isDeluxe, int quantity, CCurrency price);
    void ClearItems();
    void DisplayItems();
    
    // Deduction simulation methods
    bool DeductGroupTicketsByItem(bool groupSingleTicket = false);
    bool SimulateItemDeduction(const CPosTicketItem& item, int itemIndex);
    
    // Utility methods
    CCurrency GetTotalFare();
    size_t GetItemCount() const { return mItems.size(); }
    const CPosTicketItem& GetItem(size_t index) const { return mItems[index]; }
    
    // CSC state methods
    CCurrency GetCurrentPurseValue() const;
    CCurrency GetAvailableFunds() const;
    void SetPurseValue(CCurrency value);
    void SetNegativeLimit(CCurrency limit);
    
    // Test scenario methods
    bool RunScenario(TestScenario scenario);
    void PrepareScenario(TestScenario scenario);
};

// Test result structure
struct TestResult {
    TestScenario scenario;
    bool passed;
    std::string description;
    CCurrency initialPurse;
    CCurrency finalPurse;
    CCurrency totalDeducted;
    int itemsProcessed;
    std::string errorMessage;
};

// Test runner class
class GroupTicketTestRunner {
private:
    std::vector<TestResult> results;
    GroupTicketDeductionSimulator simulator;
    
public:
    // Run all test scenarios
    void RunAllTests();
    
    // Run specific test
    bool RunSingleTest(TestScenario scenario);
    
    // Display results
    void DisplayResults();
    void DisplaySummary();
    
    // Utility methods
    void ResetCSC();
    bool ValidateTestEnvironment();
};

// Global test data
extern MockCSCData g_mockCSC;

// Test macros for assertions
#define TEST_ASSERT(condition, message) \
    do { \
        if (!(condition)) { \
            std::cout << "ASSERTION FAILED: " << message << std::endl; \
            return false; \
        } \
    } while(0)

#define TEST_LOG(message) \
    std::cout << "[TEST] " << message << std::endl

#define TEST_ERROR(message) \
    std::cout << "[ERROR] " << message << std::endl

#define TEST_SUCCESS(message) \
    std::cout << "[SUCCESS] " << message << std::endl

// Test data generators
namespace TestDataGenerator {
    // Generate test ticket items
    std::vector<CPosTicketItem> GenerateOrdinaryTickets(int count, CCurrency price);
    std::vector<CPosTicketItem> GenerateDeluxeTickets(int count, CCurrency price);
    std::vector<CPosTicketItem> GenerateMixedTickets();
    
    // Generate test scenarios
    void SetupSufficientFundsScenario(GroupTicketDeductionSimulator& sim);
    void SetupInsufficientFundsScenario(GroupTicketDeductionSimulator& sim);
    void SetupNegativeLimitScenario(GroupTicketDeductionSimulator& sim);
}

#endif // GROUPTICKETDEDUCTIONTEST_H
