﻿Build started 2/7/2025 2:51:28 pm.
     1>Project "D:\GIT_CLONE\ocp_nwff_upgrade\NwfbLoyaltyScheme\NwfbLoyaltyScheme.vcxproj" on node 2 (build target(s)).
     1>InitializeBuildStatus:
         Creating ".\Release\NwfbLoyaltyScheme.unsuccessfulbuild" because "AlwaysCreate" was specified.
       ClCompile:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I..\ocp /I..\inc /Zi /nologo /W3 /WX- /O2 /Ob1 /Oy- /D WIN32 /D NDEBUG /D _WINDOWS /D _AFXDLL /D _USE_32BIT_TIME_T /D _WIN32_WINNT=0x0501 /D UNICODE /D _UNICODE /GF /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Fo".\Release\\" /Fd".\Release\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt LoyaltySchemeReportDlg.cpp NwfbLoyaltySchemeDlg.cpp NwfbLoyaltySchemeInfo.cpp NwfbLoyaltySchemeRedemptionLogDlg.cpp RedemptionQtyDlg.cpp
         LoyaltySchemeReportDlg.cpp
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(255): warning C4005: 'MSG_UNCONF_TRANSACTION_CSC' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(666) : see previous definition of 'MSG_UNCONF_TRANSACTION_CSC'
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(264): warning C4005: 'MSG_UNCONF_AUTOPAY' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(674) : see previous definition of 'MSG_UNCONF_AUTOPAY'
         NwfbLoyaltySchemeDlg.cpp
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(255): warning C4005: 'MSG_UNCONF_TRANSACTION_CSC' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(666) : see previous definition of 'MSG_UNCONF_TRANSACTION_CSC'
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(264): warning C4005: 'MSG_UNCONF_AUTOPAY' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(674) : see previous definition of 'MSG_UNCONF_AUTOPAY'
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\NwfbLoyaltySchemeRedemptionLogDlg.h(52): warning C4995: 'CDaoDatabase': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\NwfbLoyaltySchemeRedemptionLogDlg.h(53): warning C4995: 'CDaoWorkspace': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\NwfbLoyaltySchemeRedemptionLogDlg.h(54): warning C4995: 'CDaoTableDef': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\NwfbLoyaltySchemeRedemptionLogDlg.h(55): warning C4995: 'CDaoRecordset': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\NwfbLoyaltySchemeRedemptionLogDlg.h(56): warning C4995: 'CDaoQueryDef': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\..\NwfbLoyaltyScheme\NwfbLoyaltySchemeDlg.h(103): warning C4995: 'CDaoDatabase': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\..\NwfbLoyaltyScheme\NwfbLoyaltySchemeDlg.h(104): warning C4995: 'CDaoWorkspace': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\..\NwfbLoyaltyScheme\NwfbLoyaltySchemeDlg.h(105): warning C4995: 'CDaoTableDef': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\..\NwfbLoyaltyScheme\NwfbLoyaltySchemeDlg.h(106): warning C4995: 'CDaoRecordset': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\..\NwfbLoyaltyScheme\NwfbLoyaltySchemeDlg.h(107): warning C4995: 'CDaoQueryDef': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(158): warning C4995: 'CDaoWorkspace': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(176): warning C4995: 'CDaoDatabase': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(199): warning C4995: 'CDaoQueryDef': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(208): warning C4995: 'CDaoException': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(214): warning C4995: 'CDaoRecordset': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(241): warning C4995: 'CDaoException': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(262): warning C4995: 'CDaoTableDef': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(268): warning C4995: 'CDaoException': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(286): warning C4995: 'CDaoException': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(295): warning C4995: 'CDaoException': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(304): warning C4995: 'CDaoException': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(312): warning C4995: 'CDaoTableDef': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(317): warning C4995: 'CDaoException': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(331): warning C4995: 'CDaoException': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(340): warning C4995: 'CDaoException': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(349): warning C4995: 'CDaoException': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(377): warning C4244: '=' : conversion from '__int64' to 'long', possible loss of data
     1>NwfbLoyaltySchemeDlg.cpp(512): warning C4995: 'CDaoTableDef': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(515): warning C4995: 'CDaoException': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(521): warning C4995: 'CDaoRecordset': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(527): warning C4995: 'CDaoException': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(556): warning C4995: 'CDaoException': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(580): warning C4995: 'CDaoException': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(587): warning C4995: 'CDaoException': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(838): warning C4995: 'CDaoTableDef': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(841): warning C4995: 'CDaoException': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(848): warning C4995: 'CDaoRecordset': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(853): warning C4995: 'CDaoException': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(895): warning C4995: 'CDaoException': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeDlg.cpp(912): warning C4995: 'CDaoException': name was marked as #pragma deprecated
         NwfbLoyaltySchemeInfo.cpp
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(255): warning C4005: 'MSG_UNCONF_TRANSACTION_CSC' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(666) : see previous definition of 'MSG_UNCONF_TRANSACTION_CSC'
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(264): warning C4005: 'MSG_UNCONF_AUTOPAY' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(674) : see previous definition of 'MSG_UNCONF_AUTOPAY'
         NwfbLoyaltySchemeRedemptionLogDlg.cpp
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(255): warning C4005: 'MSG_UNCONF_TRANSACTION_CSC' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(666) : see previous definition of 'MSG_UNCONF_TRANSACTION_CSC'
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(264): warning C4005: 'MSG_UNCONF_AUTOPAY' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(674) : see previous definition of 'MSG_UNCONF_AUTOPAY'
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\NwfbLoyaltySchemeRedemptionLogDlg.h(52): warning C4995: 'CDaoDatabase': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\NwfbLoyaltySchemeRedemptionLogDlg.h(53): warning C4995: 'CDaoWorkspace': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\NwfbLoyaltySchemeRedemptionLogDlg.h(54): warning C4995: 'CDaoTableDef': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\NwfbLoyaltySchemeRedemptionLogDlg.h(55): warning C4995: 'CDaoRecordset': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\NwfbLoyaltySchemeRedemptionLogDlg.h(56): warning C4995: 'CDaoQueryDef': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeRedemptionLogDlg.cpp(101): warning C4995: 'CDaoWorkspace': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeRedemptionLogDlg.cpp(104): warning C4995: 'CDaoException': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeRedemptionLogDlg.cpp(118): warning C4995: 'CDaoDatabase': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeRedemptionLogDlg.cpp(120): warning C4995: 'CDaoException': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeRedemptionLogDlg.cpp(136): warning C4995: 'CDaoException': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeRedemptionLogDlg.cpp(148): warning C4995: 'CDaoQueryDef': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeRedemptionLogDlg.cpp(163): warning C4995: 'CDaoRecordset': name was marked as #pragma deprecated
     1>NwfbLoyaltySchemeRedemptionLogDlg.cpp(212): warning C4995: 'CDaoException': name was marked as #pragma deprecated
         RedemptionQtyDlg.cpp
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(255): warning C4005: 'MSG_UNCONF_TRANSACTION_CSC' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(666) : see previous definition of 'MSG_UNCONF_TRANSACTION_CSC'
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(264): warning C4005: 'MSG_UNCONF_AUTOPAY' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(674) : see previous definition of 'MSG_UNCONF_AUTOPAY'
         Generating Code...
       Lib:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\Lib.exe /OUT:".\Release\NwfbLoyaltyScheme.lib" /NOLOGO .\Release\LoyaltySchemeReportDlg.obj
         .\Release\NwfbLoyaltySchemeDlg.obj
         .\Release\NwfbLoyaltySchemeInfo.obj
         .\Release\NwfbLoyaltySchemeRedemptionLogDlg.obj
         .\Release\RedemptionQtyDlg.obj
         NwfbLoyaltyScheme.vcxproj -> D:\GIT_CLONE\ocp_nwff_upgrade\NwfbLoyaltyScheme\.\Release\NwfbLoyaltyScheme.lib
       FinalizeBuildStatus:
         Deleting file ".\Release\NwfbLoyaltyScheme.unsuccessfulbuild".
         Touching ".\Release\NwfbLoyaltyScheme.lastbuildstate".
     1>Done Building Project "D:\GIT_CLONE\ocp_nwff_upgrade\NwfbLoyaltyScheme\NwfbLoyaltyScheme.vcxproj" (build target(s)).

Build succeeded.

Time Elapsed 00:00:11.48
