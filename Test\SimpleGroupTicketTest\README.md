# Simple Group Ticket Test

## Overview

This is a minimal test program that demonstrates group ticket deductions with clear debug output to the console. It focuses on showing the step-by-step process of deducting multiple tickets from an Octopus card.

## Purpose

- Show detailed debug logs for each deduction step
- Demonstrate the group ticket processing flow
- Test both successful and failed deduction scenarios
- Provide clear, easy-to-read console output

## Features

### Debug Logging
The program shows detailed information for each step:
- Current purse value and negative limit
- Item details (ID, quantity, price, deluxe status)
- Available funds calculation
- Deduction attempt results
- Updated purse values

### Test Scenarios
1. **Successful Group Processing**: Multiple tickets with sufficient funds
2. **Insufficient Funds**: Demonstrates failure when funds run out

## Sample Output

```
========================================
SIMPLE GROUP TICKET DEDUCTION TEST
========================================

Simple Group Ticket Deduction Test
==================================
CSC Initialized:
  Purse value: $50.00
  Negative limit: $35.00

========================================
STARTING GROUP TICKET PROCESSING
========================================
Total items: 3
Initial CSC state:
  Purse: $50.00
  Negative limit: $35.00
Total fare for all items: $9.60

>>> Processing item 1 of 3 <<<
Item details:
  ID: 1
  Quantity: 2
  Unit price: $1.50
  Subtotal: $3.00
  Deluxe: No

--- DEDUCTION ATTEMPT ---
Item: Item 1 (ID:1, Qty:2, Ordinary)
Amount to deduct: $3.00
Current purse: $50.00
Negative limit: $35.00
Available funds: $85.00
RESULT: SUCCESS
New purse value: $47.00
------------------------

>>> Processing item 2 of 3 <<<
Item details:
  ID: 2
  Quantity: 1
  Unit price: $2.50
  Subtotal: $2.50
  Deluxe: Yes

--- DEDUCTION ATTEMPT ---
Item: Item 2 (ID:2, Qty:1, Deluxe)
Amount to deduct: $2.50
Current purse: $47.00
Negative limit: $35.00
Available funds: $82.00
RESULT: SUCCESS
New purse value: $44.50
------------------------

>>> Processing item 3 of 3 <<<
Item details:
  ID: 3
  Quantity: 3
  Unit price: $1.20
  Subtotal: $3.60
  Deluxe: No

--- DEDUCTION ATTEMPT ---
Item: Item 3 (ID:3, Qty:3, Ordinary)
Amount to deduct: $3.60
Current purse: $44.50
Negative limit: $35.00
Available funds: $79.50
RESULT: SUCCESS
New purse value: $40.90
------------------------

========================================
GROUP TICKET PROCESSING COMPLETED
========================================
All 3 items processed successfully
Final purse value: $40.90

=== FINAL RESULT ===
Test result: PASSED
```

## Building and Running

### Option 1: Visual Studio 2010
1. Open `SimpleGroupTicketTest.vcxproj` in Visual Studio 2010
2. Build and run (F5)

### Option 2: Command Line
1. Run `build.bat` from a Visual Studio Command Prompt
2. Choose 'y' when prompted to run the test

### Option 3: MSBuild
```batch
msbuild SimpleGroupTicketTest.vcxproj /p:Configuration=Debug
SimpleGroupTicketTest.exe
```

## Code Structure

The program is intentionally simple:

- **SimpleCSC struct**: Minimal CSC state (purse, negative limit)
- **MockDeduct()**: Simple deduction function with debug output
- **ProcessGroupTickets()**: Main group processing logic
- **Two test scenarios**: Success and failure cases

## Key Debug Information

For each deduction attempt, you'll see:
1. Item details (ID, quantity, price, deluxe status)
2. Current financial state (purse value, negative limit)
3. Available funds calculation
4. Deduction result (success/failure)
5. Updated purse value

This makes it easy to understand exactly what happens during group ticket processing and where failures occur.

## Dependencies

- OCP Currency class
- OCP PosItem class
- Standard C++ libraries
- MFC (minimal usage)

The program uses the same data structures as the real OCP system but with simplified mock functions for easy testing and debugging.
