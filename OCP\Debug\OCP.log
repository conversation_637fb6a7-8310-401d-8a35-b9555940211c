﻿Build started 3/7/2025 11:09:27 am.
     1>Project "D:\GIT_CLONE\ocp_nwff_upgrade\OCP\OCP.vcxproj" on node 8 (rebuild target(s)).
     1>_PrepareForClean:
         Deleting file "Debug\OCP.lastbuildstate".
       InitializeBuildStatus:
         Creating "Debug\OCP.unsuccessfulbuild" because "AlwaysCreate" was specified.
       ClCompile:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I.\ocp /I..\inc /I..\zip\debug /ZI /nologo /W3 /WX- /Od /Oy- /D WIN32 /D _DEBUG /D _WINDOWS /D _AFXDLL /D _USE_32BIT_TIME_T /D _WIN32_WINNT=0x0501 /D UNICODE /D _UNICODE /Gm- /EHsc /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Yc"stdafx.h" /Fp".\Debug\OCP.pch" /Fo".\Debug\\" /Fd".\Debug\vc100.pdb" /FR"Debug\\" /Gd /TP /analyze- /errorReport:prompt StdAfx.cpp
         StdAfx.cpp
     1>d:\git_clone\ocp_nwff_upgrade\inc\toastype.h(255): warning C4005: 'MSG_UNCONF_TRANSACTION_CSC' : macro redefinition
                 d:\git_clone\ocp_nwff_upgrade\inc\udmf.h(666) : see previous definition of 'MSG_UNCONF_TRANSACTION_CSC'
     1>d:\git_clone\ocp_nwff_upgrade\inc\toastype.h(264): warning C4005: 'MSG_UNCONF_AUTOPAY' : macro redefinition
                 d:\git_clone\ocp_nwff_upgrade\inc\udmf.h(674) : see previous definition of 'MSG_UNCONF_AUTOPAY'
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I.\ocp /I..\inc /I..\zip\debug /ZI /nologo /W3 /WX- /Od /Oy- /D WIN32 /D _DEBUG /D _WINDOWS /D _AFXDLL /D _USE_32BIT_TIME_T /D _WIN32_WINNT=0x0501 /D UNICODE /D _UNICODE /Gm- /EHsc /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Yu"stdafx.h" /Fp".\Debug\OCP.pch" /Fo".\Debug\\" /Fd".\Debug\vc100.pdb" /FR"Debug\\" /Gd /TP /analyze- /errorReport:prompt addbonus.cpp AdminDlg.cpp AelDlg.cpp AntiPass.cpp BackScrn.cpp Badcssd.cpp bcscsdrd.cpp BlocBitD.cpp Caddvald.cpp Cantipbd.cpp CardReplacementReaderPrompt.cpp Cbmain.cpp cbpfare.cpp Cbsroute.cpp CBStatD.cpp CBulkDlg.cpp CCbAppD.cpp CCHSDLOG.CPP CCHSPROC.CPP CCHSUI.CPP CColorButton.cpp Ccrdinfd.cpp CCscOp.cpp Cgendatd.cpp CHyfAppD.cpp CInitErD.cpp CInitReD.cpp Cinvcrdd.cpp Ckcrchad.cpp Ckcrclad.cpp CKmbAppD.cpp CLdMapD.cpp Cmaind.cpp Cmtrcad.cpp Cnegremd.cpp CNwbAppD.cpp Coms_ldp.cpp Concessd.cpp Cperdatd.cpp CRepPrn.cpp CSCard.cpp CSCChk.cpp CscDiagD.cpp CscEditP.cpp CSCIM.cpp CscIssID.cpp CSCPIN.CPP Cscprefd.cpp CSCrdGen.cpp CscReadPin.cpp CSetDutD.cpp Cshfrepd.cpp Cshfstd.cpp CshOutFl.cpp Ctllist.cpp CTmcsComms.cpp CTmcsPacket.cpp Ctmeexpd.cpp Ctrnlogd.cpp Cvldexpd.cpp DailyDlg.cpp DiaPollD.cpp DiskPrn.cpp Duty.cpp DutyEDlg.cpp Entitled.cpp EODVerDlg.cpp EvLogDlg.cpp ExcessD.cpp FreightCharge.cpp Gcscsdrd.cpp getcscid.cpp HrtReceiptNumber.cpp HyfAppCrDlg.cpp HyfCrtUpgrade.cpp HyfCscDpCancelDialog.cpp HyfCscHrtCancelDialog.cpp HyfFreightPosDialog.cpp HyfIssTicket.cpp HyfMonthlyPassCancel.cpp Hyfpsale.cpp HyfQtDlg.cpp HyfSailingInfoDialog.cpp HyfSales.cpp Hyfsflot.cpp HyfTInfo.cpp HyfTransactPosDialog.cpp HyfTrip.cpp KceBPRed.cpp LARmain.cpp ListHdr.cpp Log.cpp LogEntry.cpp LogEvt.cpp LogHdr.cpp LrtBonPt.cpp LWIssDlg.cpp MainScrn.cpp NCSCPick.cpp NonCSCRe.cpp NonCSCTk.cpp OCP.cpp OCPCscOpsDialog.cpp OCPDlg.cpp OCPExcep.cpp OcpHyfMonthlyPassReIssueSel.cpp Ocpmsgbx.cpp OcpPosConfirmTxnDialog.cpp OCPPosDlg.cpp OCPPropertySheet.cpp OCPPropertyPage.cpp OCPUtil.cpp OffLoad.cpp Pd.cpp PHyfPTrp.cpp PicklstD.cpp PLarProc.cpp PMthPass.cpp PosItem.cpp PostHrtPrn.cpp PostHrtReceipt.cpp posthyf.cpp PostMain.cpp Postprn.cpp PrntTLFD.cpp Ptlftran.cpp PurseExD.cpp Redoavd.cpp RefndDlg.cpp Retain.cpp RetentED.cpp Serialcom.cpp Shift.cpp Shroff.cpp Souvenir.cpp SSIGNOND.CPP Stock.cpp StockDlg.cpp StudentConcessionDailog.cpp StudentMonthlyPassDlg.cpp Summary.cpp Supplem.cpp Surcharg.cpp surrendr.cpp ToasComs.cpp Transact.cpp uploadalt.cpp XMessageBox.cpp
         addbonus.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\addbonus.cpp(351): warning C4244: '=' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\addbonus.cpp(359): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\addbonus.cpp(371): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
         AdminDlg.cpp
         AelDlg.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         AntiPass.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         BackScrn.cpp
         Badcssd.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         bcscsdrd.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         BlocBitD.cpp
     1>d:\git_clone\ocp_nwff_upgrade\inc\sebasetp.h(66): warning C4005: 'Time_t' : macro redefinition
                 d:\git_clone\ocp_nwff_upgrade\inc\udmf.h(941) : see previous definition of 'Time_t'
     1>d:\git_clone\ocp_nwff_upgrade\inc\sebasetp.h(71): warning C4005: 'ValueTenCents_t' : macro redefinition
                 d:\git_clone\ocp_nwff_upgrade\inc\udmf.h(973) : see previous definition of 'ValueTenCents_t'
     1>d:\git_clone\ocp_nwff_upgrade\ocp\blocbitd.cpp(180): warning C4244: '=' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\blocbitd.cpp(226): warning C4244: '=' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\blocbitd.cpp(432): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\blocbitd.cpp(563): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
         Caddvald.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         Cantipbd.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         CardReplacementReaderPrompt.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         Cbmain.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         cbpfare.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cbpfare.cpp(236): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cbpfare.cpp(561): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
         Cbsroute.cpp
         CBStatD.cpp
         CBulkDlg.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         CCbAppD.cpp
         CCHSDLOG.CPP
         CCHSPROC.CPP
     1>d:\git_clone\ocp_nwff_upgrade\inc\sebasetp.h(66): warning C4005: 'Time_t' : macro redefinition
                 d:\git_clone\ocp_nwff_upgrade\inc\udmf.h(941) : see previous definition of 'Time_t'
     1>d:\git_clone\ocp_nwff_upgrade\inc\sebasetp.h(71): warning C4005: 'ValueTenCents_t' : macro redefinition
                 d:\git_clone\ocp_nwff_upgrade\inc\udmf.h(973) : see previous definition of 'ValueTenCents_t'
         CCHSUI.CPP
     1>d:\git_clone\ocp_nwff_upgrade\inc\sebasetp.h(66): warning C4005: 'Time_t' : macro redefinition
                 d:\git_clone\ocp_nwff_upgrade\inc\udmf.h(941) : see previous definition of 'Time_t'
     1>d:\git_clone\ocp_nwff_upgrade\inc\sebasetp.h(71): warning C4005: 'ValueTenCents_t' : macro redefinition
                 d:\git_clone\ocp_nwff_upgrade\inc\udmf.h(973) : see previous definition of 'ValueTenCents_t'
         Generating Code...
         Compiling...
         CColorButton.cpp
         Ccrdinfd.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\ccrdinfd.cpp(515): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\ccrdinfd.cpp(505): warning C4101: 'Info' : unreferenced local variable
     1>d:\git_clone\ocp_nwff_upgrade\ocp\ccrdinfd.cpp(570): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
         CCscOp.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\inc\sebasetp.h(66): warning C4005: 'Time_t' : macro redefinition
                 d:\git_clone\ocp_nwff_upgrade\inc\udmf.h(941) : see previous definition of 'Time_t'
     1>d:\git_clone\ocp_nwff_upgrade\inc\sebasetp.h(71): warning C4005: 'ValueTenCents_t' : macro redefinition
                 d:\git_clone\ocp_nwff_upgrade\inc\udmf.h(973) : see previous definition of 'ValueTenCents_t'
     1>d:\git_clone\ocp_nwff_upgrade\ocp\ccscop.cpp(420): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\ccscop.cpp(432): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\ccscop.cpp(594): warning C4244: '=' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\ccscop.cpp(1231): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\ccscop.cpp(1257): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
         Cgendatd.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         CHyfAppD.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         CInitErD.cpp
         CInitReD.cpp
         Cinvcrdd.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cinvcrdd.cpp(789): warning C4244: '=' : conversion from '__int64' to 'long', possible loss of data
         Ckcrchad.cpp
         Ckcrclad.cpp
         CKmbAppD.cpp
         CLdMapD.cpp
         Cmaind.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\inc\sebasetp.h(66): warning C4005: 'Time_t' : macro redefinition
                 d:\git_clone\ocp_nwff_upgrade\inc\udmf.h(941) : see previous definition of 'Time_t'
     1>d:\git_clone\ocp_nwff_upgrade\inc\sebasetp.h(71): warning C4005: 'ValueTenCents_t' : macro redefinition
                 d:\git_clone\ocp_nwff_upgrade\inc\udmf.h(973) : see previous definition of 'ValueTenCents_t'
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemeredemptionlogdlg.h(52): warning C4995: 'CDaoDatabase': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemeredemptionlogdlg.h(53): warning C4995: 'CDaoWorkspace': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemeredemptionlogdlg.h(54): warning C4995: 'CDaoTableDef': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemeredemptionlogdlg.h(55): warning C4995: 'CDaoRecordset': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemeredemptionlogdlg.h(56): warning C4995: 'CDaoQueryDef': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemedlg.h(103): warning C4995: 'CDaoDatabase': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemedlg.h(104): warning C4995: 'CDaoWorkspace': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemedlg.h(105): warning C4995: 'CDaoTableDef': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemedlg.h(106): warning C4995: 'CDaoRecordset': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemedlg.h(107): warning C4995: 'CDaoQueryDef': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cmaind.cpp(1483): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
         Cmtrcad.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         Cnegremd.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         CNwbAppD.cpp
         Coms_ldp.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\coms_ldp.cpp(670): warning C4996: '_wfopen': This function or variable may be unsafe. Consider using _wfopen_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 c:\program files (x86)\microsoft visual studio 10.0\vc\include\wchar.h(815) : see declaration of '_wfopen'
         Concessd.cpp
         Cperdatd.cpp
         CRepPrn.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\crepprn.cpp(309): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\crepprn.cpp(361): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\crepprn.cpp(409): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\crepprn.cpp(456): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
         Generating Code...
         Compiling...
         CSCard.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.cpp(517): warning C4244: 'initializing' : conversion from 'time_t' to 'SHORT', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.cpp(518): warning C4244: 'initializing' : conversion from 'time_t' to 'SHORT', possible loss of data
         CSCChk.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         CscDiagD.cpp
         CscEditP.cpp
         CSCIM.cpp
         CscIssID.cpp
         CSCPIN.CPP
         Cscprefd.cpp
     1>d:\git_clone\ocp_nwff_upgrade\inc\sebasetp.h(66): warning C4005: 'Time_t' : macro redefinition
                 d:\git_clone\ocp_nwff_upgrade\inc\udmf.h(941) : see previous definition of 'Time_t'
     1>d:\git_clone\ocp_nwff_upgrade\inc\sebasetp.h(71): warning C4005: 'ValueTenCents_t' : macro redefinition
                 d:\git_clone\ocp_nwff_upgrade\inc\udmf.h(973) : see previous definition of 'ValueTenCents_t'
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscprefd.cpp(245): warning C4244: '=' : conversion from '__int64' to 'unsigned long', possible loss of data
         CSCrdGen.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         CscReadPin.cpp
         CSetDutD.cpp
         Cshfrepd.cpp
         Cshfstd.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cshfstd.cpp(229): warning C4482: nonstandard extension used: enum 'TrainingModeCscType_t' used in qualified name
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cshfstd.cpp(382): warning C4482: nonstandard extension used: enum 'TrainingModeCscType_t' used in qualified name
         CshOutFl.cpp
         Ctllist.cpp
         CTmcsComms.cpp
         CTmcsPacket.cpp
         Ctmeexpd.cpp
         Ctrnlogd.cpp
     1>d:\git_clone\ocp_nwff_upgrade\inc\sebasetp.h(66): warning C4005: 'Time_t' : macro redefinition
                 d:\git_clone\ocp_nwff_upgrade\inc\udmf.h(941) : see previous definition of 'Time_t'
     1>d:\git_clone\ocp_nwff_upgrade\inc\sebasetp.h(71): warning C4005: 'ValueTenCents_t' : macro redefinition
                 d:\git_clone\ocp_nwff_upgrade\inc\udmf.h(973) : see previous definition of 'ValueTenCents_t'
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\ctrnlogd.cpp(318): warning C4244: '=' : conversion from '__int64' to 'unsigned long', possible loss of data
         Cvldexpd.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cvldexpd.cpp(142): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
         Generating Code...
         Compiling...
         DailyDlg.cpp
         DiaPollD.cpp
         DiskPrn.cpp
         Duty.cpp
         DutyEDlg.cpp
         Entitled.cpp
         EODVerDlg.cpp
         EvLogDlg.cpp
         ExcessD.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         FreightCharge.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         Gcscsdrd.cpp
         getcscid.cpp
         HrtReceiptNumber.cpp
         HyfAppCrDlg.cpp
         HyfCrtUpgrade.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         HyfCscDpCancelDialog.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfcscdpcanceldialog.cpp(133): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfcscdpcanceldialog.cpp(145): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
         HyfCscHrtCancelDialog.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfcschrtcanceldialog.cpp(173): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfcschrtcanceldialog.cpp(185): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
         HyfFreightPosDialog.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         HyfIssTicket.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfissticket.cpp(736): warning C4482: nonstandard extension used: enum 'CHyfFareCalculation::HrtValidateResult_t' used in qualified name
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfissticket.cpp(739): warning C4482: nonstandard extension used: enum 'CHyfFareCalculation::HrtValidateResult_t' used in qualified name
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfissticket.cpp(748): warning C4482: nonstandard extension used: enum 'CHyfFareCalculation::HrtValidateResult_t' used in qualified name
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfissticket.cpp(763): warning C4482: nonstandard extension used: enum 'CHyfFareCalculation::HrtValidateResult_t' used in qualified name
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfissticket.cpp(787): warning C4482: nonstandard extension used: enum 'CHyfFareCalculation::HrtValidateResult_t' used in qualified name
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfissticket.cpp(797): warning C4482: nonstandard extension used: enum 'CHyfFareCalculation::HrtValidateResult_t' used in qualified name
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfissticket.cpp(815): warning C4482: nonstandard extension used: enum 'CHyfFareCalculation::HrtValidateResult_t' used in qualified name
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfissticket.cpp(832): warning C4482: nonstandard extension used: enum 'CHyfFareCalculation::HrtValidateResult_t' used in qualified name
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfissticket.cpp(859): warning C4482: nonstandard extension used: enum 'CHyfFareCalculation::HrtValidateResult_t' used in qualified name
         HyfMonthlyPassCancel.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfmonthlypasscancel.cpp(160): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfmonthlypasscancel.cpp(181): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfmonthlypasscancel.cpp(188): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
         Generating Code...
         Compiling...
         Hyfpsale.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfpsale.cpp(614): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfpsale.cpp(837): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfpsale.cpp(894): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfpsale.cpp(945): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfpsale.cpp(1004): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfpsale.cpp(1021): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfpsale.cpp(1082): warning C4244: '=' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfpsale.cpp(1136): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfpsale.cpp(1500): warning C4244: '=' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\hyfpsale.cpp(1617): warning C4244: '=' : conversion from '__int64' to 'long', possible loss of data
         HyfQtDlg.cpp
         HyfSailingInfoDialog.cpp
         HyfSales.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         Hyfsflot.cpp
         HyfTInfo.cpp
         HyfTransactPosDialog.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         HyfTrip.cpp
         KceBPRed.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         LARmain.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         ListHdr.cpp
         Log.cpp
         LogEntry.cpp
         LogEvt.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\logevt.cpp(2046): warning C4101: 'e' : unreferenced local variable
         LogHdr.cpp
         LrtBonPt.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\lrtbonpt.cpp(132): warning C4244: '=' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\lrtbonpt.cpp(135): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
         LWIssDlg.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         MainScrn.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         NCSCPick.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         NonCSCRe.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         Generating Code...
     1>d:\git_clone\ocp_nwff_upgrade\ocp\logevt.cpp(2061): warning C4700: uninitialized local variable 'nID' used
         Compiling...
         NonCSCTk.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         OCP.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\inc\sebasetp.h(66): warning C4005: 'Time_t' : macro redefinition
                 d:\git_clone\ocp_nwff_upgrade\inc\udmf.h(941) : see previous definition of 'Time_t'
     1>d:\git_clone\ocp_nwff_upgrade\inc\sebasetp.h(71): warning C4005: 'ValueTenCents_t' : macro redefinition
                 d:\git_clone\ocp_nwff_upgrade\inc\udmf.h(973) : see previous definition of 'ValueTenCents_t'
     1>d:\git_clone\ocp_nwff_upgrade\ocp\ocp.cpp(401): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\ocp.cpp(466): warning C4244: '=' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\ocp.cpp(471): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
         OCPCscOpsDialog.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\inc\sebasetp.h(66): warning C4005: 'Time_t' : macro redefinition
                 d:\git_clone\ocp_nwff_upgrade\inc\udmf.h(941) : see previous definition of 'Time_t'
     1>d:\git_clone\ocp_nwff_upgrade\inc\sebasetp.h(71): warning C4005: 'ValueTenCents_t' : macro redefinition
                 d:\git_clone\ocp_nwff_upgrade\inc\udmf.h(973) : see previous definition of 'ValueTenCents_t'
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemeredemptionlogdlg.h(52): warning C4995: 'CDaoDatabase': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemeredemptionlogdlg.h(53): warning C4995: 'CDaoWorkspace': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemeredemptionlogdlg.h(54): warning C4995: 'CDaoTableDef': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemeredemptionlogdlg.h(55): warning C4995: 'CDaoRecordset': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemeredemptionlogdlg.h(56): warning C4995: 'CDaoQueryDef': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemedlg.h(103): warning C4995: 'CDaoDatabase': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemedlg.h(104): warning C4995: 'CDaoWorkspace': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemedlg.h(105): warning C4995: 'CDaoTableDef': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemedlg.h(106): warning C4995: 'CDaoRecordset': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemedlg.h(107): warning C4995: 'CDaoQueryDef': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\ocp\ocpcscopsdialog.cpp(590): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
         OCPDlg.cpp
         OCPExcep.cpp
         OcpHyfMonthlyPassReIssueSel.cpp
         Ocpmsgbx.cpp
         OcpPosConfirmTxnDialog.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\inc\sebasetp.h(66): warning C4005: 'Time_t' : macro redefinition
                 d:\git_clone\ocp_nwff_upgrade\inc\udmf.h(941) : see previous definition of 'Time_t'
     1>d:\git_clone\ocp_nwff_upgrade\inc\sebasetp.h(71): warning C4005: 'ValueTenCents_t' : macro redefinition
                 d:\git_clone\ocp_nwff_upgrade\inc\udmf.h(973) : see previous definition of 'ValueTenCents_t'
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemeredemptionlogdlg.h(52): warning C4995: 'CDaoDatabase': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemeredemptionlogdlg.h(53): warning C4995: 'CDaoWorkspace': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemeredemptionlogdlg.h(54): warning C4995: 'CDaoTableDef': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemeredemptionlogdlg.h(55): warning C4995: 'CDaoRecordset': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemeredemptionlogdlg.h(56): warning C4995: 'CDaoQueryDef': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemedlg.h(103): warning C4995: 'CDaoDatabase': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemedlg.h(104): warning C4995: 'CDaoWorkspace': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemedlg.h(105): warning C4995: 'CDaoTableDef': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemedlg.h(106): warning C4995: 'CDaoRecordset': name was marked as #pragma deprecated
     1>d:\git_clone\ocp_nwff_upgrade\nwfbloyaltyscheme\nwfbloyaltyschemedlg.h(107): warning C4995: 'CDaoQueryDef': name was marked as #pragma deprecated
         OCPPosDlg.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\ocpposdlg.cpp(598): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\ocpposdlg.cpp(655): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
         OCPPropertySheet.cpp
         OCPPropertyPage.cpp
         OCPUtil.cpp
         OffLoad.cpp
         Pd.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\pd.cpp(1996): warning C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 c:\program files (x86)\microsoft visual studio 10.0\vc\include\string.h(283) : see declaration of 'wcscpy'
     1>d:\git_clone\ocp_nwff_upgrade\ocp\pd.cpp(2361): warning C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 c:\program files (x86)\microsoft visual studio 10.0\vc\include\string.h(283) : see declaration of 'wcscpy'
     1>d:\git_clone\ocp_nwff_upgrade\ocp\pd.cpp(2716): warning C4996: '_swprintf': This function or variable may be unsafe. Consider using _swprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 c:\program files (x86)\microsoft visual studio 10.0\vc\include\wchar.h(761) : see declaration of '_swprintf'
     1>d:\git_clone\ocp_nwff_upgrade\ocp\pd.cpp(2717): warning C4996: 'wcsncat': This function or variable may be unsafe. Consider using wcsncat_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 c:\program files (x86)\microsoft visual studio 10.0\vc\include\string.h(299) : see declaration of 'wcsncat'
         PHyfPTrp.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(1553): warning C4244: 'argument' : conversion from '__int64' to 'ULONG', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(1577): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(1660): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(1688): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(1719): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(1745): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(1769): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(1861): warning C4244: 'argument' : conversion from '__int64' to 'ULONG', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(1883): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(1963): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(1993): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(2024): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(2051): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(2069): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(2111): warning C4244: 'argument' : conversion from '__int64' to 'ULONG', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(2140): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(2153): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(2389): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(2460): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(2520): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(2601): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(2615): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(2635): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(2658): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(2705): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(3087): warning C4482: nonstandard extension used: enum 'CFareCalculation::Status_t' used in qualified name
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(3919): warning C4482: nonstandard extension used: enum 'CHyfFareCalculation::HrtValidateResult_t' used in qualified name
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(3966): warning C4482: nonstandard extension used: enum 'CHyfFareCalculation::HrtValidateResult_t' used in qualified name
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(4931): warning C4800: 'BOOL' : forcing value to bool 'true' or 'false' (performance warning)
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(5117): warning C4482: nonstandard extension used: enum 'CFareCalculation::Status_t' used in qualified name
         PicklstD.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         PLarProc.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\plarproc.cpp(199): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
         PMthPass.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\pmthpass.cpp(228): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\pmthpass.cpp(654): warning C4482: nonstandard extension used: enum 'enumDpTxnType' used in qualified name
     1>d:\git_clone\ocp_nwff_upgrade\ocp\pmthpass.cpp(1148): warning C4244: '=' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\pmthpass.cpp(1811): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
         PosItem.cpp
         PostHrtPrn.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         Generating Code...
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(4098): warning C4715: 'CPostHyfProcessTrip::ValidAdultCsc' : not all control paths return a value
         Compiling...
         PostHrtReceipt.cpp
         posthyf.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         PostMain.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         Postprn.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\postprn.cpp(1388): warning C4482: nonstandard extension used: enum 'EodManLanguage_t' used in qualified name
     1>d:\git_clone\ocp_nwff_upgrade\ocp\postprn.cpp(1388): warning C4482: nonstandard extension used: enum 'EodManLanguage_t' used in qualified name
     1>d:\git_clone\ocp_nwff_upgrade\ocp\postprn.cpp(1391): warning C4482: nonstandard extension used: enum 'EodManLanguage_t' used in qualified name
     1>d:\git_clone\ocp_nwff_upgrade\ocp\postprn.cpp(2281): warning C4018: '<' : signed/unsigned mismatch
         PrntTLFD.cpp
         Ptlftran.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         PurseExD.cpp
         Redoavd.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         RefndDlg.cpp
         Retain.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         RetentED.cpp
         Serialcom.cpp
         Shift.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\shift.cpp(430): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\shift.cpp(451): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
         Shroff.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>d:\git_clone\ocp_nwff_upgrade\ocp\shroff.cpp(708): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
         Souvenir.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         SSIGNOND.CPP
         Stock.cpp
         StockDlg.cpp
         StudentConcessionDailog.cpp
         StudentMonthlyPassDlg.cpp
         Generating Code...
         Compiling...
         Summary.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         Supplem.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         Surcharg.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         surrendr.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         ToasComs.cpp
         Transact.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\cscard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         uploadalt.cpp
         XMessageBox.cpp
             compiling for MFC
         Generating Code...
       ResourceCompile:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\rc.exe /D _DEBUG /D _VC80_UPGRADE=0x0600 /D _UNICODE /D UNICODE /D _AFXDLL /l"0x0c09" /nologo /fo"Debug\OCP.res" OCP.rc 
     1>OCP.rc(1590): warning RC2182: duplicate dialog control ID 65535
         
     1>OCP.rc(1591): warning RC2182: duplicate dialog control ID 65535
         
     1>OCP.rc(1592): warning RC2182: duplicate dialog control ID 65535
         
     1>OCP.rc(1593): warning RC2182: duplicate dialog control ID 65535
         
     1>OCP.rc(1594): warning RC2182: duplicate dialog control ID 65535
         
     1>OCP.rc(1595): warning RC2182: duplicate dialog control ID 65535
         
     1>OCP.rc(1596): warning RC2182: duplicate dialog control ID 65535
         
     1>OCP.rc(1597): warning RC2182: duplicate dialog control ID 65535
         
     1>OCP.rc(1598): warning RC2182: duplicate dialog control ID 65535
         
     1>OCP.rc(1599): warning RC2182: duplicate dialog control ID 65535
         
       ManifestResourceCompile:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\rc.exe /nologo /fo"Debug\OCP.exe.embed.manifest.res" Debug\OCP_manifest.rc 
       Link:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\link.exe /ERRORREPORT:PROMPT /OUT:"D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Debug\OCP.exe" /INCREMENTAL /NOLOGO ..\audit\debug\audit.lib ..\coms_ldp\client_t.lib ..\comms\lib\Debug\comsapi.lib ..\comms\lib\Debug\ftpapi.lib ..\comms\lib\Debug\dsmapi.lib ..\comms\lib\Debug\inetapi.lib ..\comms\lib\Debug\oncrpc.lib ..\comms\lib\Debug\seckey.lib ..\csc\debug\csc.lib ..\csc\largeint.lib ..\cscapi_urp\debug\cscapi_urp.lib ..\ocplog\debug\ocplog.lib ..\ud\debug\ud.lib ..\xdr\debug\xdr.lib ..\dsm\debug\dsm.lib ..\eod\debug\eod.lib ..\toas\debug\toas.lib ..\utils\debug\utils.lib ..\NV\debug\nv.lib ..\MiniAD\debug\miniad.lib ..\OcpEData\debug\ocpedata.lib ..\mobile\debug\mobile.lib ..\mtrbonus\debug\mtrbonus.lib ..\fare\debug\fare.lib D:\GIT_CLONE\ocp_nwff_upgrade\OCP\..\zip\zlib.lib ..\zip\debug\zip.lib ..\NwfbLoyaltyScheme\Debug\NwfbLoyaltyScheme.lib ..\pid\debug\pid.lib ..\rp\debug\rp.lib delayimp.lib ws2_32.lib snmpapi.lib gdiplus.lib "..\sqlite-cipher\lib\sqlite3.lib" Iphlpapi.lib "..\openssl-1.0.1c\out32dll\libeay32.lib" Delayimp.lib /NODEFAULTLIB:LIBC /NODEFAULTLIB:LIBCMT /DELAYLOAD:wininet.dll /MANIFEST /ManifestFile:"Debug\OCP.exe.intermediate.manifest" /MANIFESTUAC:"level='highestAvailable' uiAccess='false'" /DEBUG /PDB:"D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Debug\OCP.pdb" /SUBSYSTEM:WINDOWS /TLBID:1 /ENTRY:"wWinMainCRTStartup" /DYNAMICBASE /NXCOMPAT /IMPLIB:"Debug\OCP.lib" /MACHINE:X86 Debug\OCP.res
         Debug\OCP.exe.embed.manifest.res
         .\Debug\addbonus.obj
         .\Debug\AdminDlg.obj
         .\Debug\AelDlg.obj
         .\Debug\AntiPass.obj
         .\Debug\BackScrn.obj
         .\Debug\Badcssd.obj
         .\Debug\bcscsdrd.obj
         .\Debug\BlocBitD.obj
         .\Debug\Caddvald.obj
         .\Debug\Cantipbd.obj
         .\Debug\CardReplacementReaderPrompt.obj
         .\Debug\Cbmain.obj
         .\Debug\cbpfare.obj
         .\Debug\Cbsroute.obj
         .\Debug\CBStatD.obj
         .\Debug\CBulkDlg.obj
         .\Debug\CCbAppD.obj
         .\Debug\CCHSDLOG.obj
         .\Debug\CCHSPROC.obj
         .\Debug\CCHSUI.obj
         .\Debug\CColorButton.obj
         .\Debug\Ccrdinfd.obj
         .\Debug\CCscOp.obj
         .\Debug\Cgendatd.obj
         .\Debug\CHyfAppD.obj
         .\Debug\CInitErD.obj
         .\Debug\CInitReD.obj
         .\Debug\Cinvcrdd.obj
         .\Debug\Ckcrchad.obj
         .\Debug\Ckcrclad.obj
         .\Debug\CKmbAppD.obj
         .\Debug\CLdMapD.obj
         .\Debug\Cmaind.obj
         .\Debug\Cmtrcad.obj
         .\Debug\Cnegremd.obj
         .\Debug\CNwbAppD.obj
         .\Debug\Coms_ldp.obj
         .\Debug\Concessd.obj
         .\Debug\Cperdatd.obj
         .\Debug\CRepPrn.obj
         .\Debug\CSCard.obj
         .\Debug\CSCChk.obj
         .\Debug\CscDiagD.obj
         .\Debug\CscEditP.obj
         .\Debug\CSCIM.obj
         .\Debug\CscIssID.obj
         .\Debug\CSCPIN.obj
         .\Debug\Cscprefd.obj
         .\Debug\CSCrdGen.obj
         .\Debug\CscReadPin.obj
         .\Debug\CSetDutD.obj
         .\Debug\Cshfrepd.obj
         .\Debug\Cshfstd.obj
         .\Debug\CshOutFl.obj
         .\Debug\Ctllist.obj
         .\Debug\CTmcsComms.obj
         .\Debug\CTmcsPacket.obj
         .\Debug\Ctmeexpd.obj
         .\Debug\Ctrnlogd.obj
         .\Debug\Cvldexpd.obj
         .\Debug\DailyDlg.obj
         .\Debug\DiaPollD.obj
         .\Debug\DiskPrn.obj
         .\Debug\Duty.obj
         .\Debug\DutyEDlg.obj
         .\Debug\Entitled.obj
         .\Debug\EODVerDlg.obj
         .\Debug\EvLogDlg.obj
         .\Debug\ExcessD.obj
         .\Debug\FreightCharge.obj
         .\Debug\Gcscsdrd.obj
         .\Debug\getcscid.obj
         .\Debug\HrtReceiptNumber.obj
         .\Debug\HyfAppCrDlg.obj
         .\Debug\HyfCrtUpgrade.obj
         .\Debug\HyfCscDpCancelDialog.obj
         .\Debug\HyfCscHrtCancelDialog.obj
         .\Debug\HyfFreightPosDialog.obj
         .\Debug\HyfIssTicket.obj
         .\Debug\HyfMonthlyPassCancel.obj
         .\Debug\Hyfpsale.obj
         .\Debug\HyfQtDlg.obj
         .\Debug\HyfSailingInfoDialog.obj
         .\Debug\HyfSales.obj
         .\Debug\Hyfsflot.obj
         .\Debug\HyfTInfo.obj
         .\Debug\HyfTransactPosDialog.obj
         .\Debug\HyfTrip.obj
         .\Debug\KceBPRed.obj
         .\Debug\LARmain.obj
         .\Debug\ListHdr.obj
         .\Debug\Log.obj
         .\Debug\LogEntry.obj
         .\Debug\LogEvt.obj
         .\Debug\LogHdr.obj
         .\Debug\LrtBonPt.obj
         .\Debug\LWIssDlg.obj
         .\Debug\MainScrn.obj
         .\Debug\NCSCPick.obj
         .\Debug\NonCSCRe.obj
         .\Debug\NonCSCTk.obj
         .\Debug\OCP.obj
         .\Debug\OCPCscOpsDialog.obj
         .\Debug\OCPDlg.obj
         .\Debug\OCPExcep.obj
         .\Debug\OcpHyfMonthlyPassReIssueSel.obj
         .\Debug\Ocpmsgbx.obj
         .\Debug\OcpPosConfirmTxnDialog.obj
         .\Debug\OCPPosDlg.obj
         .\Debug\OCPPropertySheet.obj
         .\Debug\OCPPropertyPage.obj
         .\Debug\OCPUtil.obj
         .\Debug\OffLoad.obj
         .\Debug\Pd.obj
         .\Debug\PHyfPTrp.obj
         .\Debug\PicklstD.obj
         .\Debug\PLarProc.obj
         .\Debug\PMthPass.obj
         .\Debug\PosItem.obj
         .\Debug\PostHrtPrn.obj
         .\Debug\PostHrtReceipt.obj
         .\Debug\posthyf.obj
         .\Debug\PostMain.obj
         .\Debug\Postprn.obj
         .\Debug\PrntTLFD.obj
         .\Debug\Ptlftran.obj
         .\Debug\PurseExD.obj
         .\Debug\Redoavd.obj
         .\Debug\RefndDlg.obj
         .\Debug\Retain.obj
         .\Debug\RetentED.obj
         .\Debug\Serialcom.obj
         .\Debug\Shift.obj
         .\Debug\Shroff.obj
         .\Debug\Souvenir.obj
         .\Debug\SSIGNOND.obj
         .\Debug\StdAfx.obj
         .\Debug\Stock.obj
         .\Debug\StockDlg.obj
         .\Debug\StudentConcessionDailog.obj
         .\Debug\StudentMonthlyPassDlg.obj
         .\Debug\Summary.obj
         .\Debug\Supplem.obj
         .\Debug\Surcharg.obj
         .\Debug\surrendr.obj
         .\Debug\ToasComs.obj
         .\Debug\Transact.obj
         .\Debug\uploadalt.obj
         .\Debug\XMessageBox.obj  /MAPINFO:EXPORTS  /defaultlib:VERSION 
     1>LINK : warning LNK4199: /DELAYLOAD:wininet.dll ignored; no imports found from wininet.dll
     1>zlib.lib(adler32.obj) : warning LNK4099: PDB 'zlib.pdb' was not found with 'zlib.lib(adler32.obj)' or at 'D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Debug\zlib.pdb'; linking object as if no debug info
     1>zlib.lib(crc32.obj) : warning LNK4099: PDB 'zlib.pdb' was not found with 'zlib.lib(crc32.obj)' or at 'D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Debug\zlib.pdb'; linking object as if no debug info
     1>zlib.lib(gzio.obj) : warning LNK4099: PDB 'zlib.pdb' was not found with 'zlib.lib(gzio.obj)' or at 'D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Debug\zlib.pdb'; linking object as if no debug info
     1>zlib.lib(deflate.obj) : warning LNK4099: PDB 'zlib.pdb' was not found with 'zlib.lib(deflate.obj)' or at 'D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Debug\zlib.pdb'; linking object as if no debug info
     1>zlib.lib(trees.obj) : warning LNK4099: PDB 'zlib.pdb' was not found with 'zlib.lib(trees.obj)' or at 'D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Debug\zlib.pdb'; linking object as if no debug info
     1>zlib.lib(zutil.obj) : warning LNK4099: PDB 'zlib.pdb' was not found with 'zlib.lib(zutil.obj)' or at 'D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Debug\zlib.pdb'; linking object as if no debug info
     1>zlib.lib(inflate.obj) : warning LNK4099: PDB 'zlib.pdb' was not found with 'zlib.lib(inflate.obj)' or at 'D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Debug\zlib.pdb'; linking object as if no debug info
     1>zlib.lib(infblock.obj) : warning LNK4099: PDB 'zlib.pdb' was not found with 'zlib.lib(infblock.obj)' or at 'D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Debug\zlib.pdb'; linking object as if no debug info
     1>zlib.lib(inftrees.obj) : warning LNK4099: PDB 'zlib.pdb' was not found with 'zlib.lib(inftrees.obj)' or at 'D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Debug\zlib.pdb'; linking object as if no debug info
     1>zlib.lib(infcodes.obj) : warning LNK4099: PDB 'zlib.pdb' was not found with 'zlib.lib(infcodes.obj)' or at 'D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Debug\zlib.pdb'; linking object as if no debug info
     1>zlib.lib(infutil.obj) : warning LNK4099: PDB 'zlib.pdb' was not found with 'zlib.lib(infutil.obj)' or at 'D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Debug\zlib.pdb'; linking object as if no debug info
     1>zlib.lib(inffast.obj) : warning LNK4099: PDB 'zlib.pdb' was not found with 'zlib.lib(inffast.obj)' or at 'D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Debug\zlib.pdb'; linking object as if no debug info
       Manifest:
         Deleting file "Debug\OCP.exe.embed.manifest".
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\mt.exe /nologo /verbose /out:"Debug\OCP.exe.embed.manifest" /manifest Debug\OCP.exe.intermediate.manifest "C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\Include\Manifest\dpiaware.manifest"
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\rc.exe /nologo /fo"Debug\OCP.exe.embed.manifest.res" Debug\OCP_manifest.rc 
       LinkEmbedManifest:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\link.exe /ERRORREPORT:PROMPT /OUT:"D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Debug\OCP.exe" /INCREMENTAL /NOLOGO ..\audit\debug\audit.lib ..\coms_ldp\client_t.lib ..\comms\lib\Debug\comsapi.lib ..\comms\lib\Debug\ftpapi.lib ..\comms\lib\Debug\dsmapi.lib ..\comms\lib\Debug\inetapi.lib ..\comms\lib\Debug\oncrpc.lib ..\comms\lib\Debug\seckey.lib ..\csc\debug\csc.lib ..\csc\largeint.lib ..\cscapi_urp\debug\cscapi_urp.lib ..\ocplog\debug\ocplog.lib ..\ud\debug\ud.lib ..\xdr\debug\xdr.lib ..\dsm\debug\dsm.lib ..\eod\debug\eod.lib ..\toas\debug\toas.lib ..\utils\debug\utils.lib ..\NV\debug\nv.lib ..\MiniAD\debug\miniad.lib ..\OcpEData\debug\ocpedata.lib ..\mobile\debug\mobile.lib ..\mtrbonus\debug\mtrbonus.lib ..\fare\debug\fare.lib D:\GIT_CLONE\ocp_nwff_upgrade\OCP\..\zip\zlib.lib ..\zip\debug\zip.lib ..\NwfbLoyaltyScheme\Debug\NwfbLoyaltyScheme.lib ..\pid\debug\pid.lib ..\rp\debug\rp.lib delayimp.lib ws2_32.lib snmpapi.lib gdiplus.lib "..\sqlite-cipher\lib\sqlite3.lib" Iphlpapi.lib "..\openssl-1.0.1c\out32dll\libeay32.lib" Delayimp.lib /NODEFAULTLIB:LIBC /NODEFAULTLIB:LIBCMT /DELAYLOAD:wininet.dll /MANIFEST /ManifestFile:"Debug\OCP.exe.intermediate.manifest" /MANIFESTUAC:"level='highestAvailable' uiAccess='false'" /DEBUG /PDB:"D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Debug\OCP.pdb" /SUBSYSTEM:WINDOWS /TLBID:1 /ENTRY:"wWinMainCRTStartup" /DYNAMICBASE /NXCOMPAT /IMPLIB:"Debug\OCP.lib" /MACHINE:X86 Debug\OCP.res
         Debug\OCP.exe.embed.manifest.res
         .\Debug\addbonus.obj
         .\Debug\AdminDlg.obj
         .\Debug\AelDlg.obj
         .\Debug\AntiPass.obj
         .\Debug\BackScrn.obj
         .\Debug\Badcssd.obj
         .\Debug\bcscsdrd.obj
         .\Debug\BlocBitD.obj
         .\Debug\Caddvald.obj
         .\Debug\Cantipbd.obj
         .\Debug\CardReplacementReaderPrompt.obj
         .\Debug\Cbmain.obj
         .\Debug\cbpfare.obj
         .\Debug\Cbsroute.obj
         .\Debug\CBStatD.obj
         .\Debug\CBulkDlg.obj
         .\Debug\CCbAppD.obj
         .\Debug\CCHSDLOG.obj
         .\Debug\CCHSPROC.obj
         .\Debug\CCHSUI.obj
         .\Debug\CColorButton.obj
         .\Debug\Ccrdinfd.obj
         .\Debug\CCscOp.obj
         .\Debug\Cgendatd.obj
         .\Debug\CHyfAppD.obj
         .\Debug\CInitErD.obj
         .\Debug\CInitReD.obj
         .\Debug\Cinvcrdd.obj
         .\Debug\Ckcrchad.obj
         .\Debug\Ckcrclad.obj
         .\Debug\CKmbAppD.obj
         .\Debug\CLdMapD.obj
         .\Debug\Cmaind.obj
         .\Debug\Cmtrcad.obj
         .\Debug\Cnegremd.obj
         .\Debug\CNwbAppD.obj
         .\Debug\Coms_ldp.obj
         .\Debug\Concessd.obj
         .\Debug\Cperdatd.obj
         .\Debug\CRepPrn.obj
         .\Debug\CSCard.obj
         .\Debug\CSCChk.obj
         .\Debug\CscDiagD.obj
         .\Debug\CscEditP.obj
         .\Debug\CSCIM.obj
         .\Debug\CscIssID.obj
         .\Debug\CSCPIN.obj
         .\Debug\Cscprefd.obj
         .\Debug\CSCrdGen.obj
         .\Debug\CscReadPin.obj
         .\Debug\CSetDutD.obj
         .\Debug\Cshfrepd.obj
         .\Debug\Cshfstd.obj
         .\Debug\CshOutFl.obj
         .\Debug\Ctllist.obj
         .\Debug\CTmcsComms.obj
         .\Debug\CTmcsPacket.obj
         .\Debug\Ctmeexpd.obj
         .\Debug\Ctrnlogd.obj
         .\Debug\Cvldexpd.obj
         .\Debug\DailyDlg.obj
         .\Debug\DiaPollD.obj
         .\Debug\DiskPrn.obj
         .\Debug\Duty.obj
         .\Debug\DutyEDlg.obj
         .\Debug\Entitled.obj
         .\Debug\EODVerDlg.obj
         .\Debug\EvLogDlg.obj
         .\Debug\ExcessD.obj
         .\Debug\FreightCharge.obj
         .\Debug\Gcscsdrd.obj
         .\Debug\getcscid.obj
         .\Debug\HrtReceiptNumber.obj
         .\Debug\HyfAppCrDlg.obj
         .\Debug\HyfCrtUpgrade.obj
         .\Debug\HyfCscDpCancelDialog.obj
         .\Debug\HyfCscHrtCancelDialog.obj
         .\Debug\HyfFreightPosDialog.obj
         .\Debug\HyfIssTicket.obj
         .\Debug\HyfMonthlyPassCancel.obj
         .\Debug\Hyfpsale.obj
         .\Debug\HyfQtDlg.obj
         .\Debug\HyfSailingInfoDialog.obj
         .\Debug\HyfSales.obj
         .\Debug\Hyfsflot.obj
         .\Debug\HyfTInfo.obj
         .\Debug\HyfTransactPosDialog.obj
         .\Debug\HyfTrip.obj
         .\Debug\KceBPRed.obj
         .\Debug\LARmain.obj
         .\Debug\ListHdr.obj
         .\Debug\Log.obj
         .\Debug\LogEntry.obj
         .\Debug\LogEvt.obj
         .\Debug\LogHdr.obj
         .\Debug\LrtBonPt.obj
         .\Debug\LWIssDlg.obj
         .\Debug\MainScrn.obj
         .\Debug\NCSCPick.obj
         .\Debug\NonCSCRe.obj
         .\Debug\NonCSCTk.obj
         .\Debug\OCP.obj
         .\Debug\OCPCscOpsDialog.obj
         .\Debug\OCPDlg.obj
         .\Debug\OCPExcep.obj
         .\Debug\OcpHyfMonthlyPassReIssueSel.obj
         .\Debug\Ocpmsgbx.obj
         .\Debug\OcpPosConfirmTxnDialog.obj
         .\Debug\OCPPosDlg.obj
         .\Debug\OCPPropertySheet.obj
         .\Debug\OCPPropertyPage.obj
         .\Debug\OCPUtil.obj
         .\Debug\OffLoad.obj
         .\Debug\Pd.obj
         .\Debug\PHyfPTrp.obj
         .\Debug\PicklstD.obj
         .\Debug\PLarProc.obj
         .\Debug\PMthPass.obj
         .\Debug\PosItem.obj
         .\Debug\PostHrtPrn.obj
         .\Debug\PostHrtReceipt.obj
         .\Debug\posthyf.obj
         .\Debug\PostMain.obj
         .\Debug\Postprn.obj
         .\Debug\PrntTLFD.obj
         .\Debug\Ptlftran.obj
         .\Debug\PurseExD.obj
         .\Debug\Redoavd.obj
         .\Debug\RefndDlg.obj
         .\Debug\Retain.obj
         .\Debug\RetentED.obj
         .\Debug\Serialcom.obj
         .\Debug\Shift.obj
         .\Debug\Shroff.obj
         .\Debug\Souvenir.obj
         .\Debug\SSIGNOND.obj
         .\Debug\StdAfx.obj
         .\Debug\Stock.obj
         .\Debug\StockDlg.obj
         .\Debug\StudentConcessionDailog.obj
         .\Debug\StudentMonthlyPassDlg.obj
         .\Debug\Summary.obj
         .\Debug\Supplem.obj
         .\Debug\Surcharg.obj
         .\Debug\surrendr.obj
         .\Debug\ToasComs.obj
         .\Debug\Transact.obj
         .\Debug\uploadalt.obj
         .\Debug\XMessageBox.obj  /MAPINFO:EXPORTS  /defaultlib:VERSION 
         OCP.vcxproj -> D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Debug\OCP.exe
       BscMake:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\bscmake.exe /nologo /o".\Debug\OCP.bsc" Debug\addbonus.sbr Debug\AdminDlg.sbr Debug\AelDlg.sbr Debug\AntiPass.sbr Debug\BackScrn.sbr Debug\Badcssd.sbr Debug\bcscsdrd.sbr Debug\BlocBitD.sbr Debug\Caddvald.sbr Debug\Cantipbd.sbr Debug\CardReplacementReaderPrompt.sbr Debug\Cbmain.sbr Debug\cbpfare.sbr Debug\Cbsroute.sbr Debug\CBStatD.sbr Debug\CBulkDlg.sbr Debug\CCbAppD.sbr Debug\CCHSDLOG.sbr Debug\CCHSPROC.sbr Debug\CCHSUI.sbr Debug\CColorButton.sbr Debug\Ccrdinfd.sbr Debug\CCscOp.sbr Debug\Cgendatd.sbr Debug\CHyfAppD.sbr Debug\CInitErD.sbr Debug\CInitReD.sbr Debug\Cinvcrdd.sbr Debug\Ckcrchad.sbr Debug\Ckcrclad.sbr Debug\CKmbAppD.sbr Debug\CLdMapD.sbr Debug\Cmaind.sbr Debug\Cmtrcad.sbr Debug\Cnegremd.sbr Debug\CNwbAppD.sbr Debug\Coms_ldp.sbr Debug\Concessd.sbr Debug\Cperdatd.sbr Debug\CRepPrn.sbr Debug\CSCard.sbr Debug\CSCChk.sbr Debug\CscDiagD.sbr Debug\CscEditP.sbr Debug\CSCIM.sbr Debug\CscIssID.sbr Debug\CSCPIN.sbr Debug\Cscprefd.sbr Debug\CSCrdGen.sbr Debug\CscReadPin.sbr Debug\CSetDutD.sbr Debug\Cshfrepd.sbr Debug\Cshfstd.sbr Debug\CshOutFl.sbr Debug\Ctllist.sbr Debug\CTmcsComms.sbr Debug\CTmcsPacket.sbr Debug\Ctmeexpd.sbr Debug\Ctrnlogd.sbr Debug\Cvldexpd.sbr Debug\DailyDlg.sbr Debug\DiaPollD.sbr Debug\DiskPrn.sbr Debug\Duty.sbr Debug\DutyEDlg.sbr Debug\Entitled.sbr Debug\EODVerDlg.sbr Debug\EvLogDlg.sbr Debug\ExcessD.sbr Debug\FreightCharge.sbr Debug\Gcscsdrd.sbr Debug\getcscid.sbr Debug\HrtReceiptNumber.sbr Debug\HyfAppCrDlg.sbr Debug\HyfCrtUpgrade.sbr Debug\HyfCscDpCancelDialog.sbr Debug\HyfCscHrtCancelDialog.sbr Debug\HyfFreightPosDialog.sbr Debug\HyfIssTicket.sbr Debug\HyfMonthlyPassCancel.sbr Debug\Hyfpsale.sbr Debug\HyfQtDlg.sbr Debug\HyfSailingInfoDialog.sbr Debug\HyfSales.sbr Debug\Hyfsflot.sbr Debug\HyfTInfo.sbr Debug\HyfTransactPosDialog.sbr Debug\HyfTrip.sbr Debug\KceBPRed.sbr Debug\LARmain.sbr Debug\ListHdr.sbr Debug\Log.sbr Debug\LogEntry.sbr Debug\LogEvt.sbr Debug\LogHdr.sbr Debug\LrtBonPt.sbr Debug\LWIssDlg.sbr Debug\MainScrn.sbr Debug\NCSCPick.sbr Debug\NonCSCRe.sbr Debug\NonCSCTk.sbr Debug\OCP.sbr Debug\OCPCscOpsDialog.sbr Debug\OCPDlg.sbr Debug\OCPExcep.sbr Debug\OcpHyfMonthlyPassReIssueSel.sbr Debug\Ocpmsgbx.sbr Debug\OcpPosConfirmTxnDialog.sbr Debug\OCPPosDlg.sbr Debug\OCPPropertySheet.sbr Debug\OCPPropertyPage.sbr Debug\OCPUtil.sbr Debug\OffLoad.sbr Debug\Pd.sbr Debug\PHyfPTrp.sbr Debug\PicklstD.sbr Debug\PLarProc.sbr Debug\PMthPass.sbr Debug\PosItem.sbr Debug\PostHrtPrn.sbr Debug\PostHrtReceipt.sbr Debug\posthyf.sbr Debug\PostMain.sbr Debug\Postprn.sbr Debug\PrntTLFD.sbr Debug\Ptlftran.sbr Debug\PurseExD.sbr Debug\Redoavd.sbr Debug\RefndDlg.sbr Debug\Retain.sbr Debug\RetentED.sbr Debug\Serialcom.sbr Debug\Shift.sbr Debug\Shroff.sbr Debug\Souvenir.sbr Debug\SSIGNOND.sbr Debug\StdAfx.sbr Debug\Stock.sbr Debug\StockDlg.sbr Debug\StudentConcessionDailog.sbr Debug\StudentMonthlyPassDlg.sbr Debug\Summary.sbr Debug\Supplem.sbr Debug\Surcharg.sbr Debug\surrendr.sbr Debug\ToasComs.sbr Debug\Transact.sbr Debug\uploadalt.sbr Debug\XMessageBox.sbr
       FinalizeBuildStatus:
         Deleting file "Debug\OCP.unsuccessfulbuild".
         Touching "Debug\OCP.lastbuildstate".
     1>Done Building Project "D:\GIT_CLONE\ocp_nwff_upgrade\OCP\OCP.vcxproj" (rebuild target(s)).

Build succeeded.

Time Elapsed 00:00:19.91
