.\Debug\mobile.bsc
D:\GIT_CLONE\ocp_nwff_upgrade\mobile\.\Debug\mobile.lib
D:\GIT_CLONE\ocp_nwff_upgrade\mobile\Debug\BscMake.command.1.tlog
D:\GIT_CLONE\ocp_nwff_upgrade\mobile\Debug\bscmake.read.1.tlog
D:\GIT_CLONE\ocp_nwff_upgrade\mobile\Debug\bscmake.write.1.tlog
D:\GIT_CLONE\ocp_nwff_upgrade\mobile\Debug\cl.command.1.tlog
D:\GIT_CLONE\ocp_nwff_upgrade\mobile\Debug\CL.read.1.tlog
D:\GIT_CLONE\ocp_nwff_upgrade\mobile\Debug\CL.write.1.tlog
D:\GIT_CLONE\ocp_nwff_upgrade\mobile\Debug\lib.command.1.tlog
D:\GIT_CLONE\ocp_nwff_upgrade\mobile\Debug\Lib.read.1.tlog
D:\GIT_CLONE\ocp_nwff_upgrade\mobile\Debug\Lib-link.read.1.tlog
D:\GIT_CLONE\ocp_nwff_upgrade\mobile\Debug\Lib-link.write.1.tlog
D:\GIT_CLONE\OCP_NWFF_UPGRADE\MOBILE\DEBUG\MOBILE.BSC
D:\GIT_CLONE\OCP_NWFF_UPGRADE\MOBILE\DEBUG\MOBILE.LIB
D:\GIT_CLONE\ocp_nwff_upgrade\mobile\Debug\mobile.vcxprojResolveAssemblyReference.cache
D:\GIT_CLONE\ocp_nwff_upgrade\mobile\Debug\mobile.write.1.tlog
D:\GIT_CLONE\OCP_NWFF_UPGRADE\MOBILE\DEBUG\MOBILEAUDIT.OBJ
D:\GIT_CLONE\OCP_NWFF_UPGRADE\MOBILE\DEBUG\MOBILEAUDIT.SBR
D:\GIT_CLONE\OCP_NWFF_UPGRADE\MOBILE\DEBUG\MOBILEDIALOG.OBJ
D:\GIT_CLONE\OCP_NWFF_UPGRADE\MOBILE\DEBUG\MOBILEDIALOG.SBR
D:\GIT_CLONE\OCP_NWFF_UPGRADE\MOBILE\DEBUG\MOBILEDISCOUNT.OBJ
D:\GIT_CLONE\OCP_NWFF_UPGRADE\MOBILE\DEBUG\MOBILEDISCOUNT.SBR
D:\GIT_CLONE\OCP_NWFF_UPGRADE\MOBILE\DEBUG\MOBILEEDATA.OBJ
D:\GIT_CLONE\OCP_NWFF_UPGRADE\MOBILE\DEBUG\MOBILEEDATA.SBR
D:\GIT_CLONE\OCP_NWFF_UPGRADE\MOBILE\DEBUG\MOBILEELOG.OBJ
D:\GIT_CLONE\OCP_NWFF_UPGRADE\MOBILE\DEBUG\MOBILEELOG.SBR
D:\GIT_CLONE\ocp_nwff_upgrade\mobile\Debug\vc100.idb
D:\GIT_CLONE\OCP_NWFF_UPGRADE\MOBILE\DEBUG\VC100.PDB
