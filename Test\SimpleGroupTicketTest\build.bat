@echo off
REM Simple build script for SimpleGroupTicketTest

echo ========================================
echo Simple Group Ticket Test Builder
echo ========================================

REM Check if Visual Studio environment is set up
if "%VCINSTALLDIR%"=="" (
    echo ERROR: Visual Studio environment not detected.
    echo Please run this script from a Visual Studio Command Prompt.
    pause
    exit /b 1
)

REM Set up paths
set PROJECT_DIR=%~dp0
set ROOT_DIR=%PROJECT_DIR%..\..\

echo Building simple test program...

REM Clean previous build
if exist "*.obj" del /Q *.obj
if exist "*.pdb" del /Q *.pdb
if exist "*.ilk" del /Q *.ilk
if exist "SimpleGroupTicketTest.exe" del /Q SimpleGroupTicketTest.exe

REM Compile stdafx.cpp
echo Compiling stdafx.cpp...
cl.exe /nologo /MTd /W3 /Gm /GX /ZI /Od ^
    /I "%ROOT_DIR%INC" /I "%ROOT_DIR%OCP" /I "%ROOT_DIR%utils" ^
    /D "WIN32" /D "_DEBUG" /D "_CONSOLE" /D "_MBCS" ^
    /c stdafx.cpp

if errorlevel 1 (
    echo ERROR: Failed to compile stdafx.cpp
    pause
    exit /b 1
)

REM Compile main program
echo Compiling SimpleGroupTicketTest.cpp...
cl.exe /nologo /MTd /W3 /Gm /GX /ZI /Od ^
    /I "%ROOT_DIR%INC" /I "%ROOT_DIR%OCP" /I "%ROOT_DIR%utils" ^
    /D "WIN32" /D "_DEBUG" /D "_CONSOLE" /D "_MBCS" ^
    /c SimpleGroupTicketTest.cpp

if errorlevel 1 (
    echo ERROR: Failed to compile SimpleGroupTicketTest.cpp
    pause
    exit /b 1
)

REM Link the executable
echo Linking executable...
link.exe /nologo /subsystem:console /debug /machine:I386 ^
    /libpath:"%ROOT_DIR%utils\Debug" /libpath:"%ROOT_DIR%OCP\Debug" ^
    /out:SimpleGroupTicketTest.exe ^
    stdafx.obj SimpleGroupTicketTest.obj ^
    kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib

if errorlevel 1 (
    echo ERROR: Failed to link executable
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo.

REM Ask user if they want to run the test
set /p RUN_TEST="Do you want to run the test program now? (y/n): "
if /i "%RUN_TEST%"=="y" (
    echo.
    echo Running test program...
    echo ========================================
    SimpleGroupTicketTest.exe
    echo ========================================
    echo Test program completed.
) else (
    echo.
    echo Test program built successfully.
    echo Run 'SimpleGroupTicketTest.exe' to execute the tests.
)

echo.
pause
