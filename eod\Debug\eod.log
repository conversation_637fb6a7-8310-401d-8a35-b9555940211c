﻿Build started 3/7/2025 5:33:03 pm.
     1>Project "D:\GIT_CLONE\ocp_nwff_upgrade\eod\eod.vcxproj" on node 2 (build target(s)).
     1>InitializeBuildStatus:
         Creating ".\Debug\eod.unsuccessfulbuild" because "AlwaysCreate" was specified.
       ClCompile:
         All outputs are up-to-date.
         All outputs are up-to-date.
         All outputs are up-to-date.
       Lib:
         All outputs are up-to-date.
         eod.vcxproj -> D:\GIT_CLONE\ocp_nwff_upgrade\eod\.\Debug\eod.lib
       BscMake:
         All outputs are up-to-date.
       FinalizeBuildStatus:
         Deleting file ".\Debug\eod.unsuccessfulbuild".
         Touching ".\Debug\eod.lastbuildstate".
     1>Done Building Project "D:\GIT_CLONE\ocp_nwff_upgrade\eod\eod.vcxproj" (build target(s)).

Build succeeded.

Time Elapsed 00:00:00.17
