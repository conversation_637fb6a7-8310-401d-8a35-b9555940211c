.\Debug\fare.bsc
D:\GIT_CLONE\ocp_nwff_upgrade\FARE\.\Debug\fare.lib
D:\GIT_CLONE\OCP_NWFF_UPGRADE\FARE\DEBUG\AELOCP.OBJ
D:\GIT_CLONE\OCP_NWFF_UPGRADE\FARE\DEBUG\AELOCP.SBR
D:\GIT_CLONE\ocp_nwff_upgrade\FARE\Debug\BscMake.command.1.tlog
D:\GIT_CLONE\ocp_nwff_upgrade\FARE\Debug\bscmake.read.1.tlog
D:\GIT_CLONE\ocp_nwff_upgrade\FARE\Debug\bscmake.write.1.tlog
D:\GIT_CLONE\OCP_NWFF_UPGRADE\FARE\DEBUG\CBPOST.OBJ
D:\GIT_CLONE\OCP_NWFF_UPGRADE\FARE\DEBUG\CBPOST.SBR
D:\GIT_CLONE\ocp_nwff_upgrade\FARE\Debug\cl.command.1.tlog
D:\GIT_CLONE\ocp_nwff_upgrade\FARE\Debug\CL.read.1.tlog
D:\GIT_CLONE\ocp_nwff_upgrade\FARE\Debug\CL.write.1.tlog
D:\GIT_CLONE\OCP_NWFF_UPGRADE\FARE\DEBUG\FARE.BSC
D:\GIT_CLONE\OCP_NWFF_UPGRADE\FARE\DEBUG\FARE.LIB
D:\GIT_CLONE\OCP_NWFF_UPGRADE\FARE\DEBUG\FARE.OBJ
D:\GIT_CLONE\OCP_NWFF_UPGRADE\FARE\DEBUG\FARE.SBR
D:\GIT_CLONE\ocp_nwff_upgrade\FARE\Debug\fare.vcxprojResolveAssemblyReference.cache
D:\GIT_CLONE\ocp_nwff_upgrade\FARE\Debug\fare.write.1.tlog
D:\GIT_CLONE\OCP_NWFF_UPGRADE\FARE\DEBUG\HYFPOST.OBJ
D:\GIT_CLONE\OCP_NWFF_UPGRADE\FARE\DEBUG\HYFPOST.SBR
D:\GIT_CLONE\ocp_nwff_upgrade\FARE\Debug\lib.command.1.tlog
D:\GIT_CLONE\ocp_nwff_upgrade\FARE\Debug\Lib.read.1.tlog
D:\GIT_CLONE\ocp_nwff_upgrade\FARE\Debug\Lib-link.read.1.tlog
D:\GIT_CLONE\ocp_nwff_upgrade\FARE\Debug\Lib-link.write.1.tlog
D:\GIT_CLONE\OCP_NWFF_UPGRADE\FARE\DEBUG\VC100.PDB
