﻿Build started 2/7/2025 2:51:28 pm.
     1>Project "D:\GIT_CLONE\ocp_nwff_upgrade\MiniAD\MiniAD.vcxproj" on node 3 (build target(s)).
     1>InitializeBuildStatus:
         Creating ".\Release\MiniAD.unsuccessfulbuild" because "AlwaysCreate" was specified.
       ClCompile:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I..\inc /Zi /nologo /W3 /WX- /O2 /Ob1 /Oy- /D WIN32 /D NDEBUG /D _LIB /D _AFXDLL /D _USE_32BIT_TIME_T /D _WIN32_WINNT=0x0501 /D UNICODE /D _UNICODE /GF /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Fo".\Release\\" /Fd".\Release\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt OMEData.cpp
         OMEData.cpp
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(255): warning C4005: 'MSG_UNCONF_TRANSACTION_CSC' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(666) : see previous definition of 'MSG_UNCONF_TRANSACTION_CSC'
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(264): warning C4005: 'MSG_UNCONF_AUTOPAY' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(674) : see previous definition of 'MSG_UNCONF_AUTOPAY'
       Lib:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\Lib.exe /OUT:".\Release\MiniAD.lib" /NOLOGO .\Release\OMAudit.obj
         .\Release\OMEData.obj
         .\Release\OMInterf.obj
         .\Release\OMSerial.obj
         .\Release\OMUd.obj
         MiniAD.vcxproj -> D:\GIT_CLONE\ocp_nwff_upgrade\MiniAD\.\Release\MiniAD.lib
       FinalizeBuildStatus:
         Deleting file ".\Release\MiniAD.unsuccessfulbuild".
         Touching ".\Release\MiniAD.lastbuildstate".
     1>Done Building Project "D:\GIT_CLONE\ocp_nwff_upgrade\MiniAD\MiniAD.vcxproj" (build target(s)).

Build succeeded.

Time Elapsed 00:00:04.43
