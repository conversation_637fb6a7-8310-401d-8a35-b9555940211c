﻿Build started 2/7/2025 2:51:28 pm.
     1>Project "D:\GIT_CLONE\ocp_nwff_upgrade\csc\csc.vcxproj" on node 2 (build target(s)).
     1>InitializeBuildStatus:
         Creating ".\Release\csc.unsuccessfulbuild" because "AlwaysCreate" was specified.
       ClCompile:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I"..\openssl-1.0.1c\inc32" /I..\inc /Zi /nologo /W3 /WX- /O2 /Ob1 /Oy- /D WIN32 /D NDEBUG /D _WINDOWS /D _AFXDLL /D _USE_32BIT_TIME_T /D _WIN32_WINNT=0x0501 /D UNICODE /D _UNICODE /GF /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Yc"csc.h" /Fp".\Release\csc.pch" /Fo".\Release\\" /Fd".\Release\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt CSC.CPP
         CSC.CPP
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(255): warning C4005: 'MSG_UNCONF_TRANSACTION_CSC' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(666) : see previous definition of 'MSG_UNCONF_TRANSACTION_CSC'
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(264): warning C4005: 'MSG_UNCONF_AUTOPAY' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(674) : see previous definition of 'MSG_UNCONF_AUTOPAY'
         old cscapi function stub only
         old cscapi function stub only
         old cscapi function stub only
         old cscapi function stub only
         old cscapi function stub only
         old cscapi function stub only
         old cscapi function stub only
         old cscapi function stub only
         old cscapi function stub only
         old cscapi function stub only
         old cscapi function stub only
         old cscapi function stub only
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I"..\openssl-1.0.1c\inc32" /I..\inc /Zi /nologo /W3 /WX- /O2 /Ob1 /Oy- /D WIN32 /D NDEBUG /D _WINDOWS /D _AFXDLL /D _USE_32BIT_TIME_T /D _WIN32_WINNT=0x0501 /D UNICODE /D _UNICODE /GF /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Yu"csc.h" /Fp".\Release\csc.pch" /Fo".\Release\\" /Fd".\Release\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt Addvalue.cpp Block.cpp BonusPt.cpp CSCFld.cpp CSCPREF.CPP Cbpfare.cpp Concess.cpp CscFunc.cpp DatetimePack.cpp Deduct.cpp Entitle.cpp Fix.cpp Geninfo.cpp Getpurse.cpp Init.cpp Initissu.cpp LoyaltyScheme.cpp Persinfo.cpp Poll.cpp Spinfo.cpp Txninfo.cpp Validate.cpp ael.cpp hyfppass.cpp larproc.cpp nwbpfare.cpp
         Addvalue.cpp
     1>Addvalue.cpp(243): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
         Block.cpp
         BonusPt.cpp
         CSCFld.cpp
         CSCPREF.CPP
         Cbpfare.cpp
     1>Cbpfare.cpp(267): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
         Concess.cpp
         CscFunc.cpp
         DatetimePack.cpp
         Deduct.cpp
     1>Deduct.cpp(210): warning C4244: '=' : conversion from '__int64' to 'unsigned long', possible loss of data
         Entitle.cpp
         Fix.cpp
         Geninfo.cpp
         Getpurse.cpp
         Init.cpp
         Initissu.cpp
     1>Initissu.cpp(1513): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>Initissu.cpp(1514): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>Initissu.cpp(1545): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>Initissu.cpp(1546): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>Initissu.cpp(1612): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>Initissu.cpp(1613): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>Initissu.cpp(1746): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>Initissu.cpp(1952): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>Initissu.cpp(2254): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>Initissu.cpp(2504): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
         LoyaltyScheme.cpp
         Persinfo.cpp
         Poll.cpp
         Spinfo.cpp
         Generating Code...
         Compiling...
         Txninfo.cpp
     1>Txninfo.cpp(194): warning C4244: '=' : conversion from 'ULONG' to 'unsigned short', possible loss of data
         Validate.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\CSCard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         ael.cpp
         hyfppass.cpp
     1>hyfppass.cpp(459): warning C4244: '=' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>hyfppass.cpp(973): warning C4244: '=' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>hyfppass.cpp(984): warning C4244: '=' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>hyfppass.cpp(1222): warning C4244: '=' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>hyfppass.cpp(1233): warning C4244: '=' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>hyfppass.cpp(1475): warning C4244: '=' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>hyfppass.cpp(1486): warning C4244: '=' : conversion from '__int64' to 'unsigned long', possible loss of data
         larproc.cpp
     1>larproc.cpp(230): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
         nwbpfare.cpp
     1>nwbpfare.cpp(272): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
         Generating Code...
       Lib:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\Lib.exe /OUT:".\Release\csc.lib" /NOLOGO .\Release\Addvalue.obj
         .\Release\ael.obj
         .\Release\Block.obj
         .\Release\BonusPt.obj
         .\Release\Cbpfare.obj
         .\Release\Concess.obj
         .\Release\CSC.obj
         .\Release\CSCFld.obj
         .\Release\CscFunc.obj
         .\Release\CSCPREF.obj
         .\Release\DatetimePack.obj
         .\Release\Deduct.obj
         .\Release\Entitle.obj
         .\Release\Fix.obj
         .\Release\Geninfo.obj
         .\Release\Getpurse.obj
         .\Release\hyfppass.obj
         .\Release\Init.obj
         .\Release\Initissu.obj
         .\Release\larproc.obj
         .\Release\LoyaltyScheme.obj
         .\Release\nwbpfare.obj
         .\Release\Persinfo.obj
         .\Release\Poll.obj
         .\Release\Spinfo.obj
         .\Release\Txninfo.obj
         .\Release\Validate.obj
         csc.vcxproj -> D:\GIT_CLONE\ocp_nwff_upgrade\csc\.\Release\csc.lib
       FinalizeBuildStatus:
         Deleting file ".\Release\csc.unsuccessfulbuild".
         Touching ".\Release\csc.lastbuildstate".
     1>Done Building Project "D:\GIT_CLONE\ocp_nwff_upgrade\csc\csc.vcxproj" (build target(s)).

Build succeeded.

Time Elapsed 00:00:10.97
