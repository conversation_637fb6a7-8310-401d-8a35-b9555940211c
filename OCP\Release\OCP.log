﻿Build started 2/7/2025 2:51:40 pm.
     1>Project "D:\GIT_CLONE\ocp_nwff_upgrade\OCP\OCP.vcxproj" on node 6 (build target(s)).
     1>InitializeBuildStatus:
         Creating "Release\OCP.unsuccessfulbuild" because "AlwaysCreate" was specified.
       ClCompile:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I..\ocp /I..\inc /I..\zip\release /Zi /nologo /W3 /WX- /O2 /Ob1 /Oy- /D WIN32 /D NDEBUG /D _WINDOWS /D _AFXDLL /D _USE_32BIT_TIME_T /D _WIN32_WINNT=0x0501 /D UNICODE /D _UNICODE /D UNICODE /D _UNICODE /GF /Gm /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Yc"stdafx.h" /Fp".\Release\OCP.pch" /Fo".\Release\\" /Fd".\Release\vc100.pdb" /FR"Release\\" /Gd /TP /analyze- /errorReport:prompt StdAfx.cpp
         StdAfx.cpp
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(255): warning C4005: 'MSG_UNCONF_TRANSACTION_CSC' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(666) : see previous definition of 'MSG_UNCONF_TRANSACTION_CSC'
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(264): warning C4005: 'MSG_UNCONF_AUTOPAY' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(674) : see previous definition of 'MSG_UNCONF_AUTOPAY'
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I..\ocp /I..\inc /I..\zip\release /Zi /nologo /W3 /WX- /O2 /Ob1 /Oy- /D WIN32 /D NDEBUG /D _WINDOWS /D _AFXDLL /D _USE_32BIT_TIME_T /D _WIN32_WINNT=0x0501 /D UNICODE /D _UNICODE /D UNICODE /D _UNICODE /GF /Gm /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Yu"stdafx.h" /Fp".\Release\OCP.pch" /Fo".\Release\\" /Fd".\Release\vc100.pdb" /FR"Release\\" /Gd /TP /analyze- /errorReport:prompt AdminDlg.cpp AelDlg.cpp AntiPass.cpp BackScrn.cpp Badcssd.cpp BlocBitD.cpp CBStatD.cpp CBulkDlg.cpp CCHSDLOG.CPP CCHSPROC.CPP CCHSUI.CPP CCbAppD.cpp CColorButton.cpp CCscOp.cpp CHyfAppD.cpp CInitErD.cpp CInitReD.cpp CKmbAppD.cpp CLdMapD.cpp CNwbAppD.cpp CRepPrn.cpp CSCChk.cpp CSCIM.cpp CSCPIN.CPP CSCard.cpp CSCrdGen.cpp CSetDutD.cpp CTmcsComms.cpp CTmcsPacket.cpp Caddvald.cpp Cantipbd.cpp CardReplacementReaderPrompt.cpp Cbmain.cpp Cbsroute.cpp Ccrdinfd.cpp Cgendatd.cpp Cinvcrdd.cpp Ckcrchad.cpp Ckcrclad.cpp Cmaind.cpp Cmtrcad.cpp Cnegremd.cpp Coms_ldp.cpp Concessd.cpp Cperdatd.cpp CscDiagD.cpp CscEditP.cpp CscIssID.cpp CscReadPin.cpp Cscprefd.cpp CshOutFl.cpp Cshfrepd.cpp Cshfstd.cpp Ctllist.cpp Ctmeexpd.cpp Ctrnlogd.cpp Cvldexpd.cpp DailyDlg.cpp DiaPollD.cpp DiskPrn.cpp Duty.cpp DutyEDlg.cpp EODVerDlg.cpp Entitled.cpp EvLogDlg.cpp ExcessD.cpp FreightCharge.cpp Gcscsdrd.cpp HrtReceiptNumber.cpp HyfAppCrDlg.cpp HyfCrtUpgrade.cpp HyfCscDpCancelDialog.cpp HyfCscHrtCancelDialog.cpp HyfFreightPosDialog.cpp HyfIssTicket.cpp HyfMonthlyPassCancel.cpp HyfQtDlg.cpp HyfSailingInfoDialog.cpp HyfSales.cpp HyfTInfo.cpp HyfTransactPosDialog.cpp HyfTrip.cpp Hyfpsale.cpp Hyfsflot.cpp KceBPRed.cpp LARmain.cpp LWIssDlg.cpp ListHdr.cpp Log.cpp LogEntry.cpp LogEvt.cpp LogHdr.cpp LrtBonPt.cpp MainScrn.cpp NCSCPick.cpp NonCSCRe.cpp NonCSCTk.cpp OCP.cpp OCPCscOpsDialog.cpp OCPDlg.cpp OCPExcep.cpp OCPPosDlg.cpp OCPPropertyPage.cpp OCPPropertySheet.cpp OCPUtil.cpp OcpHyfMonthlyPassReIssueSel.cpp OcpPosConfirmTxnDialog.cpp Ocpmsgbx.cpp OffLoad.cpp PHyfPTrp.cpp PLarProc.cpp PMthPass.cpp Pd.cpp PicklstD.cpp PosItem.cpp PostHrtPrn.cpp PostHrtReceipt.cpp PostMain.cpp Postprn.cpp PrntTLFD.cpp Ptlftran.cpp PurseExD.cpp Redoavd.cpp RefndDlg.cpp Retain.cpp RetentED.cpp SSIGNOND.CPP Serialcom.cpp Shift.cpp Shroff.cpp Souvenir.cpp Stock.cpp StockDlg.cpp StudentConcessionDailog.cpp StudentMonthlyPassDlg.cpp Summary.cpp Supplem.cpp Surcharg.cpp ToasComs.cpp Transact.cpp XMessageBox.cpp addbonus.cpp bcscsdrd.cpp cbpfare.cpp getcscid.cpp posthyf.cpp surrendr.cpp uploadalt.cpp
         PMthPass.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\CSCard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>PMthPass.cpp(228): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>PMthPass.cpp(654): warning C4482: nonstandard extension used: enum 'enumDpTxnType' used in qualified name
     1>PMthPass.cpp(1148): warning C4244: '=' : conversion from '__int64' to 'long', possible loss of data
     1>PMthPass.cpp(1811): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
         PHyfPTrp.cpp
     1>d:\git_clone\ocp_nwff_upgrade\ocp\CSCard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>PHyfPTrp.cpp(1553): warning C4244: 'argument' : conversion from '__int64' to 'ULONG', possible loss of data
     1>PHyfPTrp.cpp(1577): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>PHyfPTrp.cpp(1660): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>PHyfPTrp.cpp(1688): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>PHyfPTrp.cpp(1719): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>PHyfPTrp.cpp(1745): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>PHyfPTrp.cpp(1769): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>PHyfPTrp.cpp(1861): warning C4244: 'argument' : conversion from '__int64' to 'ULONG', possible loss of data
     1>PHyfPTrp.cpp(1883): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>PHyfPTrp.cpp(1963): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>PHyfPTrp.cpp(1993): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>PHyfPTrp.cpp(2024): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>PHyfPTrp.cpp(2051): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>PHyfPTrp.cpp(2069): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>PHyfPTrp.cpp(2111): warning C4244: 'argument' : conversion from '__int64' to 'ULONG', possible loss of data
     1>PHyfPTrp.cpp(2140): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>PHyfPTrp.cpp(2153): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>PHyfPTrp.cpp(2389): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>PHyfPTrp.cpp(2460): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>PHyfPTrp.cpp(2520): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>PHyfPTrp.cpp(2601): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>PHyfPTrp.cpp(2615): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>PHyfPTrp.cpp(2635): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>PHyfPTrp.cpp(2658): warning C4244: 'argument' : conversion from '__int64' to 'long', possible loss of data
     1>PHyfPTrp.cpp(2705): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
     1>PHyfPTrp.cpp(3087): warning C4482: nonstandard extension used: enum 'CFareCalculation::Status_t' used in qualified name
     1>PHyfPTrp.cpp(3919): warning C4482: nonstandard extension used: enum 'CHyfFareCalculation::HrtValidateResult_t' used in qualified name
     1>PHyfPTrp.cpp(3966): warning C4482: nonstandard extension used: enum 'CHyfFareCalculation::HrtValidateResult_t' used in qualified name
     1>PHyfPTrp.cpp(4921): warning C4800: 'BOOL' : forcing value to bool 'true' or 'false' (performance warning)
     1>PHyfPTrp.cpp(5107): warning C4482: nonstandard extension used: enum 'CFareCalculation::Status_t' used in qualified name
         Generating Code...
     1>d:\git_clone\ocp_nwff_upgrade\ocp\phyfptrp.cpp(4098): warning C4715: 'CPostHyfProcessTrip::ValidAdultCsc' : not all control paths return a value
         Skipping... (no relevant changes detected)
         uploadalt.cpp
         getcscid.cpp
         addbonus.cpp
         XMessageBox.cpp
         ToasComs.cpp
         Supplem.cpp
         StudentMonthlyPassDlg.cpp
         StudentConcessionDailog.cpp
         StockDlg.cpp
         Stock.cpp
         Souvenir.cpp
         Shift.cpp
         Serialcom.cpp
         SSIGNOND.CPP
         RetentED.cpp
         RefndDlg.cpp
         PurseExD.cpp
         Ptlftran.cpp
         PrntTLFD.cpp
         PostHrtReceipt.cpp
         PosItem.cpp
         Pd.cpp
         PLarProc.cpp
         OffLoad.cpp
         Ocpmsgbx.cpp
         OcpHyfMonthlyPassReIssueSel.cpp
         OCPUtil.cpp
         OCPPropertySheet.cpp
         OCPPropertyPage.cpp
         OCPExcep.cpp
         OCPDlg.cpp
         NonCSCTk.cpp
         NonCSCRe.cpp
         NCSCPick.cpp
         LrtBonPt.cpp
         LogHdr.cpp
         LogEvt.cpp
         LogEntry.cpp
         Log.cpp
         ListHdr.cpp
         LWIssDlg.cpp
         KceBPRed.cpp
         Hyfsflot.cpp
         HyfTrip.cpp
         HyfTInfo.cpp
         HyfSales.cpp
         HyfSailingInfoDialog.cpp
         HyfQtDlg.cpp
         HyfAppCrDlg.cpp
         HrtReceiptNumber.cpp
         Gcscsdrd.cpp
         EvLogDlg.cpp
         Entitled.cpp
         EODVerDlg.cpp
         DutyEDlg.cpp
         Duty.cpp
         DiskPrn.cpp
         DiaPollD.cpp
         DailyDlg.cpp
         Cvldexpd.cpp
         Ctrnlogd.cpp
         Ctmeexpd.cpp
         Ctllist.cpp
         Cshfstd.cpp
         Cshfrepd.cpp
         CshOutFl.cpp
         CscReadPin.cpp
         CscIssID.cpp
         CscEditP.cpp
         CscDiagD.cpp
         Cperdatd.cpp
         Concessd.cpp
         Coms_ldp.cpp
         Cmtrcad.cpp
         Ckcrclad.cpp
         Ckcrchad.cpp
         Cinvcrdd.cpp
         Cbsroute.cpp
         Cantipbd.cpp
         CTmcsPacket.cpp
         CTmcsComms.cpp
         CSetDutD.cpp
         CSCrdGen.cpp
         CSCPIN.CPP
         CSCIM.cpp
         CSCChk.cpp
         CRepPrn.cpp
         CNwbAppD.cpp
         CLdMapD.cpp
         CKmbAppD.cpp
         CInitReD.cpp
         CInitErD.cpp
         CColorButton.cpp
         CCbAppD.cpp
         CCHSUI.CPP
         CCHSPROC.CPP
         CCHSDLOG.CPP
         CBStatD.cpp
         BlocBitD.cpp
         BackScrn.cpp
         AntiPass.cpp
         AelDlg.cpp
         AdminDlg.cpp
         surrendr.cpp
         posthyf.cpp
         cbpfare.cpp
         bcscsdrd.cpp
         Transact.cpp
         Surcharg.cpp
         Summary.cpp
         Shroff.cpp
         Retain.cpp
         Redoavd.cpp
         Postprn.cpp
         PostMain.cpp
         PostHrtPrn.cpp
         PicklstD.cpp
         OcpPosConfirmTxnDialog.cpp
         OCPPosDlg.cpp
         OCPCscOpsDialog.cpp
         OCP.cpp
         MainScrn.cpp
         LARmain.cpp
         Hyfpsale.cpp
         HyfTransactPosDialog.cpp
         HyfMonthlyPassCancel.cpp
         HyfIssTicket.cpp
         HyfFreightPosDialog.cpp
         HyfCscHrtCancelDialog.cpp
         HyfCscDpCancelDialog.cpp
         HyfCrtUpgrade.cpp
         FreightCharge.cpp
         ExcessD.cpp
         Cscprefd.cpp
         Cnegremd.cpp
         Cmaind.cpp
         Cgendatd.cpp
         Ccrdinfd.cpp
         Cbmain.cpp
         CardReplacementReaderPrompt.cpp
         Caddvald.cpp
         CSCard.cpp
         CHyfAppD.cpp
         CCscOp.cpp
         CBulkDlg.cpp
         Badcssd.cpp
       ResourceCompile:
         All outputs are up-to-date.
       ManifestResourceCompile:
         All outputs are up-to-date.
       Link:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\link.exe /ERRORREPORT:PROMPT /OUT:".\Release\OCP.exe" /INCREMENTAL /NOLOGO ..\audit\Release\audit.lib ..\coms_ldp\client_t.lib ..\comms\lib\Release\comsapi.lib ..\comms\lib\Release\ftpapi.lib ..\comms\lib\Release\dsmapi.lib ..\comms\lib\Release\inetapi.lib ..\comms\lib\Release\oncrpc.lib ..\comms\lib\Release\seckey.lib ..\csc\Release\csc.lib ..\csc\largeint.lib ..\ocplog\Release\ocplog.lib ..\ud\Release\ud.lib ..\xdr\Release\xdr.lib ..\dsm\Release\dsm.lib ..\eod\Release\eod.lib ..\toas\Release\toas.lib ..\utils\Release\utils.lib ..\NV\Release\nv.lib ..\MiniAD\Release\miniad.lib ..\OcpEData\Release\ocpedata.lib ..\mobile\Release\mobile.lib ..\mtrbonus\Release\mtrbonus.lib ..\fare\Release\fare.lib ..\zip\Release\zip.lib ..\NwfbLoyaltyScheme\Release\NwfbLoyaltyScheme.lib ..\pid\Release\pid.lib ..\rp\Release\rp.lib delayimp.lib ws2_32.lib snmpapi.lib ..\cscapi_urp\Release\cscapi_urp.lib gdiplus.lib "..\sqlite-cipher\lib\sqlite3.lib" Iphlpapi.lib ..\zip\zlib.lib "..\openssl-1.0.1c\out32dll\libeay32.lib" Delayimp.lib /NODEFAULTLIB:LIBC /NODEFAULTLIB:LIBCMT /DELAYLOAD:wininet.dll /MANIFEST /ManifestFile:"Release\OCP.exe.intermediate.manifest" /MANIFESTUAC:"level='highestAvailable' uiAccess='false'" /DEBUG /PDB:"D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Release\OCP.pdb" /SUBSYSTEM:WINDOWS /TLBID:1 /ENTRY:"wWinMainCRTStartup" /DYNAMICBASE /NXCOMPAT /IMPLIB:"Release\OCP.lib" /MACHINE:X86 /PROFILE Release\OCP.res
         Release\OCP.exe.embed.manifest.res
         .\Release\addbonus.obj
         .\Release\AdminDlg.obj
         .\Release\AelDlg.obj
         .\Release\AntiPass.obj
         .\Release\BackScrn.obj
         .\Release\Badcssd.obj
         .\Release\bcscsdrd.obj
         .\Release\BlocBitD.obj
         .\Release\Caddvald.obj
         .\Release\Cantipbd.obj
         .\Release\CardReplacementReaderPrompt.obj
         .\Release\Cbmain.obj
         .\Release\cbpfare.obj
         .\Release\Cbsroute.obj
         .\Release\CBStatD.obj
         .\Release\CBulkDlg.obj
         .\Release\CCbAppD.obj
         .\Release\CCHSDLOG.obj
         .\Release\CCHSPROC.obj
         .\Release\CCHSUI.obj
         .\Release\CColorButton.obj
         .\Release\Ccrdinfd.obj
         .\Release\CCscOp.obj
         .\Release\Cgendatd.obj
         .\Release\CHyfAppD.obj
         .\Release\CInitErD.obj
         .\Release\CInitReD.obj
         .\Release\Cinvcrdd.obj
         .\Release\Ckcrchad.obj
         .\Release\Ckcrclad.obj
         .\Release\CKmbAppD.obj
         .\Release\CLdMapD.obj
         .\Release\Cmaind.obj
         .\Release\Cmtrcad.obj
         .\Release\Cnegremd.obj
         .\Release\CNwbAppD.obj
         .\Release\Coms_ldp.obj
         .\Release\Concessd.obj
         .\Release\Cperdatd.obj
         .\Release\CRepPrn.obj
         .\Release\CSCard.obj
         .\Release\CSCChk.obj
         .\Release\CscDiagD.obj
         .\Release\CscEditP.obj
         .\Release\CSCIM.obj
         .\Release\CscIssID.obj
         .\Release\CSCPIN.obj
         .\Release\Cscprefd.obj
         .\Release\CSCrdGen.obj
         .\Release\CscReadPin.obj
         .\Release\CSetDutD.obj
         .\Release\Cshfrepd.obj
         .\Release\Cshfstd.obj
         .\Release\CshOutFl.obj
         .\Release\Ctllist.obj
         .\Release\CTmcsComms.obj
         .\Release\CTmcsPacket.obj
         .\Release\Ctmeexpd.obj
         .\Release\Ctrnlogd.obj
         .\Release\Cvldexpd.obj
         .\Release\DailyDlg.obj
         .\Release\DiaPollD.obj
         .\Release\DiskPrn.obj
         .\Release\Duty.obj
         .\Release\DutyEDlg.obj
         .\Release\Entitled.obj
         .\Release\EODVerDlg.obj
         .\Release\EvLogDlg.obj
         .\Release\ExcessD.obj
         .\Release\FreightCharge.obj
         .\Release\Gcscsdrd.obj
         .\Release\getcscid.obj
         .\Release\HrtReceiptNumber.obj
         .\Release\HyfAppCrDlg.obj
         .\Release\HyfCrtUpgrade.obj
         .\Release\HyfCscDpCancelDialog.obj
         .\Release\HyfCscHrtCancelDialog.obj
         .\Release\HyfFreightPosDialog.obj
         .\Release\HyfIssTicket.obj
         .\Release\HyfMonthlyPassCancel.obj
         .\Release\Hyfpsale.obj
         .\Release\HyfQtDlg.obj
         .\Release\HyfSailingInfoDialog.obj
         .\Release\HyfSales.obj
         .\Release\Hyfsflot.obj
         .\Release\HyfTInfo.obj
         .\Release\HyfTransactPosDialog.obj
         .\Release\HyfTrip.obj
         .\Release\KceBPRed.obj
         .\Release\LARmain.obj
         .\Release\ListHdr.obj
         .\Release\Log.obj
         .\Release\LogEntry.obj
         .\Release\LogEvt.obj
         .\Release\LogHdr.obj
         .\Release\LrtBonPt.obj
         .\Release\LWIssDlg.obj
         .\Release\MainScrn.obj
         .\Release\NCSCPick.obj
         .\Release\NonCSCRe.obj
         .\Release\NonCSCTk.obj
         .\Release\OCP.obj
         .\Release\OCPCscOpsDialog.obj
         .\Release\OCPDlg.obj
         .\Release\OCPExcep.obj
         .\Release\OcpHyfMonthlyPassReIssueSel.obj
         .\Release\Ocpmsgbx.obj
         .\Release\OcpPosConfirmTxnDialog.obj
         .\Release\OCPPosDlg.obj
         .\Release\OCPPropertySheet.obj
         .\Release\OCPPropertyPage.obj
         .\Release\OCPUtil.obj
         .\Release\OffLoad.obj
         .\Release\Pd.obj
         .\Release\PHyfPTrp.obj
         .\Release\PicklstD.obj
         .\Release\PLarProc.obj
         .\Release\PMthPass.obj
         .\Release\PosItem.obj
         .\Release\PostHrtPrn.obj
         .\Release\PostHrtReceipt.obj
         .\Release\posthyf.obj
         .\Release\PostMain.obj
         .\Release\Postprn.obj
         .\Release\PrntTLFD.obj
         .\Release\Ptlftran.obj
         .\Release\PurseExD.obj
         .\Release\Redoavd.obj
         .\Release\RefndDlg.obj
         .\Release\Retain.obj
         .\Release\RetentED.obj
         .\Release\Serialcom.obj
         .\Release\Shift.obj
         .\Release\Shroff.obj
         .\Release\Souvenir.obj
         .\Release\SSIGNOND.obj
         .\Release\StdAfx.obj
         .\Release\Stock.obj
         .\Release\StockDlg.obj
         .\Release\StudentConcessionDailog.obj
         .\Release\StudentMonthlyPassDlg.obj
         .\Release\Summary.obj
         .\Release\Supplem.obj
         .\Release\Surcharg.obj
         .\Release\surrendr.obj
         .\Release\ToasComs.obj
         .\Release\Transact.obj
         .\Release\uploadalt.obj
         .\Release\XMessageBox.obj  /defaultlib:VERSION
     1>LINK : warning LNK4075: ignoring '/INCREMENTAL' due to '/PROFILE' specification
     1>LINK : warning LNK4199: /DELAYLOAD:wininet.dll ignored; no imports found from wininet.dll
     1>zip.lib(inffast.obj) : warning LNK4099: PDB 'zlib.pdb' was not found with 'zip.lib(inffast.obj)' or at 'D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Release\zlib.pdb'; linking object as if no debug info
     1>zip.lib(infutil.obj) : warning LNK4099: PDB 'zlib.pdb' was not found with 'zip.lib(infutil.obj)' or at 'D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Release\zlib.pdb'; linking object as if no debug info
     1>zip.lib(infcodes.obj) : warning LNK4099: PDB 'zlib.pdb' was not found with 'zip.lib(infcodes.obj)' or at 'D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Release\zlib.pdb'; linking object as if no debug info
     1>zip.lib(inftrees.obj) : warning LNK4099: PDB 'zlib.pdb' was not found with 'zip.lib(inftrees.obj)' or at 'D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Release\zlib.pdb'; linking object as if no debug info
     1>zip.lib(infblock.obj) : warning LNK4099: PDB 'zlib.pdb' was not found with 'zip.lib(infblock.obj)' or at 'D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Release\zlib.pdb'; linking object as if no debug info
     1>zip.lib(inflate.obj) : warning LNK4099: PDB 'zlib.pdb' was not found with 'zip.lib(inflate.obj)' or at 'D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Release\zlib.pdb'; linking object as if no debug info
     1>zip.lib(zutil.obj) : warning LNK4099: PDB 'zlib.pdb' was not found with 'zip.lib(zutil.obj)' or at 'D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Release\zlib.pdb'; linking object as if no debug info
     1>zip.lib(trees.obj) : warning LNK4099: PDB 'zlib.pdb' was not found with 'zip.lib(trees.obj)' or at 'D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Release\zlib.pdb'; linking object as if no debug info
     1>zip.lib(deflate.obj) : warning LNK4099: PDB 'zlib.pdb' was not found with 'zip.lib(deflate.obj)' or at 'D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Release\zlib.pdb'; linking object as if no debug info
     1>zip.lib(gzio.obj) : warning LNK4099: PDB 'zlib.pdb' was not found with 'zip.lib(gzio.obj)' or at 'D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Release\zlib.pdb'; linking object as if no debug info
     1>zip.lib(crc32.obj) : warning LNK4099: PDB 'zlib.pdb' was not found with 'zip.lib(crc32.obj)' or at 'D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Release\zlib.pdb'; linking object as if no debug info
     1>zip.lib(adler32.obj) : warning LNK4099: PDB 'zlib.pdb' was not found with 'zip.lib(adler32.obj)' or at 'D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Release\zlib.pdb'; linking object as if no debug info
       Manifest:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\mt.exe /nologo /verbose /out:"Release\OCP.exe.embed.manifest" /manifest Release\OCP.exe.intermediate.manifest "C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\Include\Manifest\dpiaware.manifest"
         All outputs are up-to-date.
       LinkEmbedManifest:
         All outputs are up-to-date.
         OCP.vcxproj -> D:\GIT_CLONE\ocp_nwff_upgrade\OCP\Release\OCP.exe
       BscMake:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\bscmake.exe /nologo /o".\Release\OCP.bsc" Release\addbonus.sbr Release\AdminDlg.sbr Release\AelDlg.sbr Release\AntiPass.sbr Release\BackScrn.sbr Release\Badcssd.sbr Release\bcscsdrd.sbr Release\BlocBitD.sbr Release\Caddvald.sbr Release\Cantipbd.sbr Release\CardReplacementReaderPrompt.sbr Release\Cbmain.sbr Release\cbpfare.sbr Release\Cbsroute.sbr Release\CBStatD.sbr Release\CBulkDlg.sbr Release\CCbAppD.sbr Release\CCHSDLOG.sbr Release\CCHSPROC.sbr Release\CCHSUI.sbr Release\CColorButton.sbr Release\Ccrdinfd.sbr Release\CCscOp.sbr Release\Cgendatd.sbr Release\CHyfAppD.sbr Release\CInitErD.sbr Release\CInitReD.sbr Release\Cinvcrdd.sbr Release\Ckcrchad.sbr Release\Ckcrclad.sbr Release\CKmbAppD.sbr Release\CLdMapD.sbr Release\Cmaind.sbr Release\Cmtrcad.sbr Release\Cnegremd.sbr Release\CNwbAppD.sbr Release\Coms_ldp.sbr Release\Concessd.sbr Release\Cperdatd.sbr Release\CRepPrn.sbr Release\CSCard.sbr Release\CSCChk.sbr Release\CscDiagD.sbr Release\CscEditP.sbr Release\CSCIM.sbr Release\CscIssID.sbr Release\CSCPIN.sbr Release\Cscprefd.sbr Release\CSCrdGen.sbr Release\CscReadPin.sbr Release\CSetDutD.sbr Release\Cshfrepd.sbr Release\Cshfstd.sbr Release\CshOutFl.sbr Release\Ctllist.sbr Release\CTmcsComms.sbr Release\CTmcsPacket.sbr Release\Ctmeexpd.sbr Release\Ctrnlogd.sbr Release\Cvldexpd.sbr Release\DailyDlg.sbr Release\DiaPollD.sbr Release\DiskPrn.sbr Release\Duty.sbr Release\DutyEDlg.sbr Release\Entitled.sbr Release\EODVerDlg.sbr Release\EvLogDlg.sbr Release\ExcessD.sbr Release\FreightCharge.sbr Release\Gcscsdrd.sbr Release\getcscid.sbr Release\HrtReceiptNumber.sbr Release\HyfAppCrDlg.sbr Release\HyfCrtUpgrade.sbr Release\HyfCscDpCancelDialog.sbr Release\HyfCscHrtCancelDialog.sbr Release\HyfFreightPosDialog.sbr Release\HyfIssTicket.sbr Release\HyfMonthlyPassCancel.sbr Release\Hyfpsale.sbr Release\HyfQtDlg.sbr Release\HyfSailingInfoDialog.sbr Release\HyfSales.sbr Release\Hyfsflot.sbr Release\HyfTInfo.sbr Release\HyfTransactPosDialog.sbr Release\HyfTrip.sbr Release\KceBPRed.sbr Release\LARmain.sbr Release\ListHdr.sbr Release\Log.sbr Release\LogEntry.sbr Release\LogEvt.sbr Release\LogHdr.sbr Release\LrtBonPt.sbr Release\LWIssDlg.sbr Release\MainScrn.sbr Release\NCSCPick.sbr Release\NonCSCRe.sbr Release\NonCSCTk.sbr Release\OCP.sbr Release\OCPCscOpsDialog.sbr Release\OCPDlg.sbr Release\OCPExcep.sbr Release\OcpHyfMonthlyPassReIssueSel.sbr Release\Ocpmsgbx.sbr Release\OcpPosConfirmTxnDialog.sbr Release\OCPPosDlg.sbr Release\OCPPropertySheet.sbr Release\OCPPropertyPage.sbr Release\OCPUtil.sbr Release\OffLoad.sbr Release\Pd.sbr Release\PHyfPTrp.sbr Release\PicklstD.sbr Release\PLarProc.sbr Release\PMthPass.sbr Release\PosItem.sbr Release\PostHrtPrn.sbr Release\PostHrtReceipt.sbr Release\posthyf.sbr Release\PostMain.sbr Release\Postprn.sbr Release\PrntTLFD.sbr Release\Ptlftran.sbr Release\PurseExD.sbr Release\Redoavd.sbr Release\RefndDlg.sbr Release\Retain.sbr Release\RetentED.sbr Release\Serialcom.sbr Release\Shift.sbr Release\Shroff.sbr Release\Souvenir.sbr Release\SSIGNOND.sbr Release\StdAfx.sbr Release\Stock.sbr Release\StockDlg.sbr Release\StudentConcessionDailog.sbr Release\StudentMonthlyPassDlg.sbr Release\Summary.sbr Release\Supplem.sbr Release\Surcharg.sbr Release\surrendr.sbr Release\ToasComs.sbr Release\Transact.sbr Release\uploadalt.sbr Release\XMessageBox.sbr
       PostBuildEvent:
         "C:\Program Files (x86)\NSIS\makensis.exe" "D:\GIT_CLONE\ocp_nwff_upgrade\OCP\\..\NSIS-Install-OCP\setup.nsi"
         :VCEnd
         Processing config: C:\Program Files (x86)\NSIS\nsisconf.nsh
         Processing script file: "D:\GIT_CLONE\ocp_nwff_upgrade\OCP\\..\NSIS-Install-OCP\setup.nsi" (ACP)
         
         Processed 1 file, writing output (x86-unicode):
         
         Output: "D:\GIT_CLONE\ocp_nwff_upgrade\NSIS-Install-OCP\OCP-POST-SUNF-070059-WIN32.exe"
         Install: 2 pages (128 bytes), 3 sections (1 required) (6216 bytes), 357 instructions (9996 bytes), 204 strings (6320 bytes), 1 language table (250 bytes).
         Uninstall: 2 pages (192 bytes), 3 sections (1 required) (6216 bytes), 77 instructions (2156 bytes), 116 strings (3230 bytes), 1 language table (250 bytes).
         
         Using zlib compression.
         
         EXE header size:               53248 / 39936 bytes
         Install code:                   4022 / 23374 bytes
         Install data:                2480898 / 5279338 bytes
         Uninstall code+data:           11522 / 15842 bytes
         CRC (0x83483A1A):                  4 / 4 bytes
         
         Total size:                  2549694 / 5358494 bytes (47.5%)
       FinalizeBuildStatus:
         Deleting file "Release\OCP.unsuccessfulbuild".
         Touching "Release\OCP.lastbuildstate".
     1>Done Building Project "D:\GIT_CLONE\ocp_nwff_upgrade\OCP\OCP.vcxproj" (build target(s)).

Build succeeded.

Time Elapsed 00:00:10.25
