// SimpleGroupTicketTest.cpp : Simple test program for group ticket deductions with debug logs
//
// This is a minimal test program that demonstrates group ticket deductions
// with clear debug output to the console.

#include "stdafx.h"
#include <iostream>
#include <vector>
#include "Currency.h"
#include "PosItem.h"

using namespace std;

// Simple mock CSC data
struct SimpleCSC {
    CCurrency purseValue;
    CCurrency negativeLimit;
    bool incomplete;
};

SimpleCSC g_csc;

// Simple mock deduction function with debug logs
bool MockDeduct(CCurrency amount, const char* itemDescription) {
    cout << "\n--- DEDUCTION ATTEMPT ---" << endl;
    cout << "Item: " << itemDescription << endl;
    cout << "Amount to deduct: " << amount.Format() << endl;
    cout << "Current purse: " << g_csc.purseValue.Format() << endl;
    cout << "Negative limit: " << g_csc.negativeLimit.Format() << endl;
    
    CCurrency available = g_csc.purseValue + g_csc.negativeLimit;
    cout << "Available funds: " << available.Format() << endl;
    
    if (available < amount) {
        cout << "RESULT: INSUFFICIENT FUNDS" << endl;
        cout << "------------------------" << endl;
        return false;
    }
    
    g_csc.purseValue = g_csc.purseValue - amount;
    cout << "RESULT: SUCCESS" << endl;
    cout << "New purse value: " << g_csc.purseValue.Format() << endl;
    cout << "------------------------" << endl;
    return true;
}

// Simple group ticket processor
bool ProcessGroupTickets(const vector<CPosTicketItem>& items) {
    cout << "\n========================================" << endl;
    cout << "STARTING GROUP TICKET PROCESSING" << endl;
    cout << "========================================" << endl;
    cout << "Total items: " << items.size() << endl;
    cout << "Initial CSC state:" << endl;
    cout << "  Purse: " << g_csc.purseValue.Format() << endl;
    cout << "  Negative limit: " << g_csc.negativeLimit.Format() << endl;
    
    CCurrency totalFare(0, 0);
    for (const auto& item : items) {
        totalFare = totalFare + item.GetItemSubtotal();
    }
    cout << "Total fare for all items: " << totalFare.Format() << endl;
    
    // Process each item individually (like DeductGroupTicketsByItem)
    for (size_t i = 0; i < items.size(); i++) {
        const CPosTicketItem& item = items[i];
        CCurrency itemFare = item.GetItemSubtotal();
        
        cout << "\n>>> Processing item " << (i + 1) << " of " << items.size() << " <<<" << endl;
        cout << "Item details:" << endl;
        cout << "  ID: " << item.GetItemId() << endl;
        cout << "  Quantity: " << item.GetItemQty() << endl;
        cout << "  Unit price: " << item.GetItemPrice().Format() << endl;
        cout << "  Subtotal: " << itemFare.Format() << endl;
        cout << "  Deluxe: " << (item.GetDeluxeTicket() ? "Yes" : "No") << endl;
        
        // Create description for logging
        char description[100];
        sprintf(description, "Item %d (ID:%d, Qty:%d, %s)", 
                (int)(i + 1), 
                item.GetItemId(), 
                item.GetItemQty(),
                item.GetDeluxeTicket() ? "Deluxe" : "Ordinary");
        
        // Attempt deduction
        if (!MockDeduct(itemFare, description)) {
            cout << "\n!!! GROUP TICKET PROCESSING FAILED !!!" << endl;
            cout << "Failed on item " << (i + 1) << " of " << items.size() << endl;
            cout << "Remaining items will not be processed." << endl;
            return false;
        }
    }
    
    cout << "\n========================================" << endl;
    cout << "GROUP TICKET PROCESSING COMPLETED" << endl;
    cout << "========================================" << endl;
    cout << "All " << items.size() << " items processed successfully" << endl;
    cout << "Final purse value: " << g_csc.purseValue.Format() << endl;
    
    return true;
}

// Initialize CSC with test values
void InitializeCSC() {
    g_csc.purseValue = CCurrency(500, 0);      // $50.00
    g_csc.negativeLimit = CCurrency(350, 0);   // $35.00
    g_csc.incomplete = false;
    
    cout << "CSC Initialized:" << endl;
    cout << "  Purse value: " << g_csc.purseValue.Format() << endl;
    cout << "  Negative limit: " << g_csc.negativeLimit.Format() << endl;
}

// Create test scenarios
void RunSimpleTest() {
    cout << "Simple Group Ticket Deduction Test" << endl;
    cout << "==================================" << endl;
    
    InitializeCSC();
    
    // Create a group of tickets
    vector<CPosTicketItem> groupTickets;
    
    // Add some test tickets
    groupTickets.push_back(CPosTicketItem(1, false, 2, CCurrency(15, 0)));  // 2 ordinary @ $1.50 each
    groupTickets.push_back(CPosTicketItem(2, true, 1, CCurrency(25, 0)));   // 1 deluxe @ $2.50
    groupTickets.push_back(CPosTicketItem(3, false, 3, CCurrency(12, 0)));  // 3 ordinary @ $1.20 each
    
    // Process the group
    bool success = ProcessGroupTickets(groupTickets);
    
    cout << "\n=== FINAL RESULT ===" << endl;
    cout << "Test result: " << (success ? "PASSED" : "FAILED") << endl;
}

// Test with insufficient funds
void RunInsufficientFundsTest() {
    cout << "\n\nInsufficient Funds Test" << endl;
    cout << "======================" << endl;
    
    // Reset CSC with lower amount
    g_csc.purseValue = CCurrency(50, 0);       // $5.00
    g_csc.negativeLimit = CCurrency(20, 0);    // $2.00
    
    cout << "CSC Reset for insufficient funds test:" << endl;
    cout << "  Purse value: " << g_csc.purseValue.Format() << endl;
    cout << "  Negative limit: " << g_csc.negativeLimit.Format() << endl;
    
    // Create expensive tickets
    vector<CPosTicketItem> expensiveTickets;
    expensiveTickets.push_back(CPosTicketItem(4, false, 1, CCurrency(30, 0)));  // $3.00
    expensiveTickets.push_back(CPosTicketItem(5, true, 1, CCurrency(50, 0)));   // $5.00 (should fail)
    
    // Process the group
    bool success = ProcessGroupTickets(expensiveTickets);
    
    cout << "\n=== FINAL RESULT ===" << endl;
    cout << "Test result: " << (success ? "PASSED (unexpected)" : "FAILED (expected)") << endl;
}

int main() {
    cout << "========================================" << endl;
    cout << "SIMPLE GROUP TICKET DEDUCTION TEST" << endl;
    cout << "========================================" << endl;
    cout << "This program demonstrates group ticket" << endl;
    cout << "processing with detailed debug logs." << endl;
    cout << "========================================" << endl;
    
    // Run the tests
    RunSimpleTest();
    RunInsufficientFundsTest();
    
    cout << "\n\nPress Enter to exit..." << endl;
    cin.get();
    
    return 0;
}
