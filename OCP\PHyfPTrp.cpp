//===========================================================================
//  Copyright (c) ERG Ltd. 1996
//
//  Module name     : PHyfPTrp.cpp
//  Module type     : Library source file
//  Compiler(s)     : Microsoft Visual C++ v4.0
//  Environment(s)  : Win32
//
//  Note:   4-character hard tabs are used in this file.
//
//---------------------------------------------------------------------------
//
//  Description
//  -----------
//
//
//
//---------------------------------------------------------------------------
//
//  Who       Date      Description
//  ---     --------    -----------
//  JohnD   08/08/97    Created
//  MDC     30/09/97    Minor modifications to some print routes. Eg to print
//                      Serial numbers on group tickets, and to indicate on
//                      tickets if they were paid by cash.
//  MDC		06/04/98	Upper tiers require 'GroupSize' field in POST UD to be
//						non-zero. Changed GroupSize in HYF freight UD from 0
//						to 1.
//	YTAY	07/05/00	HYF holiday concession scheme
//	YTAY	20/06/00	Record the HYF holiday concession discount data
//						and use the proper fare class definition.
//	YTAY	08/07/00	Added functionality to determine whether message boxs 
//						are displayed during the process of issusing ticket.
//						Initalise status before the start of selling ticket process
//===========================================================================

#include "stdafx.h"
#include "ocp.h"
#include "PHyfPTrp.h"
#include "pmthpass.h"
#include "ocpmsgbx.h"
#include <farecalc.h>
#include "postprn.h"
#include "PostHrtPrn.h"
#include "hyfqtdlg.h"
#include <fshyf.h>
#include "ssignond.h"
#include "NonVol.h"
#include "HyfCrtUpgrade.h"
#include "HyfIssTicket.h"
#include "HyfTInfo.h"
#include "FreightCharge.h"
#include "CCscOp.h"
#include "XMessageBox.h"	// williamto 
#include "REDOAVD.H"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

static int initialised = FALSE;

void AFXAPI DDV_CheckFreightFare(CDataExchange* pDX, CString strFare, CCurrency& Fare);

/////////////////////////////////////////////////////////////////////////////
// CPostHyfProcessTrip dialog
IMPLEMENT_DYNAMIC(CPostHyfProcessTrip, /*COCPPropertyPage*/COCPDialog)

CPostHyfProcessTrip::CPostHyfProcessTrip(CWnd* pParent) : 
	COCPPosDialog/*COCPPropertyPage*/(CPostHyfProcessTrip::IDD, pParent), 
	RegistrationNumberGenerator(&gLogIO, gEod.ServiceProviderId(), gEod.HrtPostId())
{
	//{{AFX_DATA_INIT(CPostHyfProcessTrip)
	m_nTripType = 0;
	m_nOriginFareClass = INVALID_FARE_CLASS;
	m_nOriginHolidayNumber = 0;
	CrtSale		= false;
	CrtValidate = false;
	CrtUpgrade	= false;
	CrtSingleTicket = true;

	// Load accelerator table for virtual keys F1 to F12
	m_hAccelTable = ::LoadAccelerators(	AfxGetInstanceHandle(), MAKEINTRESOURCE( IDR_OCPACCEL ) );
	
	//}}AFX_DATA_INIT
	//memset(m_TypeSelected, 0, sizeof(m_TypeSelected));
	//memset(m_OrdinaryPatrons, 0, sizeof(m_OrdinaryPatrons));
	//memset(m_DeluxePatrons, 0, sizeof(m_DeluxePatrons));
	m_StrFare = "0.00";
	Initialised = FALSE;
	m_DoingCscOperation = FALSE;

	for (int i=0; i<NUM_OF_HYF_PATRON_CLASS; i++)
	{
		bHrtEligiblePaxClass[i] = false;
	}

	CNonVolatile nv;
	nv.Read(&(this->mHrtReprintReceipts), CNonVolatile::POST_HRT_RECEIPT, CNonVolatile::READ_NOLOCK);
}


void CPostHyfProcessTrip::DoDataExchange(CDataExchange* pDX)
{
	COCPPosDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CPostHyfProcessTrip)
	DDX_Radio(pDX, IDC_TRIP_SINGLE_JOURNEY, m_nTripType);
	//DDX_Radio(pDX, IDC_FARE_ORDINARY, m_nOriginFareClass);

	// All of the edit fields (!)
/*    DDX_Text(pDX, IDC_TRIP_EDIT_11,  m_OrdinaryPatrons[0]);
	DDV_MinMaxUInt(pDX, m_OrdinaryPatrons[0], 0, 255);
	DDX_Text(pDX, IDC_TRIP_EDIT_12,  m_DeluxePatrons[0]);
	DDV_MinMaxUInt(pDX, m_DeluxePatrons[0], 0, 255);
	DDX_Text(pDX, IDC_TRIP_EDIT_21,  m_OrdinaryPatrons[1]);
	DDV_MinMaxUInt(pDX, m_OrdinaryPatrons[1], 0, 255);
	DDX_Text(pDX, IDC_TRIP_EDIT_22,  m_DeluxePatrons[1]);
	DDV_MinMaxUInt(pDX, m_DeluxePatrons[1], 0, 255);
	DDX_Text(pDX, IDC_TRIP_EDIT_31,  m_OrdinaryPatrons[2]);
	DDV_MinMaxUInt(pDX, m_OrdinaryPatrons[2], 0, 255);
	DDX_Text(pDX, IDC_TRIP_EDIT_32,  m_DeluxePatrons[2]);
	DDV_MinMaxUInt(pDX, m_DeluxePatrons[2], 0, 255);
	DDX_Text(pDX, IDC_TRIP_EDIT_41,  m_OrdinaryPatrons[3]);
	DDV_MinMaxUInt(pDX, m_OrdinaryPatrons[3], 0, 255);
	DDX_Text(pDX, IDC_TRIP_EDIT_42,  m_DeluxePatrons[3]);
	DDV_MinMaxUInt(pDX, m_DeluxePatrons[3], 0, 255);
	DDX_Text(pDX, IDC_TRIP_EDIT_51,  m_OrdinaryPatrons[4]);
	DDV_MinMaxUInt(pDX, m_OrdinaryPatrons[4], 0, 255);
	DDX_Text(pDX, IDC_TRIP_EDIT_52,  m_DeluxePatrons[4]);
	DDV_MinMaxUInt(pDX, m_DeluxePatrons[4], 0, 255);
	DDX_Text(pDX, IDC_TRIP_EDIT_61,  m_OrdinaryPatrons[5]);
	DDV_MinMaxUInt(pDX, m_OrdinaryPatrons[5], 0, 255);
	DDX_Text(pDX, IDC_TRIP_EDIT_62,  m_DeluxePatrons[5]);
	DDV_MinMaxUInt(pDX, m_DeluxePatrons[5], 0, 255);
	DDX_Text(pDX, IDC_TRIP_EDIT_71,  m_OrdinaryPatrons[6]);
	DDV_MinMaxUInt(pDX, m_OrdinaryPatrons[6], 0, 255);
	DDX_Text(pDX, IDC_TRIP_EDIT_72,  m_DeluxePatrons[6]);
	DDV_MinMaxUInt(pDX, m_DeluxePatrons[6], 0, 255);
	DDX_Text(pDX, IDC_TRIP_EDIT_81,  m_OrdinaryPatrons[7]);
	DDV_MinMaxUInt(pDX, m_OrdinaryPatrons[7], 0, 255);
	DDX_Text(pDX, IDC_TRIP_EDIT_82,  m_DeluxePatrons[7]);
	DDV_MinMaxUInt(pDX, m_DeluxePatrons[7], 0, 255);
	DDX_Text(pDX, IDC_TRIP_EDIT_91,  m_OrdinaryPatrons[8]);
	DDV_MinMaxUInt(pDX, m_OrdinaryPatrons[8], 0, 255);
	DDX_Text(pDX, IDC_TRIP_EDIT_92,  m_DeluxePatrons[8]);
	DDV_MinMaxUInt(pDX, m_DeluxePatrons[8], 0, 255);
	DDX_Text(pDX, IDC_TRIP_EDIT_101, m_OrdinaryPatrons[9]);
	DDV_MinMaxUInt(pDX, m_OrdinaryPatrons[9], 0, 255);
	DDX_Text(pDX, IDC_TRIP_EDIT_102, m_DeluxePatrons[9]);
	DDV_MinMaxUInt(pDX, m_DeluxePatrons[9], 0, 255);
	DDX_Text(pDX, IDC_TRIP_EDIT_111, m_OrdinaryPatrons[10]);
	DDV_MinMaxUInt(pDX, m_OrdinaryPatrons[10], 0, 255);
	DDX_Text(pDX, IDC_TRIP_EDIT_112, m_DeluxePatrons[10]);
	DDV_MinMaxUInt(pDX, m_DeluxePatrons[10], 0, 255);
	DDX_Text(pDX, IDC_TRIP_EDIT_121, m_OrdinaryPatrons[11]);
	DDV_MinMaxUInt(pDX, m_OrdinaryPatrons[11], 0, 255);
	DDX_Text(pDX, IDC_TRIP_EDIT_122, m_DeluxePatrons[11]);
	DDV_MinMaxUInt(pDX, m_DeluxePatrons[11], 0, 255); */

	// All of the check boxes
	//DDX_Check(pDX, IDC_TRIP_CHECK_1,  m_TypeSelected[0]);
	//DDX_Check(pDX, IDC_TRIP_CHECK_2,  m_TypeSelected[1]);
	//DDX_Check(pDX, IDC_TRIP_CHECK_3,  m_TypeSelected[2]);
	//DDX_Check(pDX, IDC_TRIP_CHECK_4,  m_TypeSelected[3]);
	//DDX_Check(pDX, IDC_TRIP_CHECK_5,  m_TypeSelected[4]);
	//DDX_Check(pDX, IDC_TRIP_CHECK_6,  m_TypeSelected[5]);
	//DDX_Check(pDX, IDC_TRIP_CHECK_7,  m_TypeSelected[6]);
	//DDX_Check(pDX, IDC_TRIP_CHECK_8,  m_TypeSelected[7]);
	//DDX_Check(pDX, IDC_TRIP_CHECK_9,  m_TypeSelected[8]);
	//DDX_Check(pDX, IDC_TRIP_CHECK_10, m_TypeSelected[9]);
	//DDX_Check(pDX, IDC_TRIP_CHECK_11, m_TypeSelected[10]);
	//DDX_Check(pDX, IDC_TRIP_CHECK_12, m_TypeSelected[11]); 

	// The fare
	//DDX_Text(pDX, IDC_TRIP_FREIGHT_CHARGE, m_StrFare);
	
	//}}AFX_DATA_MAP
}

//
// Message map
//

BEGIN_MESSAGE_MAP(CPostHyfProcessTrip, COCPPosDialog/*COCPPropertyPage*/)
	//{{AFX_MSG_MAP(CPostHyfProcessTrip)
	ON_MESSAGE(WM_POST_HYF_NOTIFY, OnPostHyfNotify)
	ON_BN_CLICKED(IDC_TRIP_ISSUE_TICKET, OnTripIssueTicketCombo)
	ON_BN_CLICKED(IDC_TRIP_ISSUE_TICKET_SINGLEGROUP, OnTripIssueTicketSingleGroup)
	ON_BN_CLICKED(IDC_TRIP_CHECK_CSC, OnTripCheckCsc)
	ON_BN_CLICKED(IDC_TRIP_CHANGE_QUOTA, OnTripChangeQuota)
	ON_BN_CLICKED(IDC_TRIP_COMPLEMENTARY_PASS, OnTripComplementaryPass)
	ON_BN_CLICKED(IDC_TRIP_CONCESSION_SALE, OnTestHrtPrinter)	//ON_BN_CLICKED(IDC_TRIP_CONCESSION_SALE, OnTripConcessionSale)
	//ON_BN_CLICKED(IDC_TRIP_CONCESSION_CHECK, OnTripConcessionCheck)
	ON_BN_CLICKED(IDC_TRIP_DELUXE_UPGRADE, OnTripDeluxeUpgrade)
	ON_BN_CLICKED(IDC_TRIP_DELUXE, OnTripDeluxe)
//    ON_BN_CLICKED(IDC_TRIP_FREIGHT, OnTripFreight)
	ON_BN_CLICKED(IDC_TRIP_GROUP_NORMAL, OnTripGroupNormal)
	ON_BN_CLICKED(IDC_TRIP_GROUP_PREPAID, OnTripGroupPrepaid)
	ON_BN_CLICKED(IDC_TRIP_MONTHLYPASS, OnTripMonthlypass)
	ON_BN_CLICKED(IDC_TRIP_ORDINARY, OnTripOrdinary)
	ON_BN_CLICKED(IDC_TRIP_PURSE, OnTripPurse)
	ON_BN_CLICKED(IDC_TRIP_CASH, OnTripCash)
	ON_BN_CLICKED(IDC_TRIP_SINGLE_JOURNEY, OnTripSingleJourney)
/*    ON_BN_CLICKED(IDC_TRIP_CHECK_1, OnTripCheck1)
	ON_BN_CLICKED(IDC_TRIP_CHECK_2, OnTripCheck2)
	ON_BN_CLICKED(IDC_TRIP_CHECK_3, OnTripCheck3)
	ON_BN_CLICKED(IDC_TRIP_CHECK_4, OnTripCheck4)
	ON_BN_CLICKED(IDC_TRIP_CHECK_5, OnTripCheck5)
	ON_BN_CLICKED(IDC_TRIP_CHECK_6, OnTripCheck6)
	ON_BN_CLICKED(IDC_TRIP_CHECK_7, OnTripCheck7)
	ON_BN_CLICKED(IDC_TRIP_CHECK_8, OnTripCheck8)
	ON_BN_CLICKED(IDC_TRIP_CHECK_9, OnTripCheck9)
	ON_BN_CLICKED(IDC_TRIP_CHECK_10, OnTripCheck10)
	ON_BN_CLICKED(IDC_TRIP_CHECK_11, OnTripCheck11)
	ON_BN_CLICKED(IDC_TRIP_CHECK_12, OnTripCheck12) */
	ON_COMMAND_RANGE(IDC_TRIP_CHECK_1, IDC_TRIP_CHECK_12, ProcessCheckBox)
	ON_COMMAND_RANGE(IDR_OCPACCEL_VKF1, IDR_OCPACCEL_VKF12, ProcessHotKey)
	ON_BN_CLICKED(IDC_TRIP_ISSUE_MENU, OnTripIssueMenu)
	//ON_BN_CLICKED(IDC_TRIP_CRMENU, OnTripCrmenu)
	ON_BN_CLICKED(IDC_TRIP_CRT_UPGRADE, OnTripCrtUpgrade)
	ON_COMMAND_RANGE(IDC_OCPBUTTON_POS_0, IDC_OCPBUTTON_POS_9, OnPosDigit)
	ON_COMMAND(IDC_OCPBUTTON_POS_DEC, OnPosDec)
	ON_COMMAND(IDC_OCPBUTTON_POS_ENTER, OnPosEnter)
	ON_COMMAND(IDC_OCPBUTTON_POS_RECV, OnPosRecv)
	ON_BN_CLICKED(IDC_OCPBUTTON_POS_AV_OCTOPUS1, OnOctopusFn1)
	ON_COMMAND(IDOK, OnOK)
	ON_BN_CLICKED(IDC_POSTBUTTON_OP1, OnPostButtonOp1)
	ON_BN_CLICKED(IDC_POSTBUTTON_OP2, OnPostButtonOp2)
	ON_BN_CLICKED(IDC_OCPBUTTON_POS_CLEARSEL, OnClearSelection)
	ON_BN_CLICKED(IDC_OCPBUTTON_POS_CLEARALL, OnClearAll)
	ON_BN_CLICKED(IDC_OCPBUTTON_POS_CLR, OnClr)
	ON_BN_CLICKED(IDC_OCPBUTTON_POS_INCR, &CPostHyfProcessTrip::OnPosIncr)
	ON_BN_CLICKED(IDC_OCPBUTTON_POS_DECR, &CPostHyfProcessTrip::OnPosDecr)
	ON_BN_CLICKED(IDC_POSTBUTTON_OP3, &CPostHyfProcessTrip::OnTestHrtRegCode)
	ON_BN_CLICKED(IDC_OCPBUTTON_POS_RESV1, &CPostHyfProcessTrip::OnReprintReceipt)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CPostHyfProcessTrip message handlers

void CPostHyfProcessTrip::InitListHeaders(void)
{
	LOGFONT	DlgFont;
	
	this->GetFont()->GetLogFont(&DlgFont);
	DlgFont.lfWeight = FW_BOLD;
	m_ctlListFont.CreateFontIndirect( &DlgFont );
	m_ctlList.SetFont( &m_ctlListFont );

	m_ctlList.AddCol( 0, -1, 100); 
	m_ctlList.AddCol( 0, -1, 100);
	m_ctlList.AddCol( 0, -1, 100);
	m_ctlList.AddCol( 0, -1, 100);

	// Now create the header
	m_header.CreateHdr( &m_ctlList, this , IDC_HEADER );
	m_header.SetFont(this->GetFont());

	m_header.DoInsertItem(0,	100,	IDS_OCP_HDR_POS_PATRON);
	m_header.DoInsertItem(100,	100,	IDS_OCP_HDR_POS_CLASS);
	m_header.DoInsertItem(200,	100,	IDS_OCP_HDR_POS_QUANTITY);
	m_header.DoInsertItem(300,	100,	IDS_OCP_HDR_POS_SUBTOTAL);
}

BOOL CPostHyfProcessTrip::OnInitDialog()
{
	COCPPosDialog/*COCPPropertyPage*/::OnInitDialog();

	InitListHeaders();
	
	// Subclass all of the child windows on the dialog box.
	RadioOrdinary.SubclassDlgItem(IDC_TRIP_ORDINARY, this);
	RadioDeluxe.SubclassDlgItem(IDC_TRIP_DELUXE, this);
	RadioCash.SubclassDlgItem(IDC_TRIP_CASH, this);
	RadioCash.m_bDontUseWinXPTheme = TRUE;
	RadioPurse.SubclassDlgItem(IDC_TRIP_PURSE, this);
	RadioPurse.m_bDontUseWinXPTheme = TRUE;
	//RadioMonthly.SubclassDlgItem(IDC_TRIP_MONTHLYPASS, this);
	//RadioDeluxeUpgrade.SubclassDlgItem(IDC_TRIP_DELUXE_UPGRADE, this);
	//EditFreightCharge.SubclassDlgItem(IDC_TRIP_FREIGHT_CHARGE, this);

	RadioOrdinary.SetColor(RGB_BLACK, RGB_YELLOW);
	RadioDeluxe.SetColorToWindowsDefault(TRUE);

	for (int i = IDC_TRIP_CHECK_1; i <= IDC_TRIP_CHECK_12; i++)
	{
		this->RadioTripCheck[(i-IDC_TRIP_CHECK_1)].SubclassDlgItem(i, this);
		this->RadioTripCheck[(i-IDC_TRIP_CHECK_1)].SetFont( &m_DlgFont );
	}

#ifdef DEBUG	// enable unit test for HRT number generation under debug mode 
	PostButtonOp3.SubclassDlgItem(IDC_POSTBUTTON_OP3, this);
	PostButtonOp3.EnableWindow(TRUE);
#endif

	RadioCash.SetTextColor(RGB_BLACK);
	RadioCash.SetFaceColor(RGB_PALEGREEN );
	RadioCash.SetIcon((HICON)LoadImage(AfxGetApp()->m_hInstance, MAKEINTRESOURCE(IDI_ICON1), IMAGE_ICON, 32, 32, LR_LOADTRANSPARENT));
	RadioCash.SetFont( &m_DlgFont );
	RadioPurse.SetFaceColor((COLORREF)-1);
	RadioPurse.SetTextColor((COLORREF)-1);
	RadioPurse.SetIcon((HICON)LoadImage(AfxGetApp()->m_hInstance, MAKEINTRESOURCE(IDI_ICON2), IMAGE_ICON, 32, 32, LR_LOADTRANSPARENT));
	RadioPurse.SetFont( &m_DlgFont );

	// register this window
	CHyfTrip
		HyfTrip;
	HyfTrip.RegisterFareWindow( m_hWnd );

	mItems.clear();
// williamto 05Dec2013: [FAT][POST][TF 4.28.14] group ticket now supports arbituary input of patron-travel type
	mItems.reserve( MAX_HYF_TICKET_TYPE+1 ); //mItems.resize(MAX_HYF_TICKET_TYPE+1, CPosTicketItem());
	

	GetDlgItem( IDC_OCPBUTTON_POS_AV_OCTOPUS1 )->SetWindowText(_R(IDS_OCP_LABEL_CSC_ADDVALUE));
	((CButton *)GetDlgItem( IDC_OCPBUTTON_POS_RESV1 ))->SetButtonStyle(BS_MULTILINE);
	GetDlgItem( IDC_OCPBUTTON_POS_RESV1 )->SetWindowText(_R(IDS_OCP_LABEL_HRT_REPRINT));

	((CMFCButton *)GetDlgItem(IDC_TRIP_CONCESSION_SALE))->SetButtonStyle(BS_MULTILINE);

	// Set the default modes
	Initialised = TRUE;
	InitialiseDialogControls();

	// open the output filestream for HRT reprinting records
	this->hrtReprintRecordFile.open((LPCTSTR)gEod.HrtReprintLogName(), std::ios_base::out | std::ios_base::ate | std::ios_base::app);

	return TRUE;
}

BOOL CPostHyfProcessTrip::PreTranslateMessage(MSG* pMsg)
{
	// Translate accelerator key if applicable
	if( (m_hAccelTable != NULL) &&
		::TranslateAccelerator( m_hWnd, m_hAccelTable, pMsg ) )
	{
		return( TRUE );
	}

	return( CDialogEx::PreTranslateMessage( pMsg ) );
}

//
//
//
void CPostHyfProcessTrip::InitialiseDialogControls()
{
	CHyfTrip	HyfTrip;

	EnableConcessionSale = false;
	EnableConcessionChk  = false;

	Fare            = 0;
	PassUsed        = 0;
	TripIndex       = 0;
	PatronClass     = 0;
	FareIndicator   = 0;
	HyfPatronClass  = HYF_ADULT;
	CrtSale		= false;
	CrtValidate = false;
	CrtUpgrade	= false;
	CrtOrgFareClass = ORDINARY_ORDINARY;
	CrtCurFareClass = ORDINARY_ORDINARY;
	m_Discount	= 0;
	Status		= Ok;
	// reset the entries
	mQty = 0;
	mEnteredValue = _T("");

	if (HyfTrip.GetHolidayNum() != NOT_HOLIDAY_NUM)
	{
		if (gEod.HyfIsTripToCentral(HyfTrip.GetDestinationTerminal()))
		{
			// disable concession return/concession check buttons */
			EnableConcessionSale = true;
		}
		else 
		{
			EnableConcessionChk = true;
		}
	}

	::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_ISSUE_MENU), EnableConcessionSale || EnableConcessionChk);	// williamto 22Jan2013: bring issue ticket menu (now HRT issue menu) into control by concession sale/check
	//::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CONCESSION_CHECK), FALSE/*EnableConcessionChk*/);	// williamto 22Jan2013: use CRMENU and CRTUPGRADE button instead
	//::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CONCESSION_SALE),  FALSE/*EnableConcessionSale*/);
	::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CONCESSION_SALE), EnableConcessionSale || EnableConcessionChk);
	::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CRT_UPGRADE),  /*EnableConcessionChk*/FALSE);

	::EnableWindow(::GetDlgItem(m_hWnd, IDC_OCPBUTTON_POS_RESV1), EnableConcessionSale || EnableConcessionChk);

	::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_1), TRUE);
	SetDlgItemText( IDC_TRIP_CHECK_1, gEod.HyfPatronTypeUnicodeStr( HYF_ADULT, ChineseLanguage) );
	::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_2), FALSE);
	SetDlgItemText( IDC_TRIP_CHECK_2, gEod.HyfPatronTypeUnicodeStr( HYF_CHILD12, ChineseLanguage) );
	::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_3), TRUE);
	SetDlgItemText( IDC_TRIP_CHECK_3, gEod.HyfPatronTypeUnicodeStr( HYF_SENIOR, ChineseLanguage) );
	::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_4), TRUE);
	SetDlgItemText( IDC_TRIP_CHECK_4, gEod.HyfPatronTypeUnicodeStr( HYF_STUDENT, ChineseLanguage) );
	::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_5), TRUE);
	SetDlgItemText( IDC_TRIP_CHECK_5, gEod.HyfPatronTypeUnicodeStr( HYF_DISABLED, ChineseLanguage) );
	::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_6), TRUE);
	SetDlgItemText( IDC_TRIP_CHECK_6, gEod.HyfPatronTypeUnicodeStr( HYF_CHILD1, ChineseLanguage) );
	::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_7), TRUE);
	SetDlgItemText( IDC_TRIP_CHECK_7, gEod.HyfPatronTypeUnicodeStr( HYF_CHILD3, ChineseLanguage) );
	::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_8), TRUE);
	SetDlgItemText( IDC_TRIP_CHECK_8, gEod.HyfPatronTypeUnicodeStr( HYF_STAFF, ChineseLanguage) );
	::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_9), FALSE);
	SetDlgItemText( IDC_TRIP_CHECK_9, gEod.HyfPatronTypeUnicodeStr( HYF_STAFF_DEP_ADULT, ChineseLanguage) );
	::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_10), FALSE);
	SetDlgItemText( IDC_TRIP_CHECK_10, gEod.HyfPatronTypeUnicodeStr( HYF_STAFF_DEP_CHILD, ChineseLanguage) );
	::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_11), FALSE);
	SetDlgItemText( IDC_TRIP_CHECK_11, gEod.HyfPatronTypeUnicodeStr( HYF_STAFF_DEP_SENIOR, ChineseLanguage) );
	::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_12), FALSE);
	SetDlgItemText( IDC_TRIP_CHECK_12, gEod.HyfPatronTypeUnicodeStr( HYF_STAFF_RETIRED, ChineseLanguage) );

	SetupControls(SingleJourney);
	m_nTripType = 0;
	// If we are in stand-alone mode, then we need to display the available quota.
	if( HyfTrip.GetOperationMode() == CHyfTrip::StandAloneMode && HyfTrip.GetQuotaMode() == CHyfTrip::QuotaEnabled)
	{
		EnableManualQuotaWindows(TRUE);
		ShowManualQuotaValues();
	}

	if (this->IsKindOf(RUNTIME_CLASS(COCPPropertyPage)))	// hide the cancel button
	{
		GetDlgItem(IDCANCEL)->ShowWindow(SW_HIDE);
	}
	// Transfer data to the dialog box
	UpdateData(FALSE);
}


//
// This message is sent to tell us to re-examine our state:
// we are either now off-trip or quota has been disabled.
//
LRESULT CPostHyfProcessTrip::OnPostHyfNotify(WPARAM p1, LPARAM l1)
{
	switch (p1)
	{
		case POST_START_TRIP:
			// a new trip has started, reinitialise and inform user
			InitialiseDialogControls();
			OcpMessageBox(OCPMSG_HYF_TRIP_STARTED);
			break;

		case POST_END_TRIP:
			// trip has ended, inform the user
			OcpMessageBox(OCPMSG_HYF_TRIP_ENDED);
			break;

		case POST_COMMS_STATE_CHANGED:
			// re-initialise sailing controls as the quota state has probably changed.
			InitialiseDialogControls();
			break;
	}
	return TRUE;
}

//
// Enable the freight windows (done when in freight calc mode)
//
void CPostHyfProcessTrip::EnableFreightWindows(int Enable)
{
	int Show = (Enable) ? SW_SHOW : SW_HIDE;

	::ShowWindow(::GetDlgItem(m_hWnd, IDC_TRIP_FREIGHT_GROUP),  Show);
	::ShowWindow(::GetDlgItem(m_hWnd, IDC_TRIP_FREIGHT_STR),    Show);
	::ShowWindow(::GetDlgItem(m_hWnd, IDC_TRIP_FREIGHT_CHARGE), Show);
}

void CPostHyfProcessTrip::ShowFareWindows(bool bShow)
{
	int Show = (bShow) ? SW_SHOW : SW_HIDE;

	::ShowWindow(::GetDlgItem(m_hWnd, IDC_ORIGIN_FARE_GROUP),  Show);
	::ShowWindow(::GetDlgItem(m_hWnd, IDC_FARE_ORDINARY),    Show);
	::ShowWindow(::GetDlgItem(m_hWnd, IDC_FARE_DELUXE), Show);
	::ShowWindow(::GetDlgItem(m_hWnd, IDC_FARE_HOVER), Show);
}

//
// Shows the current quota values (Done when in stand-alone mode,
// ie quota is enabled and no comms to the LDP).
//
void CPostHyfProcessTrip::ShowManualQuotaValues()
{
	int     PrimaryOrdinary;
	int     PrimaryDeluxe;
	int     ReservedOrdinary;
	int     ReservedDeluxe;
	CString Temp;
	CHyfTrip
		HyfTrip;

	// Get the current quota levels
	HyfTrip.GetQuota(&PrimaryOrdinary, &PrimaryDeluxe, &ReservedOrdinary, &ReservedDeluxe);

	// Set the values in the static text boxes
	Temp.Format(_T("%d"),  PrimaryOrdinary);
	::SetWindowText(::GetDlgItem(m_hWnd, IDC_TRIP_PRIM_ORD_REMAIN), Temp);
	Temp.Format(_T("%d"),  PrimaryDeluxe);
	::SetWindowText(::GetDlgItem(m_hWnd, IDC_TRIP_PRIM_DLX_REMAIN), Temp);
	Temp.Format(_T("%d"),  ReservedOrdinary);
	::SetWindowText(::GetDlgItem(m_hWnd, IDC_TRIP_RES_ORD_REMAIN), Temp);
	Temp.Format(_T("%d"),  ReservedDeluxe);
	::SetWindowText(::GetDlgItem(m_hWnd, IDC_TRIP_RES_DLX_REMAIN), Temp);
}


//
// Shows the Quota windows if we are in standalone mode and quota is enabled
//
void CPostHyfProcessTrip::EnableManualQuotaWindows(int Enable)
{
	int Show = (Enable) ? SW_SHOW : SW_HIDE;

	::ShowWindow(::GetDlgItem(m_hWnd, IDC_TRIP_DELUXE_QUOTA),       Show);
	::ShowWindow(::GetDlgItem(m_hWnd, IDC_TRIP_ORDINARY_QUOTA),     Show);
	::ShowWindow(::GetDlgItem(m_hWnd, IDC_TRIP_DELUXE_QUOTA_RES),   Show);
	::ShowWindow(::GetDlgItem(m_hWnd, IDC_TRIP_ORDINARY_QUOTA_RES), Show);
	::ShowWindow(::GetDlgItem(m_hWnd, IDC_TRIP_PRIM_ORD_REMAIN),    Show);
	::ShowWindow(::GetDlgItem(m_hWnd, IDC_TRIP_PRIM_DLX_REMAIN),    Show);
	::ShowWindow(::GetDlgItem(m_hWnd, IDC_TRIP_RES_ORD_REMAIN),     Show);
	::ShowWindow(::GetDlgItem(m_hWnd, IDC_TRIP_RES_DLX_REMAIN),     Show);
	::ShowWindow(::GetDlgItem(m_hWnd, IDC_TRIP_QUOTAS_GROUP),       Show);
	::ShowWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHANGE_QUOTA),       Show);
	::ShowWindow(::GetDlgItem(m_hWnd, IDC_TRIP_PRIMARY_QUOTA),      Show);
	::ShowWindow(::GetDlgItem(m_hWnd, IDC_TRIP_RESERVED_QUOTA),     Show);
}

//
// Used when we change the fare calc mode to setup
// the various controls on the dialog box.
//
void CPostHyfProcessTrip::SetupControls(FareType_t FareType)
{
	if (Initialised == FALSE)
		return;

	CHyfTrip	HyfTrip;

	//
	// (Re)Initialise the controls
	//
	UpdateData(TRUE);

	// Reset all of the checkboxes/edits
	//memset(m_TypeSelected,    0, sizeof(m_TypeSelected));
	//memset(m_OrdinaryPatrons, 0, sizeof(m_OrdinaryPatrons));
	//memset(m_DeluxePatrons,   0, sizeof(m_DeluxePatrons));

	// williamto 06Dec2013: [FAT][POST] NWFF suggests reset the list if the control is changed
	ClearPendingTransactionList();
	
	// set the freight fare
	m_StrFare = "0.00";

	// If the freight windows were already displayed, hide them
	if (this->FareType == Freight && FareType != Freight)
	{
		EnableFreightWindows(FALSE);
	}

	// If the original fare windows were already displayed, hide them
	if (this->FareType == CRT_UPGRADE && FareType != CRT_UPGRADE)
	{
		ShowFareWindows(FALSE);
	}

	//
	// Set our current mode
	//
	this->FareType = FareType;
	PaymentMethod  = PayCash;
	PassUpgradePaymentMethod = PayCash;
	HrtPaymentMethod = HrtPayCash;
	PatronIndex    = 0;

	if ( HyfTrip.GetFerryType() != FERRY_ORDINARY && HyfTrip.IsTripVesselChanged() == FALSE )
	{
		// it's a hover/catamaran - always deluxe
		IsDeluxe = TRUE;
	}
	else
	{
		IsDeluxe = FALSE;
	}

	// refresh the eligible classes every time controls are set up
	gEod.HyfGetHolidayDiscountEligiblePaxClass(gEod.HyfGetFareClass(HyfTrip.GetFerryType(), this->IsDeluxe), this->bHrtEligiblePaxClass);

	// setup the default control states for all modes
	//RadioPurse.EnableWindow(FALSE);
	//RadioMonthly.EnableWindow(FALSE);
	//RadioDeluxeUpgrade.EnableWindow(FALSE);
	//EditFreightCharge.EnableWindow(FALSE);
	RadioCash.SetCheck(TRUE);
	RadioCash.SetTextColor(RGB_BLACK);
	RadioCash.SetFaceColor(RGB_PALEGREEN);
	RadioPurse.SetCheck(FALSE);
	//RadioPurse.SetColorToWindowsDefault(TRUE);
	RadioPurse.SetFaceColor((COLORREF)-1);
	RadioPurse.SetTextColor((COLORREF)-1);
	//RadioMonthly.SetCheck(FALSE);
	//RadioDeluxeUpgrade.SetCheck(FALSE);
	::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_ISSUE_TICKET_SINGLEGROUP), FALSE);
	::ShowWindow(::GetDlgItem(m_hWnd, IDC_TRIP_ISSUE_TICKET_SINGLEGROUP), FALSE);

	switch (FareType)
	{
		case GroupNormal:
			//
			// Ordinary/deluxe is provided by the edit windows
			//
			::ShowWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_CSC), TRUE);
			::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_ISSUE_TICKET_SINGLEGROUP), TRUE);
			::ShowWindow(::GetDlgItem(m_hWnd, IDC_TRIP_ISSUE_TICKET_SINGLEGROUP), TRUE);

			//RadioOrdinary.EnableWindow(FALSE);
			//RadioDeluxe.EnableWindow(FALSE);
			RadioOrdinary.SetCheck(BST_CHECKED);
			RadioOrdinary.SetColor( RGB_BLACK, RGB_YELLOW );
			RadioDeluxe.SetColorToWindowsDefault(TRUE);
			RadioCash.EnableWindow(TRUE);
			RadioCash.SetCheck(TRUE);
			RadioCash.SetTextColor(RGB_BLACK);
			RadioCash.SetFaceColor(RGB_PALEGREEN);
			//RadioPurse.SetColorToWindowsDefault(TRUE);
			RadioPurse.EnableWindow(TRUE);
			RadioPurse.SetFaceColor((COLORREF)-1);
			RadioPurse.SetTextColor((COLORREF)-1);
			//
			// Enable edit windows and checkboxes
			//
			EnablePatronEdits(0, FALSE, TRUE);
			EnablePatronCheckBox(0, TRUE, TRUE);
			for (int i=0; i<NUM_OF_HYF_PATRON_CLASS; i++)
			{
				this->RadioTripCheck[i].SetTextColor(-1);
				this->RadioTripCheck[i].SetFaceColor(-1);
			}
			GetDlgItem( IDC_TRIP_SINGLE_JOURNEY ) -> SetWindowText( _R(IDS_OCP_BTNLABEL_POST_SJTSALES) );
			// 
			// williamto 20May2014: [webcall][#12707][POST] Enable the incr/decr buttons for group ticket sales
			// 
			GetDlgItem( IDC_OCPBUTTON_POS_INCR)->EnableWindow( TRUE );	
			GetDlgItem( IDC_OCPBUTTON_POS_DECR)->EnableWindow( TRUE );
			break;

		case GroupPrePaid:
			//
			// Ordinary/deluxe is provided by the edit windows
			//
			::ShowWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_CSC), FALSE);
			::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_ISSUE_TICKET_SINGLEGROUP), TRUE);
			::ShowWindow(::GetDlgItem(m_hWnd, IDC_TRIP_ISSUE_TICKET_SINGLEGROUP), TRUE);

			//RadioOrdinary.EnableWindow(FALSE);
			//RadioDeluxe.EnableWindow(FALSE);

			//
			// Payment is not allowed!
			//
			RadioCash.EnableWindow(FALSE);
			RadioCash.SetCheck(FALSE);
			RadioCash.SetTextColor((COLORREF)-1);
			RadioCash.SetFaceColor((COLORREF)-1);
			//RadioCash.SetSetColorToWindowsDefault(TRUE);
			RadioPurse.EnableWindow(FALSE);
			RadioPurse.SetCheck(FALSE);
			RadioPurse.SetFaceColor((COLORREF)-1);
			RadioPurse.SetTextColor((COLORREF)-1);
			//
			// Enable edit windows and checkboxes
			//
			EnablePatronEdits(0, FALSE, TRUE);
			EnablePatronCheckBox(0, TRUE, TRUE);
			for (int i=0; i<NUM_OF_HYF_PATRON_CLASS; i++)
			{
				this->RadioTripCheck[i].SetTextColor(-1);
				this->RadioTripCheck[i].SetFaceColor(-1);
			}
			GetDlgItem( IDC_TRIP_SINGLE_JOURNEY ) -> SetWindowText( _R(IDS_OCP_BTNLABEL_POST_SJTSALES) );

			// 
			// williamto 20May2014: [webcall][#12707][POST] Enable the incr/decr buttons for group ticket sales
			// 
			GetDlgItem( IDC_OCPBUTTON_POS_INCR)->EnableWindow( TRUE );
			GetDlgItem( IDC_OCPBUTTON_POS_DECR)->EnableWindow( TRUE );
			break;

		case CRT_UPGRADE:
			ShowFareWindows(true);
			m_nOriginFareClass = ORDINARY_ORDINARY;
			IsDeluxe = TRUE;
		case Complementary:
		case ConcessionSale:
		case SingleJourney:		// williamto 28May2014: [webcall][#12727][POST][default should be ordinary seats]
		case ConcessionCheck:
			//
			// Setup the payment/travel type
			//
			RadioDeluxe.EnableWindow(TRUE);
			//m_TypeSelected[0] = 1;
			if (IsDeluxe)
			{
				// it's a hover/catamaran
				//RadioOrdinary.EnableWindow(FALSE);
				RadioOrdinary.SetCheck(FALSE);
				RadioOrdinary.SetColorToWindowsDefault();
				RadioDeluxe.SetCheck(TRUE);
				RadioDeluxe.SetColor( RGB_BLACK, RGB_YELLOW );
				//m_DeluxePatrons[0] = 1;
			}
			else
			{
				RadioOrdinary.EnableWindow(TRUE);
				RadioOrdinary.SetCheck(TRUE);
				RadioOrdinary.SetColor( RGB_BLACK, RGB_YELLOW );
				RadioDeluxe.SetCheck(FALSE);
				RadioDeluxe.SetColorToWindowsDefault(TRUE);
				//if (FareType == ConcessionCheck || FareType == ConcessionSale)
				//{
				//	//Disable this button for concession check/sale
				//	RadioDeluxe.EnableWindow(FALSE);
				//}
				//m_OrdinaryPatrons[0] = 1;
			}
			//
			// Complementary and ConcessionCheck do not
			// involve any payment, so we disable all of
			// the payment options.
			//

			if (FareType == Complementary/* || FareType == ConcessionCheck*/)
			{
				::ShowWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_CSC), FALSE);
				RadioCash.EnableWindow(FALSE);
				RadioCash.SetCheck(FALSE);
				RadioCash.SetTextColor((COLORREF)-1);
				RadioCash.SetFaceColor((COLORREF)-1);
				//RadioCash.SetColorToWindowsDefault(TRUE);
				RadioPurse.EnableWindow(FALSE);
				RadioPurse.SetCheck(FALSE);
				//RadioPurse.SetColorToWindowsDefault(TRUE);
				RadioPurse.SetFaceColor((COLORREF)-1);
				RadioPurse.SetTextColor((COLORREF)-1);
			}
			else if (FareType == ConcessionSale || FareType == ConcessionCheck)
			{
				// HRT sales hide check CSC and disable pay by CSC
				::ShowWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_CSC), FALSE);
				RadioPurse.EnableWindow(FALSE);
				RadioPurse.SetCheck(FALSE);
				//RadioPurse.SetColorToWindowsDefault(TRUE);
				RadioPurse.SetFaceColor((COLORREF)-1);
				RadioPurse.SetTextColor((COLORREF)-1);
				// enable/disable pay by csc depending on local configuration setting
				RadioPurse.EnableWindow(gEod.HrtCscPayment());
				RadioPurse.SetCheck(FALSE);
			}
			else
			{
				::ShowWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_CSC), TRUE);
				RadioCash.EnableWindow(TRUE);
				RadioCash.SetCheck(TRUE);
				//RadioCash.SetColor( RGB_BLACK, RGB_PALEGREEN );
				RadioCash.SetTextColor(RGB_BLACK);
				RadioCash.SetFaceColor(RGB_PALEGREEN );
				RadioPurse.EnableWindow(FALSE);
				RadioPurse.SetCheck(FALSE);
				//RadioPurse.SetColorToWindowsDefault(TRUE);
				RadioPurse.SetFaceColor((COLORREF)-1);
				RadioPurse.SetTextColor((COLORREF)-1);
			}

			GetDlgItem( IDC_TRIP_SINGLE_JOURNEY ) -> SetWindowText( ( FareType == Complementary ) ? _R(IDS_OCP_BTNLABEL_POST_RETURN_SJTSALES) : _R(IDS_OCP_BTNLABEL_POST_SJTSALES) );

			//
			// Disable all edits and checkboxes except
			// the first one and check the first checkbox
			// as well.
			//
			EnablePatronEdits(0, FALSE, TRUE);
			if ((FareType == ConcessionSale) || (FareType == ConcessionCheck) || (FareType == CRT_UPGRADE))
			{
				for (int i=0; i<NUM_OF_HYF_PATRON_CLASS; i++)
				{
					EnablePatronCheckBox(i+1, (BOOL)this->bHrtEligiblePaxClass[i], FALSE);
					this->RadioTripCheck[i].SetTextColor(RGB_HRT_GREEN);
					this->RadioTripCheck[i].SetFaceColor(-1);
				}
			}
			else
			{
				EnablePatronCheckBox(0, TRUE, TRUE);
				for (int i=0; i<NUM_OF_HYF_PATRON_CLASS; i++)
				{
					this->RadioTripCheck[i].SetTextColor(-1);
					this->RadioTripCheck[i].SetFaceColor(-1);
				}
			}
			// 
			// williamto 20May2014: [webcall][#12707][POST] Disable the incr/decr buttons for non group ticket sales
			// 
			if (( FareType == Complementary ) || (FareType == ConcessionSale))
			{
				GetDlgItem( IDC_OCPBUTTON_POS_INCR)->EnableWindow( TRUE );
				GetDlgItem( IDC_OCPBUTTON_POS_DECR)->EnableWindow( TRUE );
			}
			else
			{
				GetDlgItem( IDC_OCPBUTTON_POS_INCR)->EnableWindow( FALSE );
				GetDlgItem( IDC_OCPBUTTON_POS_DECR)->EnableWindow( FALSE );
			}
			break;

		case Freight:
			::ShowWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_CSC), TRUE);
			EnableFreightWindows(TRUE);
			RadioOrdinary.EnableWindow(FALSE);
			RadioOrdinary.SetColorToWindowsDefault(TRUE);
			RadioDeluxe.EnableWindow(FALSE);
			RadioDeluxe.SetColorToWindowsDefault(TRUE);
			RadioCash.EnableWindow(TRUE);
			RadioCash.SetCheck(TRUE);
			//RadioCash.SetColor( RGB_BLACK, RGB_PALEGREEN );
			RadioCash.SetTextColor(RGB_BLACK);
			RadioCash.SetFaceColor(RGB_PALEGREEN );
			//RadioPurse.SetColorToWindowsDefault(TRUE);
			RadioPurse.SetFaceColor((COLORREF)-1);
			RadioPurse.SetTextColor((COLORREF)-1);
			//EditFreightCharge.EnableWindow(TRUE);

			EnablePatronEdits(0, FALSE, TRUE);
			EnablePatronCheckBox(0, FALSE, TRUE);
			break;

		default:
			break;
	}

	// disable "ordinary" travel type if ferry type is not ordinary
	if ( HyfTrip.GetFerryType() != FERRY_ORDINARY )
	{
		IsDeluxe = TRUE;
		RadioOrdinary.EnableWindow(FALSE);
		RadioDeluxe.SetColor( RGB_BLACK, RGB_YELLOW );
	}
	else
		RadioOrdinary.EnableWindow(TRUE);
	
	//
	// Send the data back to the controls
	//
	UpdateData(FALSE);
}

void CPostHyfProcessTrip::OnCancel()
{
	CHyfTrip
		HyfTrip;

	HyfTrip.RegisterFareWindow(0);

	// close the HRT reprint log file here
	this->hrtReprintRecordFile.close();

	COCPPosDialog::OnCancel();
}

void CPostHyfProcessTrip::Beep(int AffirmativeResponse)
{
	if (AffirmativeResponse == FALSE)
	{
		for (int Count = 0; Count < 3; Count++)
		{
			::Beep(0, 0);
			::Sleep(100);
		}
	}
	else
	{
		::Beep(0, 0);
	}
}

void CPostHyfProcessTrip::ProcessError(Result_t TheError)
{
	if (TheError == Ok)
		TheError = Status;

	switch (TheError)
	{
		case MaxAutopayExceeded:
			gPid.ShowCentredText( PID_AUTOPAY_EXCEEDED );
			OcpMessageBox(OCPMSG_MAXAUTOPAY_EXCEEDED);
			break;

		case NotEnoughQuota:
			gPid.ShowCentredText( PID_HYFMSG_NOQUOTA );
			OcpMessageBox(OCPMSG_HYF_NOQUOTA);
			break;

		case FreightStrInvalidError:
			OcpMessageBox(OCPMSG_HYF_FREIGHTSTRINVALID);
			break;

		case InsufficientFunds:
			gPid.ShowCentredText( PID_HYF_NOFUNDSAVAIL );
			OcpMessageBox(OCPMSG_INSUFFICIENT_FUNDS);
			break;

		case AntiPassBackError:
			gPid.ShowCentredText( PID_HYFMSG_ANTIPASSBACK );
			OcpMessageBox(OCPMSG_HYF_ANTIPASSBACK);
			break;

		case FareCalculationError:
			OcpMessageBox(OCPMSG_INTERNALFAREERROR);
			break;

		case InvalidPatronClassError:
			gPid.ShowCentredText( PID_HYFMSG_BADMTPATRONCLASS );
			OcpMessageBox(OCPMSG_CANTSELLPATRONCLASS);
			break;

		case CscWriteError:
			OcpMessageBox(OCPMSG_CSCWRITEERROR);
			break;

		case UdError:
			// this isn't used yet
			break;

		case LogicError:
			// we should never get here
			OcpMessageBox(_R(IDS_OCP_ERROR_APPLOGIC));
			break;

		case CscValidityOverflow:
			gPid.ShowCentredText(PID_VALIDITY_EXPD);
			OcpMessageBox(OCPMSG_CSCVALIDITYOVERFLOW);
			break;

		case CashPaymentNotReceived:
			OcpMessageBox(OCPMSG_POS_SHORTCHANGED);
			break;
		
		case TransactionNotAllowed:
			gPid.ShowCentredText(PID_TRANSACTION_NOT_ALLOWED);
			OcpMessageBox(OCPMSG_TRANSACTION_NOT_ALLOWED);
			break;

		case UnconfTxnAbandoned:
			gPid.ShowCentredText( PID_UNCONFIRMED_TRANSACTION );
			OcpMessageBox( OCPMSG_OPCANCELLED );
			break;

		case TransactionRejectedByUrp:
			OcpMessageBox( OCPMSG_TRANSACTION_REJECTED );
			break;

		case DoubleDeduction:
			OcpMessageBox (OCPMSG_DOUBLE_AV_DEDUCT );
			break;

		case HrtCashOutOfRegNumber:
			OcpMessageBox( OCPMSG_PTRIP_HRT_NO_REGNUM );
			RadioCash.SetCheck(FALSE);
			RadioCash.EnableWindow(FALSE);
			RadioPurse.SetCheck(TRUE);
			break;

		case Ok:
		default:
			break;
	}

	if (FareType == ConcessionSale || FareType == ConcessionCheck || FareType == CRT_UPGRADE)
	{
		this->ReturnHrtRegNumbers();
	}

	gPid.Clear();
	gPid.ResetPatronLanguage();		// reset patron language as necessary
}

void CPostHyfProcessTrip::OnTripIssueTicket(bool GroupSingleTicket)
{
	CString         Text;
	CCurrency       NewPurse;
	int             Result = TRUE;
	CHyfTrip		HyfTrip;

	if( HyfTrip.IsInTrip() == FALSE )
	{
		OcpMessageBox(OCPMSG_HYF_TRIP_ENDED);
		return;
	}

	if (!UpdateData(TRUE))
	{
		return;
	}

	PRINT_DEBUG_TRACE(_T("OnTripIssueTicket(): FareType %d PaymentMethod %d"), this->FareType, this->PaymentMethod);
	
	Status = Ok;
	switch (FareType)
	{
		case SingleJourney:
			ProcessSingleJourney(false, false);
			break;

		case Complementary:
			ProcessComplementary();
			break;

		case ConcessionSale:
			ProcessConcessionSale( /*(IsDeluxe ? 0 : 1 ), (IsDeluxe ? 1 : 0 ), */ false, false, false);	// williamto 28Nov2013: [SIT][POST][Issue #35] HRT sale, check, upgrade precedence above MT
			vecHrtRegistrationNumberVector.clear();
			break;

		case ConcessionCheck:
			ProcessConcessionCheck(/*(IsDeluxe ? 0 : 1 ), (IsDeluxe ? 1 : 0 ), */false, false, false);	// williamto 28Nov2013: [SIT][POST][Issue #35] HRT sale, check, upgrade precedence above MT
			vecHrtRegistrationNumberVector.clear();
			break;

		case Freight:
			ProcessFreight();
			break;

		case GroupNormal:
		case GroupPrePaid:
			ProcessGroup(GroupSingleTicket);
			break;

		case CRT_UPGRADE:
			ProcessCrtUpgrade(/*0, 1,*/ m_nOriginFareClass,
							  gEod.HyfGetFareClass(HyfTrip.GetFerryType(), 1),
							  false, false);
			vecHrtRegistrationNumberVector.clear();
			break;
		default:
			break;
	}

	// Return any data back to the dialog
	InitialiseDialogControls();
}

void CPostHyfProcessTrip::OnTripCheckCsc()
{
	CHyfFareCalculation FareCalc;
	CHyfTrip			HyfTrip;

	//CHyfFareCalculation::HrtValidateResult_t	HrtValidationResult;

	if( m_DoingCscOperation == TRUE )
		return;

	ClearPendingTransactionList();

	m_DoingCscOperation = TRUE;
	if( HyfTrip.IsInTrip() == FALSE)
	{
		OcpMessageBox(OCPMSG_HYF_TRIP_ENDED);
		m_DoingCscOperation = FALSE;
		return;
	}

	// disable the window to make sure the button can't be clicked twice
	::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_CSC), FALSE);
	::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_ISSUE_TICKET), FALSE);

	if (Csc.Refresh())
	{
		UpdateData(TRUE);

		// Reset all the buttons to a cash payment state.
		PaymentMethod = PayCash;
		PassUpgradePaymentMethod = PayCash;
		RadioCash.SetCheck(TRUE);
		//RadioCash.SetColor( RGB_BLACK, RGB_PALEGREEN );
		RadioCash.SetTextColor(RGB_BLACK);
		RadioCash.SetFaceColor(RGB_PALEGREEN);
		//RadioPurse.EnableWindow(FALSE);
		RadioPurse.SetCheck(FALSE);
		//RadioPurse.SetColorToWindowsDefault(TRUE);
		RadioPurse.SetFaceColor((COLORREF)-1);
		RadioPurse.SetTextColor((COLORREF)-1);
		//RadioMonthly.EnableWindow(FALSE);
		//RadioMonthly.SetCheck(FALSE);
		//RadioDeluxeUpgrade.EnableWindow(FALSE);
		//RadioDeluxeUpgrade.SetCheck(FALSE);
		// williamto 19Dec2012: assume deluxe for CSC checking
		IsDeluxe = TRUE;
		RadioDeluxe.SetCheck(IsDeluxe);
		RadioDeluxe.SetColor( RGB_BLACK, RGB_YELLOW );
		RadioOrdinary.SetCheck(!IsDeluxe);
		RadioOrdinary.SetColorToWindowsDefault(TRUE);

		gPid.SetPatronLanguage( (Csc.GetGeneralInfo()->LanguageSel == 1) ? EnglishLanguage : ChineseLanguage );
		
		switch (FareType)
		{
			CFareCalculation::Status_t res;
		
			case Complementary:
			//case ConcessionCheck:			// williamto 28Nov2013: [SIT][POST][Issue #35] HRT sale, check, upgrade precedence above MT
			case GroupPrePaid:
				ProcessError(LogicError);
				break;
/* 	// williamto 28Nov2013: [SIT][POST][Issue #35] HRT sale, check, upgrade precedence above MT
			case CRT_UPGRADE: 
			case ConcessionSale:
				m_nOriginFareClass = ORDINARY_ORDINARY;		// williamto 30Aug2013: FAT failure *********, regard leg 1 as ordinary class ordinary ferry only
				FareCalc.CalculatePatronClass(&Csc.HyfInfo, &Csc.GeneralInfo, &Csc.PersonalInfo);
				HyfPatronClass = FareCalc.GetHyfPatronClass();
				PaymentMethod  = PayCsc;

				//
				// Set the patron class checkbox and
				// toggle off the last selected values.
				//
				SetPatronSelection(HyfPatronClass,   IsDeluxe);
				EnablePatronCheckBox(0,              FALSE, TRUE);
				EnablePatronCheckBox(HyfPatronClass, TRUE,  FALSE);

				//
				// Disable the cash radio button
				//
				RadioCash.SetCheck(FALSE);
				RadioCash.SetColorToWindowsDefault(TRUE);
				RadioPurse.EnableWindow(TRUE);
				RadioPurse.SetCheck(TRUE);
				RadioPurse.SetColor( RGB_BLACK, RGB_PALEGREEN );
				AddItem( HyfPatronClass, IsDeluxe, 1, FareCalc.GetFare() );
				
				UpdateData(FALSE);
				break;
*/ // williamto 28Nov2013: [SIT][POST][Issue #35] HRT sale, check, upgrade precedence above MT
			case ConcessionSale:
			case ConcessionCheck:
			case CRT_UPGRADE: 
				this->HrtPaymentMethod = HrtPayEncodeCsc;
				CrtSingleTicket = true;	
			case SingleJourney:
				res = FareCalc.CalculateFare(HyfTrip.GetSailingTime(), HyfTrip.GetOriginTerminal(), 
					HyfTrip.GetDestinationTerminal(), HyfTrip.GetFerryType(), HyfTrip.GetHolidayNum(),
					IsDeluxe, &Csc.GeneralInfo, &Csc.HyfInfo, &Csc.PersonalInfo);

				if (res == CHyfFareCalculation::Ok)
				{
					this->CurrentFareSchema = FareCalc.GetCurrentSchema();
					this->CurrentFareGroup = FareCalc.GetCurrentFareGroup();
					PatronClass    = FareCalc.GetPatronClass();
					HyfPatronClass = FareCalc.GetHyfPatronClass();
					PaymentMethod  = PayCash;		// williamto 19Dec2012: payment not assumed in CSC but in cash
					PassUsed       = FareCalc.GetPassUsed();
					TripIndex      = FareCalc.GetTripIndex();

					//
					// Set the patron class checkbox and
					// toggle off the last selected values.
					//
					SetPatronSelection(HyfPatronClass,   IsDeluxe);
					EnablePatronCheckBox(0,              FALSE, TRUE);
					//EnablePatronCheckBox(HyfPatronClass, TRUE,  FALSE);	// williamto 22Jan2013: disable all selections even the eligible onces

					//
					// Enable the cash radio button
					//
					RadioCash.SetCheck(TRUE);		// williamto 19Dec2012: payment not assumed in CSC but in cash
					//RadioCash.SetColor( RGB_BLACK, RGB_PALEGREEN );
					RadioCash.SetTextColor(RGB_BLACK);
					RadioCash.SetFaceColor(RGB_PALEGREEN);
					if (PassUsed > 0)
					{
						RadioPurse.EnableWindow(FALSE);
						//RadioMonthly.EnableWindow(TRUE);
						//RadioMonthly.SetCheck(TRUE);
						PaymentMethod = (PassUsed == 0 ) ? PayCsc : (PassUsed == CHyfFareCalculation::MonthlyPass1 || PassUsed == CHyfFareCalculation::MonthlyPass2 ) ? PayMp : PayDp;
						if (IsDeluxe)
						{
							RadioDeluxe.EnableWindow(TRUE);
							//RadioDeluxeUpgrade.EnableWindow(TRUE);
						}
						RadioCash.EnableWindow(FALSE);		// williamto 29Aug2013: disable payment when pass is used

						OcpMessageBox( (PassUsed == 0 ) ? OCPMSG_PTRIP_NO_UPGRADE : (PassUsed == CHyfFareCalculation::MonthlyPass1 || PassUsed == CHyfFareCalculation::MonthlyPass2 ) ? OCPMSG_PTRIP_MT_UPGRADE : OCPMSG_PTRIP_DP_UPGRADE );

					}
					else if ( FareCalc.CscIslandNum != 0 && FareCalc.CscHolidayNum != 0 && ( FareCalc.CscHolidayNum == HyfTrip.GetHolidayNum() ) )		// williamto 30Aug2013: FAT failure *********, consider the HRT return trip validation
					{	
						switch ( FareCalc.CscFareClass )	// trip index
						{
						 // williamto 28Nov2013: [SIT][POST][Issue #35] HRT sale, check, upgrade precedence above MT, consequence of that implementation, allow sale by CSC payment only like GAK
						case 0:
							if ( EnableConcessionSale )
							{
								PaymentMethod = PayCsc;
								HrtPaymentMethod = HrtPayEncodeCsc;
								CrtSingleTicket = true;	
								FareType = ConcessionSale;
								if (FareCalc.GetConcessionReturnFare(HyfTrip.GetOriginTerminal(), HyfTrip.GetDestinationTerminal(), 
									HyfTrip.GetFerryType(), IsDeluxe, &Csc.GeneralInfo, &Csc.HyfInfo, &Csc.PersonalInfo) == CHyfFareCalculation::Ok)
								{
									this->CurrentFareSchema = FareCalc.GetCurrentSchema();
									this->CurrentFareGroup = FareCalc.GetCurrentFareGroup();
									Fare           = FareCalc.GetFare();
									HyfPatronClass = FareCalc.GetHyfPatronClass();
									PatronClass    = FareCalc.GetPatronClass();
									FareIndicator  = FareCalc.GetFareIndicator();
									Csc.IslandNum  = FareCalc.IslandNum;
									Csc.FareClass  = FareCalc.FareClass;
								}
								else
								{
									Status = InvalidPatronClassError;
								}
								RadioCash.SetCheck( FALSE );		// williamto 19Dec2012: payment not assumed in CSC but in cash
								//RadioCash.SetColorToWindowsDefault(TRUE);
								RadioCash.SetTextColor((COLORREF)-1);
								RadioCash.SetFaceColor((COLORREF)-1);
								RadioPurse.SetCheck( TRUE );
								RadioPurse.SetFaceColor/*SetColor*/( /*RGB_BLACK,*/ RGB_PALEGREEN, 1 );
								RadioCash.EnableWindow(FALSE);		// williamto 29Aug2013: disable payment when pass is used

								OcpMessageBox( OCPMSG_PTRIP_HRT_UPGRADE );
							}
							break;

						case 1:
							if ( EnableConcessionChk )
							{
								m_nOriginFareClass = ORDINARY_ORDINARY;		// williamto 30Aug2013: FAT failure *********, regard leg 1 as ordinary class ordinary ferry only
								PaymentMethod = PayCsc;
								FareType = ( IsDeluxe ) ? CRT_UPGRADE : ConcessionCheck;
								if (IsDeluxe)
								{
									RadioDeluxe.EnableWindow(TRUE);
								}
								RadioCash.SetCheck( FALSE );		// williamto 19Dec2012: payment not assumed in CSC but in cash
								//RadioCash.SetColorToWindowsDefault(TRUE);
								RadioCash.SetTextColor((COLORREF)-1);
								RadioCash.SetFaceColor((COLORREF)-1);
								RadioPurse.SetCheck( TRUE );
								RadioPurse.SetTextColor(RGB_BLACK);
								RadioPurse.SetFaceColor(RGB_PALEGREEN, 1);
								RadioCash.EnableWindow(FALSE);		// williamto 29Aug2013: disable payment when pass is used

								OcpMessageBox( OCPMSG_PTRIP_HRT_UPGRADE );
							}
							break;

						case 2: 
							// exhausted
							break;
						}

					}
					else
					{
						PaymentMethod = PayCash;
						RadioCash.EnableWindow(TRUE);
						RadioCash.SetCheck(TRUE);
						//RadioCash.SetColor( RGB_BLACK, RGB_PALEGREEN );
						RadioCash.SetTextColor(RGB_BLACK);
						RadioCash.SetFaceColor(RGB_PALEGREEN);
						RadioPurse.EnableWindow(TRUE);
						RadioPurse.SetCheck(FALSE/*TRUE*/);			// williamto 19Dec2012: payment not assumed in CSC but in cash
						//RadioPurse.SetColorToWindowsDefault(TRUE);
						RadioPurse.SetFaceColor((COLORREF)-1);
						RadioPurse.SetTextColor((COLORREF)-1);
					}
					
					AddItem( HyfPatronClass, IsDeluxe, 1, FareCalc.GetFare() );

					UpdateData(FALSE);
				}
				else if (res ==  CHyfFareCalculation::TransactionNotAllowed)
				{
				  /* PwD Senior FFS */
					ProcessError(TransactionNotAllowed);
				}
				else
				{
					ProcessError(FareCalculationError);
				}
				break;

			case GroupNormal:
				// group tickets
		/* PwD Senior FFS */
		/* Csc.Refresh() already */
		FareCalc.CalculatePatronClass(&Csc.HyfInfo, &Csc.GeneralInfo, &Csc.PersonalInfo);
		if (FareCalc.IsPwDSeniorFlatFareEnabled())
			if (FareCalc.EvaluatePwDSeniorFlatFare(FALSE) == Ok)
			{
#if 0  /* reject regardless FerryType */
				if (HyfTrip.GetFerryType() == FERRY_ORDINARY)
				{
					  int i;
					  m_AllowDeluxeSalesOnly = TRUE;
					  /* resume on if selected */
					  UpdateData(TRUE);
					  for (i=0; i<12; i++)
					  {
						m_OrdinaryPatrons[i] = 0;
						if (m_TypeSelected[i] == 1)
						EnablePatronEdits(MapPatronIndexToClass(i), TRUE, FALSE);
					else
						EnablePatronEdits(MapPatronIndexToClass(i), FALSE, FALSE);
			  }
					  UpdateData(FALSE);
				}
				else
#endif  /* #if0 */
				{
			/* FERRY_HOVER */
						ProcessError(TransactionNotAllowed);
						break;
					}
			}
		/* end PwD Senior FFS */
		
		/* Silver Age PTFCS 15-June-2021*/
		if (FareCalc.IsPwDSilverAgeFlatFareEnabled()) {
			if (FareCalc.EvaluatePwDSilverAgeFlatFare(FALSE) == Ok) {
				ProcessError(TransactionNotAllowed);
				break;
			}
		}
		/* End of Silver Age PTFCS 15-June-2021*/

				PaymentMethod = PayCsc;
				RadioPurse.EnableWindow(TRUE);
				RadioCash.SetCheck(FALSE);
				//RadioCash.SetColorToWindowsDefault(TRUE);
				RadioCash.SetTextColor((COLORREF)-1);
				RadioCash.SetFaceColor((COLORREF)-1);
				RadioPurse.SetCheck(TRUE);
				//RadioPurse.SetColor( RGB_BLACK, RGB_PALEGREEN );
				RadioPurse.SetTextColor(RGB_BLACK);
				RadioPurse.SetFaceColor(RGB_PALEGREEN);
				break;

			case Freight:
				PaymentMethod = PayCsc;
				RadioPurse.EnableWindow(TRUE);
				RadioCash.SetCheck(FALSE);
				//RadioCash.SetColorToWindowsDefault(TRUE);
				RadioCash.SetTextColor((COLORREF)-1);
				RadioCash.SetFaceColor((COLORREF)-1);
				RadioPurse.SetCheck(TRUE);
				//RadioPurse.SetColor( RGB_BLACK, RGB_PALEGREEN );
				RadioPurse.SetTextColor(RGB_BLACK);
				RadioPurse.SetFaceColor(RGB_PALEGREEN);
				break;

			default:
				break;
		}
	}
	// re-enable the window
	::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_ISSUE_TICKET), TRUE);
	::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_CSC), TRUE);

	m_DoingCscOperation = FALSE;
}


//
// for:
// group fares normal
// group fares pre-paid (reserved)
//

int CPostHyfProcessTrip::ProcessGroupFares(int Generate, int GroupSingleTicket, int *OrdinaryPatrons, int *DeluxePatrons, BOOL AutoPayDone)
{
	CHyfFareCalculation FareCalc;
	int                 PatronClass;
	int                 HyfPatronClass;
	int                 MappedPatronClass;
	int                 Count, Patrons;
	CCurrency           TempFare;
	CCurrency           NewPurse;
	CCurrency			IntermediatePurse;
	int                 ReturnValue = TRUE;
	int                 TransType = 0;
	int                 FreeFare = 0;
	//BOOL                AutoPayDone = FALSE;
	CHyfTrip			HyfTrip;
	CCurrency           OrdinaryFare[12], DeluxeFare[12];
	CCurrency           OrdinaryFareTotal, DeluxeFareTotal;

	CMachineId          LAVMachineAfterTxn;
	CCscLogicalId       CscLogicalIdAfterTxn;

	*OrdinaryPatrons = 0;
	*DeluxePatrons   = 0;

	Fare = CCurrency(0, 0);

	if (FareType == GroupPrePaid)
		TransType = POST_TRANS_PREGROUP;
	else if (FareType == Complementary)		// 29Jan2013: include complementary fare into processing
		TransType = POST_TRANS_COMP_USAGE;
	else if ( FareType == GroupNormal )
		TransType = POST_TRANS_GROUP;
	else if ( FareType == ConcessionSale )
		TransType = POST_TRANS_CONSSALE;
	else if ( FareType == ConcessionCheck )
		TransType = POST_TRANS_CONS_CHECK;
	else if ( FareType ==  CRT_UPGRADE )
		TransType = POST_CRT_UPGRADE;
	else 
		TransType = POST_TRANS_FARE;

	OrdinaryFareTotal = 0;
	DeluxeFareTotal = 0;

	if (Generate)
	{
	  if (PaymentMethod == PayCsc)
	  {
		if ( Csc.IsDataValid() /*&& !Csc.fIncomplete*/ )	// williamto 12Nov2013 FAT observation *******, unconfirmed transactions need to generate the logical ID too
		{
		  IntermediatePurse = NewPurse = Csc.GeneralInfo.PurseValue;
		  LAVMachineAfterTxn = CMachineId(Csc.GeneralInfo.LastAvMachineId);
		  CscLogicalIdAfterTxn = Csc.GetLogicalCscId();
		}
	  }
	}

	if (!Generate)
	{
		FareCalcTime     = CDateTime::GetCurrentTime();
		Fare             = 0;
		PassUsed         = 0;
		TripIndex        = 0;
		PatronClass      = 0;
		HyfPatronClass   = HYF_ADULT;
	}

	CFareCalculation::Status_t FareCalcStatus = CHyfFareCalculation::Ok;
				
	CWaitCursor waitCursor;		// set a wait cursor for potentially lengthy ticket issue operation

	CNonVolatile nv;
	if ((Generate) && (FareType == ConcessionSale || FareType == ConcessionCheck || FareType == CRT_UPGRADE) && (HrtPaymentMethod == HrtPayCash))	// only delete last HRT record when issuing HRT tickets in CASH
	{	
		nv.Read(&(this->mHrtReprintReceipts), CNonVolatile::POST_HRT_RECEIPT, CNonVolatile::READ_LOCK);
		this->mHrtReprintReceipts.clear();
		nv.Write(&(this->mHrtReprintReceipts));
	}
	
	for (Count = 0; Count < (int)mItems.size(); Count++)	// williamto 05Dec2013: [FAT][POST][TF 4.28.14] group ticket now supports arbituary input of patron-travel type
	{
		if  ( /*m_TypeSelected[Count] == 1*/ mItems[Count].GetItemId()  )
		{
			// then we have a fare(s) here
			MappedPatronClass = mItems[Count].GetItemId(); //MapPatronIndexToClass(Count);

			if ( /*m_OrdinaryPatrons[Count]*/ mItems[Count].GetItemQty() && !mItems[Count].GetDeluxeTicket() )
			{
				*OrdinaryPatrons += mItems[Count].GetItemQty(); //m_OrdinaryPatrons[Count];

				if ( FareType == ConcessionSale )
				{
					TransType = POST_TRANS_CONSSALE;

					FareCalcStatus = FareCalc.GetConcessionReturnFare(HyfTrip.GetOriginTerminal(), HyfTrip.GetDestinationTerminal(), 
						HyfTrip.GetFerryType(), FALSE, MappedPatronClass);
				}
				else if (FareType == ConcessionCheck || (FareType == CRT_UPGRADE && !mItems[Count].GetDeluxeTicket()))
				{
					if (FareType == CRT_UPGRADE && !mItems[Count].GetDeluxeTicket())
					{
						FareType = ConcessionCheck;
						IsDeluxe = FALSE;
					}
					
					TransType = POST_TRANS_CONS_CHECK;

					FareCalcStatus = FareCalc.CalculateCrtUpgrade(MappedPatronClass, 
						HyfTrip.GetSailingTime(), 
						HyfTrip.GetOriginTerminal(), 
						HyfTrip.GetDestinationTerminal(), 
						ORDINARY_ORDINARY, 
						gEod.HyfGetFareClass(HyfTrip.GetFerryType(), mItems[Count].GetDeluxeTicket()));
				}
				else  
				{
					//
					// Calculate the fare; note that the fare remains
					//
					FareCalcStatus = FareCalc.CalculateFare(MappedPatronClass, 
						HyfTrip.GetSailingTime(), 
						HyfTrip.GetOriginTerminal(), 
						HyfTrip.GetDestinationTerminal(), 
						HyfTrip.GetFerryType(), 
						FALSE);
				}
				if ( FareCalcStatus == CHyfFareCalculation::Ok )
				{

					this->CurrentFareSchema = FareCalc.GetCurrentSchema();
					this->CurrentFareGroup = FareCalc.GetCurrentFareGroup();
					if (FareType == GroupPrePaid)
					{
						TempFare = 0;
						FreeFare = 4;   // Show detail on receipts.
					}
					else if (FareType == Complementary)
					{	
						TempFare = 0;
						FreeFare = 2;	// show complementary ticket on receipts.
					}
					else if ( FareType == ConcessionCheck )
					{
						TempFare = 0;
						FreeFare = 3;
					}
					else
					{
						// Get the fare
						TempFare = FareCalc.GetFare();
					}

					if (Generate)
					{
						//
						// For each patron we must generate a ticket.
						// Note that if a CSC was used for payment the
						// CSC serial number must be written on all
						// the tickets.
						//
						this->CurrentFareSchema = FareCalc.GetCurrentSchema();
						this->CurrentFareGroup = FareCalc.GetCurrentFareGroup();
						HyfPatronClass = FareCalc.GetHyfPatronClass();
						PatronClass    = FareCalc.GetPatronClass();
						FareIndicator  = FareCalc.GetFareIndicator();

						// work out the total fare
						CCurrency NewFare = CCurrency(TempFare.GetValueIn10th() * (unsigned char) mItems[Count].GetItemQty())/*m_OrdinaryPatrons[Count]*/;
						OrdinaryFare[Count]  = NewFare;
						OrdinaryFareTotal += NewFare;

						// Generate the UD
						if (PaymentMethod == PayCsc)
						{
							if( Csc.IsAutopayUsed() && !AutoPayDone && Csc.GetAutopayAmount() )
							{
								CDateTime Now;

								Csc.GeneralInfo.TransactionNum +=1;
								GenerateAutopayUd();
//                               NewPurse = (Csc.GeneralInfo.PurseValue + Csc.GeneralInfo.DirectDebit) - NewFare;
								NewPurse += Csc.GeneralInfo.DirectDebit;
								IntermediatePurse = NewPurse;
								NewPurse -= NewFare;
								AutoPayDone = TRUE;

								Now = CDateTime::GetCurrentTime();
								LAVMachineAfterTxn = CMachineId(gEod.MachineId());
								CscLogicalIdAfterTxn = CCscLogicalId(Csc.InvalidCsc.lnSerialNo, 
									AUTOPAY_AV, 
									Now.GetDayOfYear(), 
									Csc.GeneralInfo.AddValueAccum+1);
							}
							else
							{
//                                NewPurse = Csc.GeneralInfo.PurseValue - NewFare;
								NewPurse -= NewFare;
							}


							COcpEventDataCscPostTransCsc EventCsc;
							EventCsc.SetConfirmationState( !(BOOL)Csc.fIncomplete );
							EventCsc.Set(TransType, CscLogicalIdAfterTxn, 
								FareIndicator, HyfPatronClass, PatronClass, 
								Csc.GeneralInfo.TransactionNum, NewFare, NewPurse, LAVMachineAfterTxn, 
								HyfTrip.GetRouteCode(), HyfTrip.GetFerryType(), 
								mItems[Count].GetItemQty()/*m_OrdinaryPatrons[Count]*/, 
								Csc.GeneralInfo.IssuerId);
							EventCsc.Commit();
						}
						else
						{
							COcpEventDataCscPostTransCash EventCash;

							if ((FareType == ConcessionSale || FareType == CRT_UPGRADE) && HrtPaymentMethod == HrtPayCash)
							{
								//RegistrationNumberGenerator.CommitRegistrationNumber(vecHrtRegistrationNumberVector[Count]);
								
								EventCash.Set(TransType, HyfPatronClass, PatronClass, FareIndicator, NewFare, 
									HyfTrip.GetRouteCode(), 
									HyfTrip.GetFerryType(), 
									mItems[Count].GetItemQty(), 
									vecHrtRegistrationNumberVector[Count]);
							}
							else
							{
								EventCash.Set(TransType, HyfPatronClass, PatronClass, FareIndicator, NewFare, 
									HyfTrip.GetRouteCode(), 
									HyfTrip.GetFerryType(), 
									mItems[Count].GetItemQty());
							}
							EventCash.Commit();
						}

						if (!GroupSingleTicket)
						{
							if ( Status == Ok )	// only print if Status=Ok
							{
								for (Patrons = 0; Patrons < mItems[Count].GetItemQty()/*m_OrdinaryPatrons[Count]*/; Patrons++)
								{
									IntermediatePurse -= TempFare;
									CPostReportPrn      Prn;

									if ( FareType == ConcessionSale )
									{
										// we only redirect to the HRT printer if paid by cash therefore have registration numbers
										if (HrtPaymentMethod == HrtPayCash)
										{
											CPostHrtPrn		HrtPrn(gEod.HrtPrinterName());

											DWORD			HrtTicketSeqNo = 0;
											CNonVolatile nv;
											CHrtReceiptNumber   HrtReceiptNumber;     // Class to access audit file where receipt number is held.
											nv.Read(&HrtReceiptNumber, CNonVolatile::POST_HRT_RECEIPT_NUMBER, CNonVolatile::READ_LOCK);
											HrtTicketSeqNo = HrtReceiptNumber.Add(1);              // Increment receipt number.
											nv.Write(&HrtReceiptNumber);

											CPostHrtReceipt PostHrtReceipt(FALSE, (PaymentMethod == PayCsc), 
													mItems[Count].GetDeluxeTicket(), 
													TempFare, 
													Csc.InvalidCsc.lnSerialNo, 
													HrtTicketSeqNo, 
													((PaymentMethod == PayCsc) ? Csc.InvalidCsc.LanguageSel : 0), 
													Csc.InvalidCsc.nConcession, 
													HyfPatronClass, 
													HyfTrip.GetOriginGroup(), 
													HyfTrip.GetDestinationGroup(), 
													HyfTrip.GetFerryType(), 
													gEod.MachineId(), 
													FareCalcTime, 
													HyfTrip.GetSailingTime(), 
													(mItems.size() > 1)  ? TempFare : mRecv,  
													(mItems.size() > 1 ) ? CCurrency(0,0) : mChange, 	// williamto 25Mar2014: [webcall][POST][#12501, 2] show "exact fare" for individual tickets in group
													vecHrtRegistrationNumberVector[Count][Patrons].FullReg(),
													CurrentFareGroup);

											nv.Read(&(this->mHrtReprintReceipts), CNonVolatile::POST_HRT_RECEIPT, CNonVolatile::READ_LOCK);
											this->mHrtReprintReceipts.push_back(PostHrtReceipt);
											nv.Write(&(this->mHrtReprintReceipts));

											HrtPrn.PrintConcessionReturnReceipt(PostHrtReceipt);

											//// print the return trip voucher
											HrtPrn.PrintCrtReturnEntry(PostHrtReceipt);

											// print the deluxe class voucher, only for ordinary ferry deluxe class
											if ( (mItems[Count].GetDeluxeTicket() == TRUE) && (HyfTrip.GetFerryType() == FERRY_ORDINARY) )
											{
												HrtPrn.PrintCrtDeluxeEntry(PostHrtReceipt);
											}
										}
										else
										{
											Prn.PrintConcessionReturnReceipt((PaymentMethod == PayCsc), 
													FALSE, 
													TempFare, 
													Csc.InvalidCsc.lnSerialNo, 
													((PaymentMethod == PayCsc) ? Csc.InvalidCsc.LanguageSel : 0), 
													Csc.InvalidCsc.nConcession, 
													HyfPatronClass, 
													FareCalcTime, 
													(mItems.size() > 1)  ? TempFare : mRecv,  
													(mItems.size() > 1 ) ? CCurrency(0,0) : mChange);
										}
									}
									else if ( FareType == CRT_UPGRADE )
									{
										if (HrtPaymentMethod == HrtPayCash)
										{
											DWORD			HrtTicketSeqNo = 0;
											CNonVolatile nv;
											CHrtReceiptNumber   HrtReceiptNumber;     // Class to access audit file where receipt number is held.
											nv.Read(&HrtReceiptNumber, CNonVolatile::POST_HRT_RECEIPT_NUMBER, CNonVolatile::READ_LOCK);
											HrtTicketSeqNo = HrtReceiptNumber.Add(1);              // Increment receipt number.
											nv.Write(&HrtReceiptNumber);

											CPostHrtReceipt PostHrtReceipt(TRUE, (PaymentMethod == PayCsc), 
													mItems[Count].GetDeluxeTicket(), 
													TempFare, 
													Csc.InvalidCsc.lnSerialNo, 
													HrtTicketSeqNo, 
													((PaymentMethod == PayCsc) ? Csc.InvalidCsc.LanguageSel : 0), 
													Csc.InvalidCsc.nConcession, 
													HyfPatronClass, 
													HyfTrip.GetOriginGroup(), 
													HyfTrip.GetDestinationGroup(), 
													HyfTrip.GetFerryType(), 
													gEod.MachineId(), 
													FareCalcTime, 
													HyfTrip.GetSailingTime(), 
													(mItems.size() > 1)  ? TempFare : mRecv,  
													(mItems.size() > 1 ) ? CCurrency(0,0) : mChange, 	// williamto 25Mar2014: [webcall][POST][#12501, 2] show "exact fare" for individual tickets in group
													vecHrtRegistrationNumberVector[Count][Patrons].FullReg(),
													CurrentFareGroup);

											nv.Read(&(this->mHrtReprintReceipts), CNonVolatile::POST_HRT_RECEIPT, CNonVolatile::READ_LOCK);
											this->mHrtReprintReceipts.push_back(PostHrtReceipt);
											nv.Write(&(this->mHrtReprintReceipts));

											CPostHrtPrn		HrtPrn(gEod.HrtPrinterName());
											HrtPrn.PrintConcessionReturnReceipt(PostHrtReceipt);	// williamto 25Mar2014: [webcall][POST][#12501, 2] show "exact fare" for individual tickets in group

											// print the deluxe class voucher
											if ((mItems[Count].GetDeluxeTicket() == TRUE) && (HyfTrip.GetFerryType() == FERRY_ORDINARY))
											{
												HrtPrn.PrintCrtDeluxeEntry(PostHrtReceipt);
											}
										}
										else
										{
											Prn.PrintConcessionReturnReceipt((PaymentMethod == PayCsc), 
												IsDeluxe, 
												Fare, 
												Csc.InvalidCsc.lnSerialNo, 
												((PaymentMethod == PayCsc) ? Csc.InvalidCsc.LanguageSel : 0), 
												Csc.InvalidCsc.nConcession, 
												HyfPatronClass, 
												FareCalcTime, 
												(mItems.size() > 1)  ? TempFare : mRecv,  
												(mItems.size() > 1 ) ? CCurrency(0,0) : mChange );	// williamto 25Mar2014: [webcall][POST][#12501, 2] show "exact fare" for individual tickets in group
										}
									}
									else
									{
										if (FareType == ConcessionCheck && HrtPaymentMethod == HrtPayCash)
										{
											// no need to print anything according to NWFF
										}
										else
										{
											Prn.PrintSingleJourneyReceipt((PaymentMethod == PayCsc), 
												FALSE, 
												TempFare, 
												Csc.InvalidCsc.lnSerialNo, 
												(PaymentMethod == PayCsc) ? Csc.GetGeneralInfo()->LanguageSel : 0 , 
												this->CurrentFareGroup,//[webcall][#17247]Use fare group as index
												HyfPatronClass, 
												FareCalcTime, 
												/*mRecv*/TempFare, 
												/*mChange*/CCurrency(0,0),	// williamto 25Mar2014: [webcall][POST][#12501, 2] show "exact fare" for individual tickets in group
												(PaymentMethod == PayCsc && Csc.IsAutopayUsed() && !AutoPayDone && Csc.GetAutopayAmount()) ? Csc.GeneralInfo.DirectDebit : CCurrency(0, 0), 
												(PaymentMethod == PayCsc) ? IntermediatePurse : CCurrency(0, 0), 
												(PaymentMethod == PayCsc) ? Csc.GetGeneralInfo()->bSmartOctopus : FALSE, 
												FreeFare);
										}
									}
								}
							}
						}
					}  /* end of if(Generate) */
					else	// if not generate ud and selling HRT / upgrading HRT, generate the HRT registration numbers 
					{
						// 
					}
					//
					// Update the subtotals
					//
					Fare += CCurrency( (ULONG)( TempFare.GetValueIn10th() * (unsigned char)mItems[Count].GetItemQty()));
				}
				else
				{
					Status = FareCalculationError;
					ReturnValue = FALSE;
					break;
				}  /* end of if(FareCalc.CalculateFare()) */
			}  /* end of if(m_OrdinaryPatrons) */

			if ( /*m_DeluxePatrons[Count] > 0 */ mItems[Count].GetItemQty() && mItems[Count].GetDeluxeTicket() )
			{
				*DeluxePatrons += mItems[Count].GetItemQty(); //m_DeluxePatrons[Count];

				if ( FareType == ConcessionSale || (FareType == ConcessionCheck && !mItems[Count].GetDeluxeTicket()) )
				{
					FareCalcStatus = FareCalc.GetConcessionReturnFare(HyfTrip.GetOriginTerminal(), HyfTrip.GetDestinationTerminal(), 
						HyfTrip.GetFerryType(), TRUE, MappedPatronClass);
				}
				else if ( (FareType == ConcessionCheck && mItems[Count].GetDeluxeTicket() ) || FareType == CRT_UPGRADE )
				{
					if (FareType == ConcessionCheck && mItems[Count].GetDeluxeTicket() )
					{
						FareType = CRT_UPGRADE;
						TransType = POST_CRT_UPGRADE;
						IsDeluxe = TRUE;
					}
					FareCalcStatus = FareCalc.CalculateCrtUpgrade(MappedPatronClass, HyfTrip.GetSailingTime(), HyfTrip.GetOriginTerminal(), 
									HyfTrip.GetDestinationTerminal(), CrtOrgFareClass, CrtCurFareClass);
				}
				else  
				{
					FareCalc.CalculateFare(MappedPatronClass, 
										HyfTrip.GetSailingTime(), 
										HyfTrip.GetOriginTerminal(), 
										HyfTrip.GetDestinationTerminal(), 
										HyfTrip.GetFerryType(), TRUE );
				}

				if ( FareCalcStatus == CHyfFareCalculation::Ok) 
				{
					this->CurrentFareSchema = FareCalc.GetCurrentSchema();
					this->CurrentFareGroup = FareCalc.GetCurrentFareGroup();
					if (FareType == GroupPrePaid)
					{
						TempFare = 0;
						FreeFare = 4;   // Show detail on receipts.
					}
					else if (FareType == Complementary)
					{
						TempFare = 0;
						FreeFare = 2;	// 29Jan2013: process complementary tickets as groups
					}
					else if ( FareType == ConcessionCheck && !mItems[Count].GetDeluxeTicket() )
					{
						TempFare = 0;
						FreeFare = 3;
					}
					else
					{
						// Get the fare
						TempFare = FareCalc.GetFare();
					}

					if (Generate)
					{
						//
						// For each patron we must generate a ticket
						// Note that if a CSC was used for payment the CSC serial number must be
						// written on all the tickets.
						//
						this->CurrentFareSchema = FareCalc.GetCurrentSchema();
						this->CurrentFareGroup = FareCalc.GetCurrentFareGroup();
						HyfPatronClass = FareCalc.GetHyfPatronClass();
						PatronClass    = FareCalc.GetPatronClass();
						FareIndicator  = FareCalc.GetFareIndicator();

						// work out the total fare
						CCurrency NewFare = CCurrency(TempFare.GetValueIn10th() * (unsigned char) mItems[Count].GetItemQty())/*m_DeluxePatrons[Count]*/;
						DeluxeFare[Count] = NewFare;
						DeluxeFareTotal += NewFare;

						// generate the UD
						if (PaymentMethod == PayCsc)
						{
							if( Csc.IsAutopayUsed() && !AutoPayDone )
							{
								CDateTime Now;

								Csc.GeneralInfo.TransactionNum +=1;
								GenerateAutopayUd();
//                                NewPurse = (Csc.GeneralInfo.PurseValue + Csc.GeneralInfo.DirectDebit) - NewFare;
								NewPurse += Csc.GeneralInfo.DirectDebit;
								IntermediatePurse = NewPurse;
								NewPurse -= NewFare;

								AutoPayDone = TRUE;

								Now = CDateTime::GetCurrentTime();
								LAVMachineAfterTxn = CMachineId(gEod.MachineId());
								CscLogicalIdAfterTxn = CCscLogicalId(Csc.InvalidCsc.lnSerialNo, AUTOPAY_AV, Now.GetDayOfYear(), Csc.GeneralInfo.AddValueAccum+1);
							}
							else
							{
//                                NewPurse = Csc.GeneralInfo.PurseValue - NewFare;
								NewPurse -= NewFare;
							}


							COcpEventDataCscPostTransCsc EventCsc;
							EventCsc.SetConfirmationState( !(BOOL)Csc.fIncomplete );
							EventCsc.Set(TransType, CscLogicalIdAfterTxn, FareIndicator, HyfPatronClass, PatronClass, 
								Csc.GeneralInfo.TransactionNum, NewFare, NewPurse, LAVMachineAfterTxn, 
								HyfTrip.GetRouteCode(), HyfTrip.GetFerryType(), 
								/*m_DeluxePatrons[Count]*/mItems[Count].GetItemQty(), 
								Csc.GeneralInfo.IssuerId);
							EventCsc.Commit();
						}
						else
						{
							COcpEventDataCscPostTransCash EventCash;

							// NWFF says PTFSS applies to CRT upgrade as well
							// no need to generate another HRT reg number for HRT return trip since its already paid
							if ((FareType == ConcessionSale || FareType == CRT_UPGRADE) && HrtPaymentMethod == HrtPayCash)
							{
								//RegistrationNumberGenerator.CommitRegistrationNumber(vecHrtRegistrationNumberVector[Count]);

								EventCash.Set(TransType, HyfPatronClass, PatronClass, FareIndicator, NewFare, 
									HyfTrip.GetRouteCode(), 
									HyfTrip.GetFerryType(), 
									mItems[Count].GetItemQty(), 
									vecHrtRegistrationNumberVector[Count]);
							}
							else
							{
								EventCash.Set(TransType, HyfPatronClass, PatronClass, FareIndicator, NewFare, 
									HyfTrip.GetRouteCode(), 
									HyfTrip.GetFerryType(), 
									mItems[Count].GetItemQty());
							}
							EventCash.Commit();
						}

						if ( !GroupSingleTicket && Status == Ok /*&& ( PaymentMethod == PayCsc && !Csc.fIncomplete ) */ )
						{
							for (Patrons = 0; Patrons < /*m_DeluxePatrons[Count]*/mItems[Count].GetItemQty(); Patrons++)
							{
								IntermediatePurse -= TempFare;
								
								CPostReportPrn      Prn;

								if ( FareType == ConcessionSale )
								{
									if (HrtPaymentMethod == HrtPayCash)
									{
										DWORD			HrtTicketSeqNo = 0;
										CNonVolatile nv;
										CHrtReceiptNumber   HrtReceiptNumber;     // Class to access audit file where receipt number is held.
										nv.Read(&HrtReceiptNumber, CNonVolatile::POST_HRT_RECEIPT_NUMBER, CNonVolatile::READ_LOCK);
										HrtTicketSeqNo = HrtReceiptNumber.Add(1);              // Increment receipt number.
										nv.Write(&HrtReceiptNumber);

										CPostHrtReceipt PostHrtReceipt(FALSE, (PaymentMethod == PayCsc), 
												mItems[Count].GetDeluxeTicket(), 
												TempFare, 
												Csc.InvalidCsc.lnSerialNo, 
												HrtTicketSeqNo, 
												((PaymentMethod == PayCsc) ? Csc.InvalidCsc.LanguageSel : 0), 
												Csc.InvalidCsc.nConcession, 
												HyfPatronClass, 
												HyfTrip.GetOriginGroup(), 
												HyfTrip.GetDestinationGroup(), 
												HyfTrip.GetFerryType(), 
												gEod.MachineId(), 
												FareCalcTime, 
												HyfTrip.GetSailingTime(), 
												(mItems.size() > 1)  ? TempFare : mRecv,  
												(mItems.size() > 1 ) ? CCurrency(0,0) : mChange, 	// williamto 25Mar2014: [webcall][POST][#12501, 2] show "exact fare" for individual tickets in group
												vecHrtRegistrationNumberVector[Count][Patrons].FullReg(),
												CurrentFareGroup);

										nv.Read(&(this->mHrtReprintReceipts), CNonVolatile::POST_HRT_RECEIPT, CNonVolatile::READ_LOCK);
										this->mHrtReprintReceipts.push_back(PostHrtReceipt);
										nv.Write(&(this->mHrtReprintReceipts));

										CPostHrtPrn HrtPrn(gEod.HrtPrinterName());

										HrtPrn.PrintConcessionReturnReceipt(PostHrtReceipt);

										//// print the return trip voucher
										HrtPrn.PrintCrtReturnEntry(PostHrtReceipt);

										// print the deluxe class voucher
										if ((mItems[Count].GetDeluxeTicket() == TRUE) && (HyfTrip.GetFerryType() == FERRY_ORDINARY))
										{
											HrtPrn.PrintCrtDeluxeEntry(PostHrtReceipt);
										}
									}
									else
									{
										Prn.PrintConcessionReturnReceipt((PaymentMethod == PayCsc), 
												TRUE, 
												TempFare, 
												Csc.InvalidCsc.lnSerialNo, 
												((PaymentMethod == PayCsc) ? Csc.InvalidCsc.LanguageSel : 0), 
												Csc.InvalidCsc.nConcession, 
												HyfPatronClass, 
												FareCalcTime, 
												(mItems.size() > 1)  ? TempFare : mRecv,  
												(mItems.size() > 1 ) ? CCurrency(0,0) : mChange); 	// williamto 25Mar2014: [webcall][POST][#12501, 2] show "exact fare" for individual tickets in group
									}
								}
								else if ( FareType == CRT_UPGRADE )
								{
									if (HrtPaymentMethod == HrtPayCash)
									{
										DWORD			HrtTicketSeqNo = 0;
										CNonVolatile nv;
										CHrtReceiptNumber   HrtReceiptNumber;     // Class to access audit file where receipt number is held.
										nv.Read(&HrtReceiptNumber, CNonVolatile::POST_HRT_RECEIPT_NUMBER, CNonVolatile::READ_LOCK);
										HrtTicketSeqNo = HrtReceiptNumber.Add(1);              // Increment receipt number.
										nv.Write(&HrtReceiptNumber);

										CPostHrtReceipt PostHrtReceipt(TRUE, (PaymentMethod == PayCsc), 
												mItems[Count].GetDeluxeTicket(), 
												TempFare, 
												Csc.InvalidCsc.lnSerialNo, 
												HrtTicketSeqNo, 
												((PaymentMethod == PayCsc) ? Csc.InvalidCsc.LanguageSel : 0), 
												Csc.InvalidCsc.nConcession, 
												HyfPatronClass, 
												HyfTrip.GetOriginGroup(), 
												HyfTrip.GetDestinationGroup(), 
												HyfTrip.GetFerryType(), 
												gEod.MachineId(), 
												FareCalcTime, 
												HyfTrip.GetSailingTime(), 
												(mItems.size() > 1)  ? TempFare : mRecv,  
												(mItems.size() > 1 ) ? CCurrency(0,0) : mChange, 	// williamto 25Mar2014: [webcall][POST][#12501, 2] show "exact fare" for individual tickets in group
												vecHrtRegistrationNumberVector[Count][Patrons].FullReg(),
												CurrentFareGroup);

										nv.Read(&(this->mHrtReprintReceipts), CNonVolatile::POST_HRT_RECEIPT, CNonVolatile::READ_LOCK);
										this->mHrtReprintReceipts.push_back(PostHrtReceipt);
										nv.Write(&(this->mHrtReprintReceipts));

										CPostHrtPrn HrtPrn(gEod.HrtPrinterName());

										HrtPrn.PrintConcessionReturnReceipt(PostHrtReceipt);	// williamto 25Mar2014: [webcall][POST][#12501, 2] show "exact fare" for individual tickets in group

										// print the deluxe class voucher
										if ((mItems[Count].GetDeluxeTicket() == TRUE) && (HyfTrip.GetFerryType() == FERRY_ORDINARY))
										{
											HrtPrn.PrintCrtDeluxeEntry(PostHrtReceipt);
										}
									}
									else
									{
										Prn.PrintCrtUpgradeReceipt((PaymentMethod == PayCsc), 
											IsDeluxe, 
											TempFare, 
											Csc.InvalidCsc.lnSerialNo, 
											((PaymentMethod == PayCsc) ? Csc.InvalidCsc.LanguageSel : 0), 
											Csc.InvalidCsc.nConcession, 
											HyfPatronClass, 
											FareCalcTime, 
											(mItems.size() > 1)  ? TempFare : mRecv,  
											(mItems.size() > 1 ) ? CCurrency(0,0) : mChange );	// williamto 25Mar2014: [webcall][POST][#12501, 2] show "exact fare" for individual tickets in group
									}
								}
								else
								{
									Prn.PrintSingleJourneyReceipt(
										(PaymentMethod == PayCsc), 
										TRUE, 
										TempFare, 
										Csc.InvalidCsc.lnSerialNo, 
										(PaymentMethod == PayCsc) ? Csc.InvalidCsc.LanguageSel : 0, 
										this->CurrentFareGroup,//[webcall][#17247]Use fare group as index
										HyfPatronClass, FareCalcTime, 
										/*mRecv*/TempFare, 
										/*mChange*/CCurrency(0,0), 	// williamto 25Mar2014: [webcall][POST][#12501, 2] show "exact fare" for individual tickets in group
										(PaymentMethod == PayCsc && Csc.IsAutopayUsed() && !AutoPayDone && Csc.GetAutopayAmount()) ? Csc.GeneralInfo.DirectDebit : CCurrency(0, 0), 
										(PaymentMethod == PayCsc) ? IntermediatePurse : CCurrency(0, 0), 
										(PaymentMethod == PayCsc) ? Csc.GetGeneralInfo()->bSmartOctopus : FALSE,  
										FreeFare );
								}
							}
						}
					}  /* end of if(Generate) */
					else
					{
						//// NWFF says PTFSS applies to CRT upgrade as well
						//// no need to generate another HRT reg number for HRT return trip since its already paid
						//if ((FareType == ConcessionSale || FareType == CRT_UPGRADE) && HrtPaymentMethod == HrtPayCash)
						//{
						//	int qty = 0;
						//	CRegistrationNumber HrtRegistrationNumber;
						//	CRegistrationNumberGenerator RegistrationNumberGenerator(&gLogIO, gEod.ServiceProviderId(), gEod.HrtPostId());

						//	for (qty=0; qty<mItems[Count].GetItemQty(); qty++)	// for each ticket in the passenger group
						//	{
						//		// find the next registration number 
						//		if (RegistrationNumberGenerator.FindNextRegistrationNumber(HrtRegistrationNumber) == true)
						//		{
						//			ulHrtRegistrationNumberVector[Count].push_back(HrtRegistrationNumber.FullReg());
						//		}
						//		else
						//		{
						//			// return any generated HRT registration numbers to the pool
						//			for (std::vector<uint64_t>::iterator it = ulHrtRegistrationNumberVector[Count].begin(); it != ulHrtRegistrationNumberVector[Count].end(); it++)
						//			{
						//				RegistrationNumberGenerator.ReleaseRegistrationNumber(*it);
						//			}

						//			ulHrtRegistrationNumberVector[Count].clear();

						//			Status = HrtCashOutOfRegNumber;
						//			ReturnValue = FALSE;
						//			break;
						//		}
						//	}
						//}
					}
					//
					// Update the subtotals
					//
					Fare += CCurrency(TempFare.GetValueIn10th() * (unsigned char) /*m_DeluxePatrons[Count]*/ mItems[Count].GetItemQty());
				}
				else
				{
					Status = FareCalculationError;
					ReturnValue = FALSE;
					break;
				}  /*  end of if(FareCalc.CalculateFare() */
			}  /* end of if(m_DeluxePatrons) */

		}  /* end of if(m_TypeSelected) */
	}  /* end of for(Count) */

	if ((Generate) && (GroupSingleTicket) )
	{
		if (ReturnValue && Status == Ok )		// williamto 30Sept2013: FAT failure *******, stop printing invalid ticket if transaction status is not OK
		{
			CPostReportPrn      Prn;			// williamto 30Sept2013: FAT failure *******, move the CPostReportPrn constructor here to stop dummy ticket printing
			Prn.PrintSingleGroupReceiptPerClass(TransType, (PaymentMethod == PayCsc), FALSE,
											*OrdinaryPatrons, /*m_OrdinaryPatrons*/mItems, OrdinaryFare, OrdinaryFareTotal,
											Csc.InvalidCsc.lnSerialNo, ((PaymentMethod == PayCsc) ? Csc.InvalidCsc.LanguageSel : 0), 
											FareCalc.GetCurrentFareGroup(),//[webcall][#17247]
											(PaymentMethod == PayCsc) ? Csc.InvalidCsc.nConcession : HYF_ADULT,		 	// williamto 25Apr2014: [webcall][#12626][POST] fixed wrong patron type for CSC transaction
											FareCalcTime, 
											(*OrdinaryPatrons && *DeluxePatrons) ? OrdinaryFareTotal : mRecv,	// williamto 20May2014: [webcall][#12501][POST] show exact fare for group tickets
											(*OrdinaryPatrons && *DeluxePatrons) ? CCurrency(0, 0) : mChange,	// williamto 20May2014: [webcall][#12501][POST] show exact fare for group tickets
											(PaymentMethod == PayCsc && Csc.IsAutopayUsed() && !AutoPayDone && Csc.GetAutopayAmount()) ? Csc.GeneralInfo.DirectDebit : CCurrency(0, 0), 
											(PaymentMethod == PayCsc) ? NewPurse : CCurrency(0, 0), 
											(PaymentMethod == PayCsc) ? Csc.InvalidCsc.bSmartOctopus : 0, 
											FreeFare);

			Prn.PrintSingleGroupReceiptPerClass(TransType, (PaymentMethod == PayCsc), TRUE,
										  *DeluxePatrons, /*m_DeluxePatrons*/mItems, DeluxeFare, DeluxeFareTotal,
										  Csc.InvalidCsc.lnSerialNo, ((PaymentMethod == PayCsc) ? Csc.InvalidCsc.LanguageSel : 0), 
										  FareCalc.GetCurrentFareGroup(),//[webcall][#17247][POST] Use fare group as index into string table
										  (PaymentMethod == PayCsc) ? Csc.InvalidCsc.nConcession : HYF_ADULT,		// williamto 25Apr2014: [webcall][#12626][POST] fixed wrong patron type for CSC transaction
										  FareCalcTime, 
										  (*OrdinaryPatrons && *DeluxePatrons) ? DeluxeFareTotal : mRecv,		// williamto 20May2014: [webcall][#12501][POST] show exact fare for group tickets
										  (*OrdinaryPatrons && *DeluxePatrons) ? CCurrency(0, 0) : mChange,		// williamto 20May2014: [webcall][#12501][POST] show exact fare for group tickets
										  (PaymentMethod == PayCsc && Csc.IsAutopayUsed() && !AutoPayDone && Csc.GetAutopayAmount()) ? Csc.GeneralInfo.DirectDebit : CCurrency(0, 0), 
										  (PaymentMethod == PayCsc) ? NewPurse : CCurrency(0, 0), 
										  (PaymentMethod == PayCsc) ? Csc.InvalidCsc.bSmartOctopus : 0, 
										  (FareType == ConcessionCheck) ? 0 : FreeFare);	// exlucde free fare for ConcessionCheck & Deluxe class
		}
	}

	return ReturnValue;
}


//
// For:
// Complementary
// ConcessionSale
// ConcessionCheck
// SingleJourney
//
int CPostHyfProcessTrip::CalculateCrtFare()
{
	CHyfFareCalculation FareCalc;
	CHyfTrip			HyfTrip;

	int ReturnValue = TRUE;

	FareCalcTime     = CDateTime::GetCurrentTime();
	if (PaymentMethod == PayCsc)
	{
		if (FareCalc.GetConcessionReturnFare(HyfTrip.GetOriginTerminal(), HyfTrip.GetDestinationTerminal(), 
			HyfTrip.GetFerryType(), IsDeluxe, &Csc.GeneralInfo, &Csc.HyfInfo, &Csc.PersonalInfo) == CHyfFareCalculation::Ok)
		{
			this->CurrentFareSchema = FareCalc.GetCurrentSchema();
			this->CurrentFareGroup = FareCalc.GetCurrentFareGroup();
			Fare           = FareCalc.GetFare();
			HyfPatronClass = FareCalc.GetHyfPatronClass();
			PatronClass    = FareCalc.GetPatronClass();
			FareIndicator  = FareCalc.GetFareIndicator();
			Csc.IslandNum  = FareCalc.IslandNum;
			Csc.FareClass  = FareCalc.FareClass;
		}
		else
		{
			Status = InvalidPatronClassError;
			ReturnValue = FALSE;
		}
	}
	else
	{
		// Payment via cash
		HyfPatronClass = MapPatronIndexToClass(PatronIndex);
		if (FareCalc.GetConcessionReturnFare(HyfTrip.GetOriginTerminal(), HyfTrip.GetDestinationTerminal(), 
			HyfTrip.GetFerryType(), IsDeluxe, HyfPatronClass) == CHyfFareCalculation::Ok)
		{
			this->CurrentFareSchema = FareCalc.GetCurrentSchema();
			this->CurrentFareGroup = FareCalc.GetCurrentFareGroup();
			Fare           = FareCalc.GetFare();
			HyfPatronClass = FareCalc.GetHyfPatronClass();
			PatronClass    = FareCalc.GetPatronClass();
			FareIndicator  = FareCalc.GetFareIndicator();
		}
		else
		{
			Status = FareCalculationError;
			ReturnValue = FALSE;
		}
	}
	return ReturnValue;
}

int CPostHyfProcessTrip::CalculateCrtUpgrade(int OldFareClass, int NewFareClass)
{
	CHyfFareCalculation FareCalc;
	CHyfTrip			HyfTrip;

	int ReturnValue = TRUE;

	FareCalcTime     = CDateTime::GetCurrentTime();
	if (PaymentMethod == PayCsc)
	{
			if (FareCalc.CalculateCrtUpgrade(HyfTrip.GetSailingTime(), HyfTrip.GetOriginTerminal(), 
											 HyfTrip.GetDestinationTerminal(), OldFareClass, NewFareClass, 
											 &Csc.GeneralInfo, &Csc.HyfInfo, &Csc.PersonalInfo) == CHyfFareCalculation::Ok)
			{
				this->CurrentFareSchema = FareCalc.GetCurrentSchema();
				this->CurrentFareGroup = FareCalc.GetCurrentFareGroup();
				Fare           = FareCalc.GetFare();
				HyfPatronClass = FareCalc.GetHyfPatronClass();
				PatronClass    = FareCalc.GetPatronClass();
				FareIndicator  = FareCalc.GetFareIndicator();
			}
			else
			{
				Status = InvalidPatronClassError;
				ReturnValue = FALSE;
			}
	}
	else
	{
		// Payment via cash
		HyfPatronClass = MapPatronIndexToClass(PatronIndex);
		if ( FareCalc.CalculateCrtUpgrade(HyfPatronClass, HyfTrip.GetSailingTime(), HyfTrip.GetOriginTerminal(), 
						HyfTrip.GetDestinationTerminal(), OldFareClass, NewFareClass) == CHyfFareCalculation::Ok )
		{
			this->CurrentFareSchema = FareCalc.GetCurrentSchema();
			this->CurrentFareGroup = FareCalc.GetCurrentFareGroup();
			Fare           = FareCalc.GetFare();
			HyfPatronClass = FareCalc.GetHyfPatronClass();
			PatronClass    = FareCalc.GetPatronClass();
			FareIndicator  = FareCalc.GetFareIndicator();
		}
		else
		{
			Status = FareCalculationError;
			ReturnValue = FALSE;
		}
	}
	return ReturnValue;
}

int CPostHyfProcessTrip::CalculateSjFare()
{
	CHyfFareCalculation FareCalc;
	CHyfTrip
		HyfTrip;

	int		SJItemPatronClass;
	BOOL	SJItemIsDeluxe = FALSE;
	
	int ReturnValue = TRUE;

	FareCalcTime     = CDateTime::GetCurrentTime();
	if ( PaymentMethod == PayCsc || PaymentMethod == PayMp || PaymentMethod == PayDp )
	{
		// assume list position = 0 SJT item is the entered item, because SJT fare gets cleared
		vector<CPosTicketItem>::iterator it;
		for ( it = mItems.begin(); it < mItems.end(); it++ )
		{
			if( it->GetItemListPos() == 0)
			{
				SJItemPatronClass = it->GetItemId();
				SJItemIsDeluxe = it->GetDeluxeTicket();
				break;
			}
		}

		CFareCalculation::Status_t res;
		// we use the information from the CSC
			res = FareCalc.CalculateFare(HyfTrip.GetSailingTime(), HyfTrip.GetOriginTerminal(), 
				HyfTrip.GetDestinationTerminal(), HyfTrip.GetFerryType(), HyfTrip.GetHolidayNum(), 
				IsDeluxe, &Csc.GeneralInfo, &Csc.HyfInfo, &Csc.PersonalInfo);
			if (res ==  CHyfFareCalculation::Ok)
		{
			this->CurrentFareSchema = FareCalc.GetCurrentSchema();
			this->CurrentFareGroup = FareCalc.GetCurrentFareGroup();
			Fare           = FareCalc.GetFare();
			PassUsed       = FareCalc.GetPassUsed();
			TripIndex      = FareCalc.GetTripIndex();
			HyfPatronClass = FareCalc.GetHyfPatronClass();
			PatronClass    = FareCalc.GetPatronClass();
			FareIndicator  = FareCalc.GetFareIndicator();
			// YTAY extract holiday concession data
			Csc.IslandNum  = FareCalc.IslandNum;
			Csc.FareClass  = FareCalc.FareClass;
			if (FareIndicator & FS_HYF_HOLIDAY_DISCOUNT)
			{
				m_Discount = FareCalc.GetHolidayDiscount();
			}
// williamto 27Oct2013: FAT NTF ********, final fare calculation now checks for the CSC patron class against the input patron class, and re-calculates the actual fare as cash for electronic purse payment
			// if the CSC patron type is not the same as input patron type, then we should ignore any MT/DP on CSC and treat the CSC as electronic purse only
			if ( HyfPatronClass == SJItemPatronClass )
			{
				PaymentMethod = (PassUsed == 0) ? PayCsc : ( ( PassUsed == CHyfFareCalculation::MonthlyPass1 || PassUsed == CHyfFareCalculation::MonthlyPass2 ) ? PayMp : PayDp );	// williamto 06Nov2013: Payment method also includes CSC
				ModifyItem( 0, IsDeluxe, 1, Fare );		// the entered item is eligible for MT or DP, so modify it correspondingly
				mTotal = Fare;	// the fare calculated here is the finalised value
				CalcItemSubtotal();
				UpdateData(FALSE);
			}
			else	// williamto 11Nov2013: FAT NTF ********, also recalculate when the input patron class does not match the CSC's patron class as calculated
			{
				if (FareCalc.CalculateFare(SJItemPatronClass, HyfTrip.GetSailingTime(), HyfTrip.GetOriginTerminal(), HyfTrip.GetDestinationTerminal(), HyfTrip.GetFerryType(), SJItemIsDeluxe) == CHyfFareCalculation::Ok)
				{
					this->CurrentFareSchema = FareCalc.GetCurrentSchema();
					this->CurrentFareGroup = FareCalc.GetCurrentFareGroup();
					HyfPatronClass = FareCalc.GetHyfPatronClass();
					PatronClass    = FareCalc.GetPatronClass();
					Fare           = FareCalc.GetFare();
					FareIndicator  = FareCalc.GetFareIndicator();
					
					PaymentMethod = PayCsc;
					HyfPatronClass = SJItemPatronClass;
					mTotal = Fare;
					PassUsed = 0;
					TripIndex = 0;
				}
				else
				{
					Status = FareCalculationError;
					ReturnValue = FALSE;
				}				
			}
// williamto 27Oct2013: FAT NTF ********, final fare calculation now checks for the CSC patron class against the input patron class, and re-calculates the actual fare as cash for electronic purse payment
		}
		else if (res ==  CHyfFareCalculation::TransactionNotAllowed)
		{
			/* PwD Senior FFS */
			Status = TransactionNotAllowed;
			ReturnValue = FALSE;
		}
		else
		{
			Status = FareCalculationError;
			ReturnValue = FALSE;
		}
	}
	else
	{
		// Payment via cash
		HyfPatronClass = MapPatronIndexToClass(PatronIndex);
			if (FareCalc.CalculateFare(HyfPatronClass, HyfTrip.GetSailingTime(), HyfTrip.GetOriginTerminal(), HyfTrip.GetDestinationTerminal(), HyfTrip.GetFerryType(), IsDeluxe) == CHyfFareCalculation::Ok)
		{
			this->CurrentFareSchema = FareCalc.GetCurrentSchema();
			this->CurrentFareGroup = FareCalc.GetCurrentFareGroup();
			HyfPatronClass = FareCalc.GetHyfPatronClass();
			PatronClass    = FareCalc.GetPatronClass();
			Fare           = FareCalc.GetFare();
			FareIndicator  = FareCalc.GetFareIndicator();
		}
		else
		{
			Status = FareCalculationError;
			ReturnValue = FALSE;
		}
	}
	return ReturnValue;
}

int CPostHyfProcessTrip::GenerateAutopayUd()
{
	CDateTime                    Now = CDateTime::GetCurrentTime();
	CCscLogicalId                CscId(Csc.InvalidCsc.lnSerialNo, AUTOPAY_AV/*Csc.GeneralInfo.AddValueType*/, Now.GetDayOfYear(), Csc.GeneralInfo.AddValueAccum+1);
	COcpEventDataAutoPay         Autopay;
	CCurrency                    NewPurse;
	CMachineId                   LAVMachineB4Txn;

	LAVMachineB4Txn = CMachineId(Csc.GeneralInfo.LastAvMachineId);
	NewPurse = Csc.GeneralInfo.PurseValue + Csc.GeneralInfo.DirectDebit;
	Autopay.SetConfirmationState(!(BOOL)Csc.fIncomplete);
	Autopay.Set(CscId, Csc.GeneralInfo.TransactionNum - 1, Csc.GeneralInfo.DirectDebit, NewPurse, /*gEod.MachineId()*/LAVMachineB4Txn, Csc.GeneralInfo.IssuerId);
	Autopay.Commit();
	return TRUE;
}

int CPostHyfProcessTrip::GenerateSjUd()
{
	//CDiskPrn		DiskPrn;		// williamto 4Jan2013: reprint receipt
	CCurrency       NewPurse;
	int             TransType;
	int             FreeFare = 0;   // To pass details to receipt printer on free fares.
	CHyfTrip
		HyfTrip;

	switch (FareType)
	{
		case Complementary:
			TransType = POST_TRANS_COMP_USAGE;
			break;

		case ConcessionCheck:
			TransType = POST_TRANS_CONS_CHECK;
			break;

		case ConcessionSale:
			TransType = POST_TRANS_CONSSALE;
			break;

		case SingleJourney:
			TransType = POST_TRANS_FARE;
			break;

		case CRT_UPGRADE:
			TransType = POST_CRT_UPGRADE;
			break;
		default:
			break;
	}

	if (PaymentMethod == PayCash)
	{
		COcpEventDataCscPostTransCash EventCash;
		EventCash.Set(TransType, HyfPatronClass, PatronClass, FareIndicator, Fare, HyfTrip.GetRouteCode(), HyfTrip.GetFerryType(), 1);
		EventCash.Commit();
	}
	else
	{
		if (PaymentMethod == PayCsc)
		{
		  CMachineId    LAVMachineAfterTxn;
		  CCscLogicalId CscLogicalIdAfterTxn;

			if (Csc.IsAutopayUsed())
			{
				// autopay occurred
				CDateTime Now;

				Csc.GeneralInfo.TransactionNum +=1;
				GenerateAutopayUd();
				NewPurse = (Csc.GeneralInfo.PurseValue + Csc.GeneralInfo.DirectDebit) - Fare;

				Now = CDateTime::GetCurrentTime();
				LAVMachineAfterTxn = CMachineId(gEod.MachineId());
				CscLogicalIdAfterTxn = CCscLogicalId(Csc.InvalidCsc.lnSerialNo, AUTOPAY_AV, Now.GetDayOfYear(), Csc.GeneralInfo.AddValueAccum+1);
			}
			else
			{
				NewPurse = Csc.GeneralInfo.PurseValue - Fare;

				LAVMachineAfterTxn = CMachineId(Csc.GeneralInfo.LastAvMachineId);
				CscLogicalIdAfterTxn = Csc.GetLogicalCscId();
			}
			COcpEventDataCscPostTransCsc EventCsc;
			EventCsc.SetConfirmationState( !(BOOL)Csc.fIncomplete );
			EventCsc.Set(TransType, CscLogicalIdAfterTxn, FareIndicator, HyfPatronClass, PatronClass, Csc.GeneralInfo.TransactionNum, Fare, NewPurse, LAVMachineAfterTxn, HyfTrip.GetRouteCode(), HyfTrip.GetFerryType(), 1, Csc.GeneralInfo.IssuerId);

			if (FareIndicator & FS_HYF_HOLIDAY_DISCOUNT)
			{
				EventCsc.SetDiscountData(m_Discount,
										 gEod.ServiceProviderId());
			}
			EventCsc.Commit();
		}
		else	// pay by MT/DP
		{
			COleDateTimeSpan ValidityPeriod(Csc.HyfInfo.HyfPass1ValidityPeriod, 0, 0, 0);
			CDateTime        EndDate = Csc.HyfInfo.HyfPass1StartDate + ValidityPeriod;

			// Faretype == PayMp which requires different ud
			if ( /*RadioDeluxe.GetCheck() == 1*/IsDeluxe && PassUpgradePaymentMethod == PayCash )
			{
				// Deluxe upgrade by cash
				COcpEventDataCscPassUsageCash PassCash;
				PassCash.Set(Csc.GetLogicalCscId(),
					Csc.GeneralInfo.TransactionNum,
					Fare,
					EndDate,
					HyfTrip.GetRouteCode(),
					(PassUsed == CHyfFareCalculation::MonthlyPass2 ) ? 2 : PassUsed,  
					Csc.GeneralInfo.LastAvMachineId,
					TRC_DELUXE,
					PatronClass,
					HyfTrip.GetFerryType(),
					Csc.GeneralInfo.IssuerId);
				PassCash.Commit();
			}
			else
			{
			  CMachineId    LAVMachineAfterTxn;
			  CCscLogicalId CscLogicalIdAfterTxn;

				// Deluxe upgrade or normal pass usage
				if (Csc.IsAutopayUsed())
				{
					// autopay occurred
					CDateTime Now;

					Csc.GeneralInfo.TransactionNum +=1;
					GenerateAutopayUd();
					NewPurse = (Csc.GeneralInfo.PurseValue + Csc.GeneralInfo.DirectDebit) - Fare;

					Now = CDateTime::GetCurrentTime();
					LAVMachineAfterTxn = CMachineId(gEod.MachineId());
					CscLogicalIdAfterTxn = CCscLogicalId(Csc.InvalidCsc.lnSerialNo, AUTOPAY_AV, Now.GetDayOfYear(), Csc.GeneralInfo.AddValueAccum+1);
				}
				else
				{
					NewPurse = Csc.GeneralInfo.PurseValue - Fare;

					LAVMachineAfterTxn = CMachineId(Csc.GeneralInfo.LastAvMachineId);
					CscLogicalIdAfterTxn = Csc.GetLogicalCscId();
				}
				COcpEventDataCscPassUsage PassCsc;
				PassCsc.SetConfirmationState( !(BOOL)Csc.fIncomplete );
				PassCsc.Set(CscLogicalIdAfterTxn,
					Csc.GeneralInfo.TransactionNum,
					Fare,
					NewPurse,
					EndDate,
					HyfTrip.GetRouteCode(),
					(PassUsed == CHyfFareCalculation::MonthlyPass2 ) ? 2 : PassUsed,  
					LAVMachineAfterTxn,
					(IsDeluxe) ? TRC_DELUXE : TRC_NORMAL,
					PatronClass,
					HyfTrip.GetFerryType(),
					Csc.GeneralInfo.IssuerId);
				PassCsc.Commit();
			}
		}
	}

	// print a ticket
	if ( PaymentMethod == PayCash || ( ( PaymentMethod == PayCsc || PaymentMethod == PayMp || PaymentMethod == PayDp ) && !Csc.fIncomplete ) )	// williamto 19Aug2013: FAT failure *********, always print for cash payment and confirmed CSC payment
	{
		CPostReportPrn  Prn;	// williamto 28Aug2013: this is not good practice, but CPostReportPrn class initialises the printer when initialising an instance of its own and that causes printer to be used even when no ticket is printed
		
		switch (FareType)
		{
			case Complementary:
				FreeFare = 2;
				Prn.PrintSingleJourneyReceipt(FALSE, 
					IsDeluxe, 
					0, 
					0, 
					0, 
					this->CurrentFareGroup,//[webcall][#17247]Use fare group as index
					HyfPatronClass, 
					FareCalcTime, 
					mRecv, 
					mChange, 
					Csc.IsAutopayUsed() ? Csc.GeneralInfo.DirectDebit : CCurrency(0, 0), 
					NewPurse, 
					FALSE, 
					FreeFare);
				break;

			case ConcessionCheck:
				FreeFare = 3;
				Prn.PrintSingleJourneyReceipt(FALSE, IsDeluxe, 
					0, 
					0, 
					0, 
					this->CurrentFareGroup,//[webcall][#17247]Use fare group as index
					HyfPatronClass, 
					FareCalcTime, 
					mRecv, 
					mChange, 
					Csc.IsAutopayUsed() ? Csc.GeneralInfo.DirectDebit : CCurrency(0, 0), 
					NewPurse, 
					FALSE, 
					FreeFare);
				break;

			case CRT_UPGRADE:
				Prn.PrintCrtUpgradeReceipt(
					(PaymentMethod == PayCsc), 
					IsDeluxe, 
					Fare, 
					Csc.InvalidCsc.lnSerialNo, 
					((PaymentMethod == PayCsc) ? Csc.InvalidCsc.LanguageSel : 0), 
					PatronClass, 
					HyfPatronClass, 
					FareCalcTime, 
					mRecv, 
					mChange);
				break;

			case ConcessionSale:
				Prn.PrintConcessionReturnReceipt(
					(PaymentMethod == PayCsc), 
					IsDeluxe, 
					Fare, 
					Csc.InvalidCsc.lnSerialNo, 
					((PaymentMethod == PayCsc) ? Csc.InvalidCsc.LanguageSel : 0), 
					PatronClass, 
					HyfPatronClass, 
					FareCalcTime, 
					mRecv, 
					mChange);
				break;

			case SingleJourney:
				if (PassUsed && IsDeluxe)
				{
					// note that the first parameter indicates
					// the fact that we did a deluxe upgrade
					// by cash if false.
					Prn.PrintDeluxeUpgradeReceipt( 
						( PassUpgradePaymentMethod == PayCash ) ? FALSE : TRUE, 
						Fare, 
						Csc.InvalidCsc.lnSerialNo, 
						Csc.InvalidCsc.LanguageSel, 
						HyfPatronClass, 
						FareCalcTime, 
						mRecv, 
						mChange, 
						Csc.IsAutopayUsed() ? Csc.GeneralInfo.DirectDebit : CCurrency(0, 0), 
						NewPurse, 
						Csc.GetGeneralInfo()->bSmartOctopus);
				}
				else
				{
					if ( PassUsed == CHyfFareCalculation::MonthlyPass1 || PassUsed == CHyfFareCalculation::MonthlyPass2 )
						FreeFare = 1;
					else if ( PassUsed >= CHyfFareCalculation::DayPass1 )
						FreeFare = 10;

					Prn.PrintSingleJourneyReceipt(
						(PaymentMethod == PayCsc || PaymentMethod == PayMp || PaymentMethod == PayDp ), 
						IsDeluxe, 
						Fare, 
						Csc.InvalidCsc.lnSerialNo, 
						((PaymentMethod == PayCsc || PaymentMethod == PayMp || PaymentMethod == PayDp) ? Csc.InvalidCsc.LanguageSel : 0 ), 
						this->CurrentFareGroup,//[webcall][#17247]Use fare group as index 
						HyfPatronClass, 
						FareCalcTime, 
						mRecv, 
						mChange, 
						Csc.IsAutopayUsed() ? Csc.GeneralInfo.DirectDebit : CCurrency(0, 0), 
						NewPurse, 
						(PaymentMethod == PayCsc) ? Csc.GetGeneralInfo()->bSmartOctopus : FALSE,  
						FreeFare);
				}
				break;

			default:
				break;
		}
	}
	return TRUE;
}

int CPostHyfProcessTrip::ProcessFreightFare(int Generate)
{
	CHyfTrip
		HyfTrip;

	int ReturnValue = TRUE;

	if (Generate)
	{
		CPostReportPrn Prn;

		if (PaymentMethod == PayCash)
		{
			COcpEventDataCscPostTransCash EventCash;
//			Changed GroupSize from 0 to 1. Required by upper tiers.
//          EventCash.Set(POST_TRANS_FREIGHT, 0, 0, 0, Fare, HyfTrip.GetRouteCode(), HyfTrip.GetFerryType(), 0);
			EventCash.Set(POST_TRANS_FREIGHT, 0, 0, 0, Fare, HyfTrip.GetRouteCode(), HyfTrip.GetFerryType(), 1);
			EventCash.Commit();
		}
		else
		{
			CCurrency NewPurse;
			CMachineId    LAVMachineAfterTxn;
			CCscLogicalId CscLogicalIdAfterTxn;

			if (Csc.IsAutopayUsed())
			{
				// autopay occurred
				CDateTime Now;

				Csc.GeneralInfo.TransactionNum +=1;
				GenerateAutopayUd();
				NewPurse = (Csc.GeneralInfo.PurseValue + Csc.GeneralInfo.DirectDebit) - Fare;

				Now = CDateTime::GetCurrentTime();
				LAVMachineAfterTxn = CMachineId(gEod.MachineId());
				CscLogicalIdAfterTxn = CCscLogicalId(Csc.InvalidCsc.lnSerialNo, AUTOPAY_AV, Now.GetDayOfYear(), Csc.GeneralInfo.AddValueAccum+1);
			}
			else
			{
				NewPurse = Csc.GeneralInfo.PurseValue - Fare;

				LAVMachineAfterTxn = CMachineId(Csc.GeneralInfo.LastAvMachineId);
				CscLogicalIdAfterTxn = Csc.GetLogicalCscId();
			}
			COcpEventDataCscPostTransCsc EventCsc;
//			Changed GroupSize from 0 to 1. Required by upper tiers.
//          EventCsc.Set(POST_TRANS_FREIGHT, Csc.GetLogicalCscId(), 0, 0, 0, Csc.GeneralInfo.TransactionNum, Fare, NewPurse, Csc.GeneralInfo.LastAvMachineId, HyfTrip.GetRouteCode(), HyfTrip.GetFerryType(), 0, Csc.GeneralInfo.IssuerId);
			EventCsc.Set(POST_TRANS_FREIGHT, CscLogicalIdAfterTxn, 0, 0, 0, Csc.GeneralInfo.TransactionNum, Fare, NewPurse, LAVMachineAfterTxn, HyfTrip.GetRouteCode(), HyfTrip.GetFerryType(), 1, Csc.GeneralInfo.IssuerId);
			EventCsc.Commit();
		}
		//Prn.PrintFreightReceipt((PaymentMethod == PayCsc), Fare, FareCalcTime);
	}
	else
	{
		TCHAR  Buffer[20];
		int   Cents   = 0;
		int   Dollars = 0;
		TCHAR *s;
		BOOL  secondDecimalValid = FALSE;

		// Convert the string to dollars and cents and validate it
		FareCalcTime = CDateTime::GetCurrentTime();
		memset(Buffer, 0, sizeof(TCHAR)*20);
		_tcsncpy_s(Buffer, 20, m_StrFare, 19);

		// check for valid chars in the string
		s = Buffer;
		while (*s)
		{
			if (isdigit(*s) || *s == '.')
			{
				s++;
			}
			else
			{
				s           = NULL;
				ReturnValue = FALSE;
				Status      = FreightStrInvalidError;
				break;
			}
		}

		if (s)
		{
			// convert the string
			s = _tcschr(Buffer, '.');
			if (s)
			{
				if ( (*(s+2) != '0') && (*(s+2) != NULL) )
				{
					secondDecimalValid = TRUE;
				}
				*s = 0;
				Dollars = _tstoi(Buffer);
				if (*(s + 1))
				{
					// We only chop off the '0' because it's
					// valid in this context i.e. "2.10", but
					// if the user enters a value where the
					// hundredth's is greater than zero then
					// we leave it to be caught later so as
					// to display an error.
					if (*(s + 2) == '0')
					{
						// chop off any cent hundredths
						*(s + 2) = '\0';
					}
					Cents = _tstoi(s + 1);
				}
			}
			else
			{
				Dollars = _tstoi(Buffer);
			}
			if (Dollars < 0 || Cents < 0 || Cents > 9 || secondDecimalValid)
			{
				// invalid values
				Status      = FreightStrInvalidError;
				ReturnValue = FALSE;
			}
			else
			{
				Fare = (Dollars * 10) + Cents;
			}
		}
	}
	return ReturnValue;
}

int CPostHyfProcessTrip::MapPatronIndexToClass(int PatronIndex, int *RealClass)
{
	// maps classes on screen to hyf patron classes:
	// for concession check and complementary which don't use farecalcs.
	int RealMap[] = {
		FsAdult,
		FsChild,
		FsSeniorCitizen,
		FsStudent,
		FsDisabled,
		FsChild,
		FsChild,
		PC_STAFF,
		PC_STAFF_SPOUSE,
		PC_STAFF_JUNIOR_DEP,
		PC_STAFF_SENIOR_DEP,
		PC_RETIREE
		};

	// maps classes on screen to hyf patron classes
	int Map[] = {
		HYF_ADULT,
		HYF_CHILD12,
		HYF_SENIOR,
		HYF_STUDENT,
		HYF_DISABLED,
		HYF_CHILD1,
		HYF_CHILD3,
		HYF_STAFF,
		HYF_STAFF_DEP_ADULT,
		HYF_STAFF_DEP_CHILD,
		HYF_STAFF_DEP_SENIOR,
		HYF_STAFF_RETIRED
		};

	if (RealClass != 0)
	{
		*RealClass = RealMap[PatronIndex];
	}

	return Map[PatronIndex];
}

void CPostHyfProcessTrip::EnablePatronCheckBox(int PatronClass, int Enable, int DoAllControls)
{
	if (DoAllControls || PatronClass == HYF_ADULT)
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_1), Enable);
	if (DoAllControls || PatronClass == HYF_CHILD12)
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_2), Enable);
	if (DoAllControls || PatronClass == HYF_SENIOR)
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_3), Enable);
	if (DoAllControls || PatronClass == HYF_STUDENT)
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_4), Enable);
	if (DoAllControls || PatronClass == HYF_DISABLED)
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_5), Enable);
	if (DoAllControls || PatronClass == HYF_CHILD1)
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_6), Enable);
	if (DoAllControls || PatronClass == HYF_CHILD3)
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_7), Enable);
	if (DoAllControls || PatronClass == HYF_STAFF )	
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_8), Enable);
	if (DoAllControls || PatronClass == HYF_STAFF_DEP_ADULT)
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_9), Enable);
	if (DoAllControls || PatronClass == HYF_STAFF_DEP_CHILD)
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_10), Enable);
	if (DoAllControls || PatronClass == HYF_STAFF_DEP_SENIOR)
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_11), Enable);
	if (DoAllControls || PatronClass == HYF_STAFF_RETIRED)
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_CHECK_12), Enable);
}


void CPostHyfProcessTrip::EnablePatronEdits(int PatronClass, int EnableThem, int DoAllControls)
{
	CHyfTrip
		HyfTrip;

	int DeluxeOnly = FALSE;

	if (HyfTrip.GetFerryType() != FERRY_ORDINARY)
	{
		// if we are not an ordinary ferry then
		// we cannot have ordinary class, so it
		// must be disabled all the time.
		DeluxeOnly = TRUE;
	}

	if (DoAllControls || PatronClass == HYF_ADULT)
	{
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_EDIT_11), (DeluxeOnly) ? FALSE : EnableThem);
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_EDIT_12), EnableThem);
	}
	if (DoAllControls || PatronClass == HYF_CHILD1)
	{
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_EDIT_21), (DeluxeOnly) ? FALSE : EnableThem);
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_EDIT_22), EnableThem);
	}
	if (DoAllControls || PatronClass == HYF_CHILD3)
	{
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_EDIT_31), (DeluxeOnly) ? FALSE : EnableThem);
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_EDIT_32), EnableThem);
	}
	if (DoAllControls || PatronClass == HYF_CHILD12)
	{
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_EDIT_41), (DeluxeOnly) ? FALSE : EnableThem);
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_EDIT_42), EnableThem);
	}
	if (DoAllControls || PatronClass == HYF_SENIOR)
	{
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_EDIT_51), (DeluxeOnly) ? FALSE : EnableThem);
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_EDIT_52), EnableThem);
	}
	if (DoAllControls || PatronClass == HYF_STUDENT)
	{
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_EDIT_61), (DeluxeOnly) ? FALSE : EnableThem);
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_EDIT_62), EnableThem);
	}
	if (DoAllControls || PatronClass == HYF_DISABLED)
	{
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_EDIT_71), (DeluxeOnly) ? FALSE : EnableThem);
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_EDIT_72), EnableThem);
	}
	if (DoAllControls || PatronClass == HYF_STAFF)
	{
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_EDIT_81), (DeluxeOnly) ? FALSE : EnableThem);
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_EDIT_82), EnableThem);
	}
	if (DoAllControls || PatronClass == HYF_STAFF_DEP_ADULT)
	{
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_EDIT_91), (DeluxeOnly) ? FALSE : EnableThem);
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_EDIT_92), EnableThem);
	}
	if (DoAllControls || PatronClass == HYF_STAFF_DEP_CHILD)
	{
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_EDIT_101), (DeluxeOnly) ? FALSE : EnableThem);
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_EDIT_102), EnableThem);
	}
	if (DoAllControls || PatronClass == HYF_STAFF_DEP_SENIOR)
	{
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_EDIT_111), (DeluxeOnly) ? FALSE : EnableThem);
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_EDIT_112), EnableThem);
	}
	if (DoAllControls || PatronClass == HYF_STAFF_RETIRED)
	{
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_EDIT_121), (DeluxeOnly) ? FALSE : EnableThem);
		::EnableWindow(::GetDlgItem(m_hWnd, IDC_TRIP_EDIT_122), EnableThem);
	}
}


void CPostHyfProcessTrip::SetPatronSelection(int PatronClass, int IsDeluxe)
{
	int Map [] = {0,    /* nothing */               0,  /* HYF_ADULT */
				  3,    /* HYF_CHILD12 */           4,  /* HYF_SENIOR */
				  5,    /* HYF_STUDENT */           6,  /* HYF_DISABLED */
				  1,    /* HYF_CHILD1 */            2,  /* HYF_CHILD3 */
				  7,    /* HYF_STAFF */             8,  /* HYF_STAFF_DE P_ADULT */
				  9,    /* HYF_STAFF_DEP_CHILD */   10, /* HYF_STAFF_DEP_SENIOR */
				  11    /* HYF_STAFF_RETIRED */
				};

	//
	// Reset all of the selections
	//
	//memset(m_TypeSelected,    0, sizeof(m_TypeSelected));
	//memset(m_OrdinaryPatrons, 0, sizeof(m_OrdinaryPatrons));
	//memset(m_DeluxePatrons,   0, sizeof(m_DeluxePatrons));

	//
	// Map the real patron class to the classes shown onscreen (different order).
	//
	PatronIndex = Map[PatronClass];

	//
	// Check the new checkbox and set the edit box numbers
	//
	//m_TypeSelected[PatronIndex] = 1;
	//if (IsDeluxe)
	//{
	//    m_DeluxePatrons[PatronIndex] = 1;
	//}
	//else
	//{
	//    m_OrdinaryPatrons[PatronIndex] = 1;
	//}
}

void CPostHyfProcessTrip::OnTripChangeQuota()
{
	CHyfTrip
		HyfTrip;

	if (HyfTrip.IsInTrip() == FALSE)
	{
		// if we're not in a trip, we can't change the quota!
		OcpMessageBox(OCPMSG_HYF_TRIP_ENDED);
	}

	CSupervisorSignOnDialog signOnDlg;

	// ask for supervisor CSC
	if (signOnDlg.DoModal() == IDOK)
	{
		CHyfQuotaDlg dlg(this);

		// Change quota button
		dlg.DoModal();
		ShowManualQuotaValues();
	}
}

void CPostHyfProcessTrip::OnTripSingleJourney()
{
	// Single journey radio button
	SetupControls(SingleJourney);
}

void CPostHyfProcessTrip::OnTripComplementaryPass()
{
	// Complementary Pass radio button
	SetupControls(Complementary);
}

void CPostHyfProcessTrip::OnTripConcessionCheck()
{
	// Concession Check radio button
	SetupControls(ConcessionCheck);
}

void CPostHyfProcessTrip::OnTripConcessionSale()
{
	// Concession pass sale raido button
	SetupControls(ConcessionSale);
}


void CPostHyfProcessTrip::OnTripFreight()
{
	// Freight radio button
	SetupControls(Freight);
}

void CPostHyfProcessTrip::OnTripGroupNormal()
{
	// Group normal radio button
	SetupControls(GroupNormal);
}

void CPostHyfProcessTrip::OnTripGroupPrepaid()
{
	// Prepaid raido button
	SetupControls(GroupPrePaid);
}

void CPostHyfProcessTrip::OnTripMonthlypass()
{
	// Monthly pass radio button
	switch (FareType)
	{
		case SingleJourney:
		case ConcessionSale:
		case GroupNormal:
			PaymentMethod = PayMp;
			break;

		case ConcessionCheck:
		case Complementary:
		case GroupPrePaid:
		default:
			ProcessError(LogicError);
			break;
	}
}

void CPostHyfProcessTrip::OnTripDeluxeUpgrade()
{
	// Deluxe upgrade radio button
	// DeluxeUpgrade = !DeluxeUpgrade;
}

void CPostHyfProcessTrip::OnTripDeluxe()
{
	int selIdx = 0;
	int selItems = 0;

	CHyfFareCalculation FareCalc;
	CHyfTrip			HyfTrip;

	CFareCalculation::Status_t fareCalcStatus = CFareCalculation::Status_t::Ok;

	// Deluxe travel radio button
	switch (FareType)
	{
		case CRT_UPGRADE:
		case ConcessionCheck:
			m_nOriginFareClass = ORDINARY_ORDINARY;
		case ConcessionSale:
		case SingleJourney:
		case Complementary:
		// williamto 20121113: allow group tickets to select patron class
		case GroupNormal:
		case GroupPrePaid:	
			UpdateData(TRUE);
			IsDeluxe = TRUE;
			//m_DeluxePatrons[PatronIndex] = 1;
			//m_OrdinaryPatrons[PatronIndex] = 0;
			//if (FareType == SingleJourney && PaymentMethod == PayMp)
			//{
				//RadioDeluxe.EnableWindow(TRUE);
			//}
			selIdx = 0;
			selItems = m_ctlList.GetSelItems( 1, &selIdx );
			if ( selItems == 1 )
			{
				vector<CPosTicketItem>::iterator it;
				CString	empty = _T("");
				for ( it = mItems.begin(); it != mItems.end(); it++ )
				{
					CPosTicketItem & item = *it;

					if ( item.GetItemListPos() == selIdx )
					{
					// williamto 28Nov2013: [SIT][POST][Issue #35] HRT sale, check, upgrade precedence above MT
						if ( FareType == ConcessionSale )
						{
							if (HrtPaymentMethod == HrtPayEncodeCsc)
							{
								fareCalcStatus = FareCalc.GetConcessionReturnFare(HyfTrip.GetOriginTerminal(), HyfTrip.GetDestinationTerminal(), HyfTrip.GetFerryType(), IsDeluxe, 
									&Csc.GeneralInfo, &Csc.HyfInfo, &Csc.PersonalInfo);
							}
							else
							{
								fareCalcStatus = FareCalc.GetConcessionReturnFare(HyfTrip.GetOriginTerminal(), HyfTrip.GetDestinationTerminal(), HyfTrip.GetFerryType(), IsDeluxe, 
									item.GetItemId());
							}
							
							if (fareCalcStatus != CHyfFareCalculation::Ok)
							{
								ProcessError(FareCalculationError);
								return;
							}
						}
						else if ( (FareType == ConcessionCheck || FareType == CRT_UPGRADE) /*&& PaymentMethod == PayCash*/ )
						{
							m_nOriginFareClass = ORDINARY_ORDINARY;
							
							// this should be considered as upgrade, so need to pay surcharge
							// we should calculate the upgrade fare based on the current sailing (deluxe / fast / cata)
							if (HrtPaymentMethod == HrtPayEncodeCsc)
							{
								// calculate as csc pay-encode operation
								fareCalcStatus = FareCalc.CalculateCrtUpgrade(HyfTrip.GetSailingTime(), 
									HyfTrip.GetOriginTerminal(), 
									HyfTrip.GetDestinationTerminal(), 
									m_nOriginFareClass, 
									gEod.HyfGetFareClass(HyfTrip.GetFerryType(), this->IsDeluxe), 
									&Csc.GeneralInfo, 
									&Csc.HyfInfo, 
									&Csc.PersonalInfo);	// calculate the current upgrade fare based on the sailing
							}
							else
							{
								// calculate as cash / csc purse only payment
								fareCalcStatus = FareCalc.CalculateCrtUpgrade(item.GetItemId(), 
										HyfTrip.GetSailingTime(), 
										HyfTrip.GetOriginTerminal(), 
										HyfTrip.GetDestinationTerminal(), 
										m_nOriginFareClass, 
										gEod.HyfGetFareClass(HyfTrip.GetFerryType(), this->IsDeluxe));	// calculate the current upgrade fare based on the sailing
							}
									
							if (fareCalcStatus != CHyfFareCalculation::Ok)
							{
								ProcessError(FareCalculationError);
								return;
							}

							if ( FareType == ConcessionCheck )
								FareType = CRT_UPGRADE;
						}
						//else if ( FareType == ConcessionCheck || FareType == CRT_UPGRADE || (FareType == SingleJourney && PaymentMethod == PayMp) || PaymentMethod == PayDp || PaymentMethod == PayCsc )
						else if (FareType == SingleJourney && (PaymentMethod == PayMp || PaymentMethod == PayDp || PaymentMethod == PayCsc) && (Csc.CscDataValid != FALSE))
						{
							// this is for SJT paid by monthly ticket, day pass or by csc purse
							// and we only do that when the HyfProcessFareCalc CSC data is valid
							if ((fareCalcStatus = FareCalc.CalculateFare(HyfTrip.GetSailingTime(), HyfTrip.GetOriginTerminal(), 
									HyfTrip.GetDestinationTerminal(), HyfTrip.GetFerryType(), HyfTrip.GetHolidayNum(),
									IsDeluxe, &Csc.GeneralInfo, &Csc.HyfInfo, &Csc.PersonalInfo)) !=  CHyfFareCalculation::Ok)
							{
								ProcessError(FareCalculationError);
								return;
							}

							//if ( FareType == ConcessionCheck )
							//	FareType = CRT_UPGRADE;
						}
						else
						{
							if ((fareCalcStatus = FareCalc.CalculateFare(item.GetItemId(), 
								HyfTrip.GetSailingTime(), 
								HyfTrip.GetOriginTerminal(), 
								HyfTrip.GetDestinationTerminal(), 
								HyfTrip.GetFerryType(), 
								IsDeluxe)) != CHyfFareCalculation::Ok)
							{
								ProcessError(FareCalculationError);
								return;
							}
						}
				// williamto 19Nov2013: SIT failure [POST][*******] POST shows normal SJT fare under pre-paid group and complementary ticket mode when changing the travel class for a selected patron entry.
						Fare = ( FareType == GroupPrePaid || FareType == Complementary ) ? 0 : FareCalc.GetFare();
						ModifyItem( selIdx, IsDeluxe, item.GetItemQty(), Fare );
						break;
					}
				}
			}
			RadioOrdinary.SetColorToWindowsDefault(TRUE);
			RadioDeluxe.SetColor( RGB_BLACK, RGB_YELLOW );
			UpdateData(FALSE);
			break;

		default:
			ProcessError(LogicError);
			break;
	}
}

void CPostHyfProcessTrip::OnTripOrdinary()
{
	int selIdx = 0;
	int selItems = 0;

	CHyfFareCalculation FareCalc;
	CHyfTrip			HyfTrip;

	CFareCalculation::Status_t fareCalcStatus = CHyfFareCalculation::Ok;
							
	// Ordinary travel radio button

	switch (FareType)
	{
		case SingleJourney:
		case ConcessionCheck:
		case ConcessionSale:
		case Complementary:
		case CRT_UPGRADE:
		// williamto 20121113: allow group tickets to select patron class
		case GroupNormal:
		case GroupPrePaid:	
			// need to change edits to reflect current type
			UpdateData(TRUE);
			IsDeluxe = FALSE;
			//m_DeluxePatrons[PatronIndex] = 0;
			//m_OrdinaryPatrons[PatronIndex] = 1;
//            if (FareType == SingleJourney && PaymentMethod == PayMp)
//            {
				//RadioDeluxe.EnableWindow(FALSE);
				//RadioDeluxe.SetCheck(FALSE);
//            }
			selIdx = 0;
			selItems = m_ctlList.GetSelItems( 1, &selIdx );
			if ( selItems == 1 )
			{
				vector<CPosTicketItem>::iterator it;
				CString	empty = _T("");
				for ( it = mItems.begin(); it != mItems.end(); it++ )
				{
					CPosTicketItem & item = *it;

					if ( item.GetItemListPos() == selIdx )
					{
					// williamto 28Nov2013: [SIT][POST][Issue #35] HRT sale, check, upgrade precedence above MT						
						if ( FareType == ConcessionSale )
						{
							if (HrtPaymentMethod == HrtPayEncodeCsc)
							{
								fareCalcStatus = FareCalc.GetConcessionReturnFare(HyfTrip.GetOriginTerminal(), HyfTrip.GetDestinationTerminal(), 
									HyfTrip.GetFerryType(), IsDeluxe, &Csc.GeneralInfo, &Csc.HyfInfo, &Csc.PersonalInfo);
							}
							else
							{
								fareCalcStatus = FareCalc.GetConcessionReturnFare(HyfTrip.GetOriginTerminal(), HyfTrip.GetDestinationTerminal(), 
									HyfTrip.GetFerryType(), IsDeluxe, item.GetItemId());
							}

							if (fareCalcStatus != CHyfFareCalculation::Ok)
							{
								ProcessError(FareCalculationError);
								return;
							}
						}
						else if ( FareType == ConcessionCheck || FareType == CRT_UPGRADE /*&& PaymentMethod == PayCash*/ )
						{
							m_nOriginFareClass = ORDINARY_ORDINARY;

							// this should be considered as upgrade, so need to pay surcharge
							// we should calculate the upgrade fare based on the current sailing (deluxe / fast / cata)
							if (HrtPaymentMethod == HrtPayEncodeCsc)
							{
								// calculate as csc pay-encode operation
								fareCalcStatus = FareCalc.CalculateCrtUpgrade(HyfTrip.GetSailingTime(), 
									HyfTrip.GetOriginTerminal(), 
									HyfTrip.GetDestinationTerminal(), 
									m_nOriginFareClass, 
									gEod.HyfGetFareClass(HyfTrip.GetFerryType(), this->IsDeluxe), 
									&Csc.GeneralInfo, 
									&Csc.HyfInfo, 
									&Csc.PersonalInfo);	// calculate the current upgrade fare based on the sailing
							}
							else
							{
								fareCalcStatus = FareCalc.CalculateCrtUpgrade(item.GetItemId(), 
									HyfTrip.GetSailingTime(), 
									HyfTrip.GetOriginTerminal(), 
									HyfTrip.GetDestinationTerminal(), 
									m_nOriginFareClass, 
									ORDINARY_ORDINARY	// calculate the current upgrade fare based on the sailing
								);
							}

							if ( fareCalcStatus!= CHyfFareCalculation::Ok)
							{
								ProcessError(FareCalculationError);
								return;
							}

							if ( FareType == CRT_UPGRADE )
								FareType = ConcessionCheck;
						}
						//else if ( FareType == ConcessionCheck || FareType == CRT_UPGRADE || (FareType == SingleJourney && PaymentMethod == PayMp) || PaymentMethod == PayDp || PaymentMethod == PayCsc )
						else if ( FareType == SingleJourney && (PaymentMethod == PayMp || PaymentMethod == PayDp || PaymentMethod == PayCsc) && (Csc.CscDataValid != FALSE))
						{
							// williamto 22Jan2013: if payment by MT for SJT, then recalculate the fare based on the MT
							// and we only do that when the HyfProcessFareCalc CSC data is valid
							if ((fareCalcStatus = FareCalc.CalculateFare(HyfTrip.GetSailingTime(), HyfTrip.GetOriginTerminal(), 
									HyfTrip.GetDestinationTerminal(), HyfTrip.GetFerryType(), HyfTrip.GetHolidayNum(),
									IsDeluxe, &Csc.GeneralInfo, &Csc.HyfInfo, &Csc.PersonalInfo)) !=  CHyfFareCalculation::Ok)
							{
								ProcessError(FareCalculationError);
								return;
							}

							//if ( FareType == CRT_UPGRADE )
							//	FareType = ConcessionCheck;
						}
						else
						{
					// williamto 22Jan2013: if payment by MT for SJT, then recalculate the fare based on the MT
							if ((fareCalcStatus = FareCalc.CalculateFare(item.GetItemId(), 
								HyfTrip.GetSailingTime(), 
								HyfTrip.GetOriginTerminal(), 
								HyfTrip.GetDestinationTerminal(), 
								HyfTrip.GetFerryType(), 
								IsDeluxe)) != CHyfFareCalculation::Ok)
							{
								ProcessError(FareCalculationError);
								return;
							}
					// williamto 22Jan2013: if payment by MT for SJT, then recalculate the fare based on the MT
						}
					// williamto 22Jan2013: if payment by MT for SJT, then recalculate the fare based on the MT
					// williamto 19Nov2013: SIT failure [POST][*******] POST shows normal SJT fare under pre-paid group and complementary ticket mode when changing the travel class for a selected patron entry.
						Fare = ( FareType == GroupPrePaid || FareType == Complementary ) ? 0 : FareCalc.GetFare();
						ModifyItem( selIdx, IsDeluxe, item.GetItemQty(), Fare );
						break;
					}
				}
			}
			RadioDeluxe.SetColorToWindowsDefault(TRUE);
			RadioOrdinary.SetColor( RGB_BLACK, RGB_YELLOW );
			UpdateData(FALSE);
			break;

		default:
			ProcessError(LogicError);
			break;
	}
}

void CPostHyfProcessTrip::OnTripPurse()
{
	// kmlam 27May2024:NWFF requests to disable payment by CSC requires supervisor authorisation
	// NWFF requests payment by CSC requires supervisor authorisation
	//CSupervisorSignOnDialog signOnDlg;

	// ask for supervisor CSC
	//if (signOnDlg.DoModal() == IDOK)
	//{
		//
		// Pay by purse radio button
		//
		switch (FareType)
		{
			case ConcessionSale:
			case ConcessionCheck:
			case CRT_UPGRADE:
				HrtPaymentMethod = HrtPayCsc;
			case SingleJourney:
			case GroupNormal:
			case Freight:
				PaymentMethod = PayCsc;

				RadioCash.SetCheck(FALSE);
				//RadioCash.SetColorToWindowsDefault(TRUE);
				RadioCash.SetTextColor((COLORREF)-1);
				RadioCash.SetFaceColor((COLORREF)-1);
				RadioPurse.SetCheck(TRUE);
				//RadioPurse.SetColor( RGB_BLACK, RGB_PALEGREEN );
				RadioPurse.SetTextColor(RGB_BLACK);
				RadioPurse.SetFaceColor(RGB_PALEGREEN);
				break;

			//case ConcessionCheck:
			case Complementary:
			case GroupPrePaid:
			default:
				ProcessError(LogicError);
				break;
		}
	//}
	//else
	//{
	//	OnTripCash();
	//}
}

//
// Pay by cash radio button
//

void CPostHyfProcessTrip::OnTripCash()
{
	CHyfTrip 
		HyfTrip;
	
	switch (FareType)
	{
		case ConcessionSale:
		case ConcessionCheck:
		case CRT_UPGRADE:
			HrtPaymentMethod = HrtPayCash;
		case GroupNormal:
		case SingleJourney:
			if ((FareType == ConcessionSale) || (FareType == ConcessionCheck) || (FareType == CRT_UPGRADE))
			{
				for (int i=0; i<NUM_OF_HYF_PATRON_CLASS; i++)
				{
					EnablePatronCheckBox(i+1, (BOOL)this->bHrtEligiblePaxClass[i], FALSE);
				}
			}
			else
			{
				EnablePatronCheckBox(0, TRUE, TRUE);
			}
			// Note the fall through here!
		case Freight:
			PaymentMethod = PayCash;
			//
			// Disable the mt and purse radio buttons
			//
			//RadioMonthly.EnableWindow(FALSE);
			//RadioPurse.EnableWindow(FALSE);
			//RadioDeluxe.EnableWindow(FALSE);

			//
			// Set them up.
			//
			RadioCash.SetCheck(TRUE);
			//RadioCash.SetColor( RGB_BLACK, RGB_PALEGREEN );
			RadioCash.SetTextColor(RGB_BLACK);
			RadioCash.SetFaceColor(RGB_PALEGREEN);
			RadioPurse.SetCheck(FALSE);
			//RadioPurse.SetColorToWindowsDefault(TRUE);
			RadioPurse.SetFaceColor((COLORREF)-1);
			RadioPurse.SetTextColor((COLORREF)-1);
			//RadioDeluxe.SetCheck(FALSE);
			break;

		//case ConcessionCheck:
		case Complementary:
		case GroupPrePaid:
		default:
			ProcessError(LogicError);
			break;
	}
}

void CPostHyfProcessTrip::ProcessHotKey( UINT nID )
{
	int i = (int) (nID - IDR_OCPACCEL_VKF1);

	if ( GetDlgItem( i+IDC_TRIP_CHECK_1 )->IsWindowEnabled() )
		return ProcessCheckBox( i + IDC_TRIP_CHECK_1 );
}

//
// This function is called whenever a checkbox is clicked.
// Depending on the current fare type selection, we:
//
// 1. Single Journey: unselect the previous box that was
//    checked and select the new box & setup the edit
//    boxes.
//
// 2. Group Tickets: we toggle the edit boxes on and off
//    to indicate if the boxes are included in the group
//    ticket.
//

void CPostHyfProcessTrip::ProcessCheckBox(/*int CheckBoxPressed*/UINT nID)
{
	// webcall #13574: disallow change of patron type when transaction is progress
	if (m_DoingCscOperation == TRUE)
		return;

	// Retrieve data from the controls
	UpdateData(TRUE);

	//ProcessItemAndQty();
	mQty = 0;
	mEnteredValue = _T("");
	
	// change into "item input mode"
	mSequence = COCPPosDialog::POS_EVENT_ENTER_ITEM;
	
	int CheckBoxPressed = (int) (nID - IDC_TRIP_CHECK_1);

	//m_TypeSelected[CheckBoxPressed] = 1;

	switch (FareType)
	{
		case SingleJourney:
			ClearPendingTransactionList();		// williamto 20Dec2012: for SJT always clear the list before proceessing
		case ConcessionCheck:
		case ConcessionSale:
		case CRT_UPGRADE:
			// if the payment is not pay by cash, then do not allow for more than 1 entry to comply with OCL business rules
			if (HrtPaymentMethod != HrtPayCash)
			{
				ClearPendingTransactionList();
			}
			// Set the new ones
			PatronIndex = CheckBoxPressed;
	//		if (PaymentMethod == PayCash)
	//		{
	//			// Retrieve data from the controls
	//			//UpdateData(TRUE);

	//			// Zero the current controls
	//			//m_TypeSelected[PatronIndex]    = 0;
	//			//m_DeluxePatrons[PatronIndex]   = 0;
	//			//m_OrdinaryPatrons[PatronIndex] = 0;

	//			// Set the new ones
	//			//PatronIndex = CheckBoxPressed;
	//			//m_TypeSelected[PatronIndex]    = 1;
	////            if (IsDeluxe)
	////            {
	//			//	m_DeluxePatrons[PatronIndex]   = mQty;
	////                m_OrdinaryPatrons[PatronIndex] = 0;
	////            }
	////            else
	////            {
	////                m_DeluxePatrons[PatronIndex]   = 0;
	////                m_OrdinaryPatrons[PatronIndex] = mQty;
	////            }
	//			// Put the data back into the controls
	//			//UpdateData(FALSE);
	//		}
	//		else
	//		{
	//			//
	//			// Make sure we cannot unselect the box.
	//			//
	//			//if (CheckBoxPressed == PatronIndex)
	//			//{
	//				// Copy the data from the controls
	//				//UpdateData(TRUE);

	//				// Make sure the checkbox is pressed
	//				//PatronIndex = CheckBoxPressed;

	//				//m_TypeSelected[PatronIndex] = 1;
	//				// Copy the data back
	//				//UpdateData(FALSE);
	//			//}
	//		}
			// single journey ticket only sells 1 ticket so mQty=1 and then process item
			mQty = 1;
			ProcessItemAndQty();
			// change into "quantity input mode"
			mSequence = COCPPosDialog::POS_EVENT_ENTER_QTY;
			break;

		case Complementary:
		case GroupNormal:
		case GroupPrePaid:
			//
			// Work out if we are already on
			// ok, here we want to disable the
			// edit boxes depending on if the
			// corresponding check  has been
			// selected.
			//
			//UpdateData(TRUE);
			
			PatronIndex = CheckBoxPressed;
			//m_DeluxePatrons[CheckBoxPressed]   = 0;
			//m_OrdinaryPatrons[CheckBoxPressed] = 0;
			//if (m_TypeSelected[CheckBoxPressed] == 1)
   //             EnablePatronEdits(MapPatronIndexToClass(CheckBoxPressed), TRUE, FALSE);
   //         else
   //             EnablePatronEdits(MapPatronIndexToClass(CheckBoxPressed), FALSE, FALSE);
			
			// add empty item here

			// kmlam 27May2024: NWFF request to allow multiple item can be select when PaymentMethod != PayCash
			//if (PaymentMethod != PayCash)
			//{
				//	// williamto 1Jun2018: as current UD limitations its impossible to include multiple item tranactions into POST CSC ticket payment 
				//	// therefore we only allow single item and qty > 1 when payment is NOT cash so we need to clear any existing pending transactions 
				//	// and let the operator start over again
			//	ClearPendingTransactionList();
			//}
			AddItem( MapPatronIndexToClass(PatronIndex), IsDeluxe, 0, COleCurrency(0, 0));
			//UpdateData(FALSE);
			// change into "quantity input mode"
			mSequence = COCPPosDialog::POS_EVENT_ENTER_QTY;

			break;

		default:
			ProcessError(LogicError);
			break;
	}

	// Copy the data back
	UpdateData(FALSE);
}


//
//
// The following functions call the above funtion to
// handle the pressing of the checkbox.  Each function
// tells the main one which checkbox called it.
//
/*
void CPostHyfProcessTrip::OnTripCheck1()
{
	// note that the 'checkbox' is zero based
	ProcessCheckBox(0);
}


void CPostHyfProcessTrip::OnTripCheck2()
{
	ProcessCheckBox(1);
}

void CPostHyfProcessTrip::OnTripCheck3()
{
	ProcessCheckBox(2);
}

void CPostHyfProcessTrip::OnTripCheck4()
{
	ProcessCheckBox(3);
}

void CPostHyfProcessTrip::OnTripCheck5()
{
	ProcessCheckBox(4);
}

void CPostHyfProcessTrip::OnTripCheck6()
{
	ProcessCheckBox(5);
}

void CPostHyfProcessTrip::OnTripCheck7()
{
	ProcessCheckBox(6);
}

void CPostHyfProcessTrip::OnTripCheck8()
{
	ProcessCheckBox(7);
}

void CPostHyfProcessTrip::OnTripCheck9()
{
	ProcessCheckBox(8);
}

void CPostHyfProcessTrip::OnTripCheck10()
{
	ProcessCheckBox(9);
}

void CPostHyfProcessTrip::OnTripCheck11()
{
	ProcessCheckBox(10);
}

void CPostHyfProcessTrip::OnTripCheck12()
{
	ProcessCheckBox(11);
}
*/

void CPostHyfProcessTrip::OnTripIssueMenu() 
{
	CHyfTrip HyfTrip; 
	
	//// TODO: Add your control notification handler code here
	//if (this->PaymentMethod != PayCash)
	//{
	//	CHyfIssTicket dlg( this );

	//	dlg.m_bFromProcessSailing	= TRUE;
	//	dlg.m_pTicketProcess		= this;
	//	if ( dlg.DoModal() == IDCANCEL )
	//		EndDialog( IDCANCEL );
	//	else
	//	{
	//		InitialiseDialogControls();
	//		// re-register this window
	//		HyfTrip.RegisterFareWindow( m_hWnd );
	//	}
	//}
	//else
	{
		this->SetupControls(gEod.HyfIsTripToCentral(HyfTrip.GetDestinationTerminal()) ? ConcessionSale : ConcessionCheck);
	}
}

void CPostHyfProcessTrip::OnTripCrmenu() 
{
	// TODO: Add your control notification handler code here
	CHyfCrtUpgrade dlg( this );

	dlg.m_bFromProcessSailing	= TRUE;
	dlg.m_pTicketProcess		= this;
	if ( dlg.DoModal() == IDCANCEL )
		EndDialog( IDCANCEL );
	else
	{
		CHyfTrip	HyfTrip;

		InitialiseDialogControls();
		// re-register this window
		HyfTrip.RegisterFareWindow( m_hWnd );
	}
}

void CPostHyfProcessTrip::SellSingle(bool IsCash, ShortCutPatronType ScType, bool Deluxe)
{

	Status = Ok;
	if (IsCash)
	{
		PaymentMethod = PayCash;
		switch (ScType)
		{
			case SC_ADULT:
				PatronIndex = 0;
				break;

			case SC_CHILD_UNDER3:
				PatronIndex = 2;
				break;

			case SC_CHILD:
				PatronIndex = 3;
				break;
			case SC_SENIOR:
				PatronIndex = 4;
				break;
		}
	}
	else
	{
	/* add check for same CSC if paid by CSC */
	DWORD res;
	if ( ( res = OcpRepollCsc() ) != ERROR_SUCCESS )
	{
		OcpMessageBox( OCPMSG_ERROR, res );
		return;
	}
		PaymentMethod = PayCsc;
		if (!Csc.Refresh())
			return;
	}

	FareType = SingleJourney;
	IsDeluxe = Deluxe;
	ProcessSingleJourney(false, true);
}

/*void*/BOOL CPostHyfProcessTrip::SellCrt(int nNumTickets, bool IsCash, ShortCutPatronType ScType, bool Deluxe, bool bShortCut)
{

	Status = Ok;
	
	switch (ScType)
	{
		case SC_ADULT:
			PatronIndex = 0;
			break;

		case SC_CHILD:
			PatronIndex = 3;
			break;

		case SC_STUDENT:
			PatronIndex = 5;
			break;
	}
	
	if (IsCash)
	{
		PaymentMethod = PayCash;
	}
	else
	{
		PaymentMethod = PayCsc;
//	    if (!Csc.Refresh())		// don't refresh to overwrite the autopay inforfmation
//			return FALSE;
	}

	FareType = ConcessionSale;
	CrtSingleTicket = false;
	IsDeluxe = Deluxe;
	return (BOOL)ProcessConcessionSale( /*(IsDeluxe ? 0 : nNumTickets), (IsDeluxe ? nNumTickets : 0 ),*/ bShortCut, bShortCut, false);
}

/*void*/BOOL CPostHyfProcessTrip::ValidateCrtRetTrip( int nNumTickets, bool IsCash, ShortCutPatronType ScType, bool Deluxe, bool bShortCut, int OldFareClass, int NewFareClass )
{
	Status = Ok;

	switch (ScType)
	{
		case SC_ADULT:
			PatronIndex = 0;
			break;

		case SC_CHILD:
			PatronIndex = 3;
			break;

		case SC_STUDENT:
			PatronIndex = 5;
			break;
	}

	if (IsCash)
	{
		PaymentMethod = PayCash;
	}
	else
	{
		PaymentMethod = PayCsc;
		//if (!Csc.Refresh())	// don't refresh to overwrite the autopay inforfmation
		//	return FALSE;
	}

	FareType = ConcessionCheck;
	IsDeluxe = Deluxe;

	CrtSingleTicket = false;

	if ( IsDeluxe )
		return (BOOL)ProcessCrtUpgrade( /*0, nNumTickets,*/ OldFareClass, NewFareClass, true, true, false);
	else
		return (BOOL)ProcessConcessionCheck( /*(IsDeluxe ? 0 : nNumTickets), (IsDeluxe ? nNumTickets : 0 ) ,*/ true, bShortCut );
}

/*void*/ BOOL CPostHyfProcessTrip::UpgradeCrt(int nNumTickets, bool IsCash, ShortCutPatronType ScType, int OldFareClass, int NewFareClass)
{
	Status = Ok;

	switch (ScType)
	{
		case SC_ADULT:
			PatronIndex = 0;
			break;
		case SC_CHILD:
			PatronIndex = 3;
			break;
		case SC_STUDENT:
			PatronIndex = 5;
			break;
	}

	if (IsCash)
	{
		PaymentMethod = PayCash;
	}
	else
	{
		PaymentMethod = PayCsc;
	  //  if (!Csc.Refresh())	// don't refresh to overwrite the autopay inforfmation
			//return false;
	}
	FareType = CRT_UPGRADE;
	CrtSingleTicket = false;
	IsDeluxe = 1;
	return (BOOL)ProcessCrtUpgrade( /*0, nNumTickets,*/ OldFareClass, NewFareClass, true, true, false);
}

void CPostHyfProcessTrip::CheckCscPatron(ShortCutPatronType *ScType, CHyfFareCalculation::HrtValidateResult_t *HrtResult)
{
	CHyfTrip			HyfTrip;
		
	CHyfFareCalculation FareCalc;

	int					CscIslandNum = 0;
	
	*ScType = SC_NONE;

	*HrtResult = CHyfFareCalculation::HrtValidateResult_t::NoHrt;

	Status = Ok;
	if (Csc.Refresh())
	{
		FareCalc.CalculatePatronClass(&Csc.HyfInfo, &Csc.GeneralInfo, &Csc.PersonalInfo);
		/* PwD & Senior FFS */
		if (FareCalc.IsPwDSeniorFlatFareEnabled())
		  if (FareCalc.EvaluatePwDSeniorFlatFare(FALSE) == Ok)  /* reject regardless FerryType */
		  {
			*ScType = SC_TRANSACTION_NOT_ALLOWED;
			return;
		  }
		
		/* Silver Age PTFCS 15-June-2021*/
		if (FareCalc.IsPwDSilverAgeFlatFareEnabled()) {
			if (FareCalc.EvaluatePwDSilverAgeFlatFare(FALSE) == Ok) {
				*ScType = SC_TRANSACTION_NOT_ALLOWED;
				return;
			}
		}
		/* End of Silver Age PTFCS 15-June-2021*/

		HyfPatronClass = FareCalc.GetHyfPatronClass();
		switch (HyfPatronClass)
		{
			case HYF_ADULT:
				*ScType = SC_ADULT;
				break;
			case HYF_CHILD12:
				*ScType = SC_CHILD;
				break;
			case HYF_STUDENT:
				*ScType = SC_STUDENT;
				break;
			case HYF_SENIOR:
				*ScType = SC_SENIOR;
				break;
			case HYF_CHILD3:
				*ScType = SC_CHILD_UNDER3;
				break;
		}
		
		if ( FareCalc.CalculateFare(HyfTrip.GetSailingTime(), HyfTrip.GetOriginTerminal(), 
			HyfTrip.GetDestinationTerminal(), HyfTrip.GetFerryType(), HyfTrip.GetHolidayNum(),
			IsDeluxe, &Csc.GeneralInfo, &Csc.HyfInfo, &Csc.PersonalInfo) != CHyfFareCalculation::Ok )
		{
			*HrtResult = CHyfFareCalculation::HrtValidateResult_t::NoHrt;
		}
		else
		{
			// case for valid HRT matching the current route
			if ( FareCalc.CscIslandNum != 0 && FareCalc.CscHolidayNum != 0 && ( FareCalc.CscHolidayNum == HyfTrip.GetHolidayNum() ) )
			{
				switch ( FareCalc.CscFareClass )
				{
					case 0 :
						*HrtResult = CHyfFareCalculation::PreRegSameRoute;
						break;
					case 1 :
						*HrtResult = CHyfFareCalculation::HrtFwdTripOnly;
						break;
					case 2 :
						*HrtResult = CHyfFareCalculation::HrtAllUsed;
						break;
					default:
						*HrtResult = CHyfFareCalculation::NoHrt;
						break;
				}
			}
			else
			{
				*HrtResult = CHyfFareCalculation::NoHrt;
			}

		}
	}
}

bool CPostHyfProcessTrip::ValidCsc(bool CheckAntiPassBack, Result_t *InvalidReason)		// give an optional argument
{
	bool CscOk = true;
	// Check to see if there is
	// enough value on the csc
	if (!Csc.IsSufficientValue(Fare))
	{
		// insufficient funds
		if (Csc.IsMaxAutopayExceeded())
		{
			// williamto 25Oct2013: FAT observation *******, if invalid reason pointer here that means handle the error condition at higher level once
			if ( InvalidReason != NULL ) 
				*InvalidReason = MaxAutopayExceeded;
			else
				ProcessError(MaxAutopayExceeded);
		}
		else
		{
			// williamto 25Oct2013: FAT observation *******, if invalid reason pointer here that means handle the error condition at higher level once
			if ( InvalidReason != NULL ) 
				*InvalidReason = InsufficientFunds;
			else
				ProcessError(InsufficientFunds);
		}
		CscOk = false;
	}

	// insufficient funds takes a lower priority than anti-passback handling
	if (CheckAntiPassBack && Csc.CheckForAntipassBack())
	{
		// check for antipassback
		// williamto 25Oct2013: FAT observation *******, if invalid reason pointer here that means handle the error condition at higher level once
		if ( InvalidReason != NULL ) 
			*InvalidReason = AntiPassBackError;
		else
			ProcessError(AntiPassBackError);

		CscOk = false;
	}

	return CscOk;
}

// williamto 30Aug2013: FAT failure *********, disallow purchase group tickets for CSC != ADULT
// kmlam 12Jun2024: allow purchase group tickets for CSC != ADULT
bool CPostHyfProcessTrip::ValidAdultCsc(bool CheckAntiPassBack, Result_t *InvalidReason)
{
	CHyfFareCalculation FareCalc;

	bool CscOk = true;

	if ( !ValidCsc(CheckAntiPassBack, InvalidReason) )
		CscOk = false;
	else
	{
	//	Csc.Refresh();	// williamto 11Sept2013: FAT failure *******, do not need to refresh the CSC to calculate the patron class
		FareCalc.CalculatePatronClass( &Csc.HyfInfo, &Csc.GeneralInfo, &Csc.PersonalInfo );
		switch (FareCalc.GetHyfPatronClass())
        {
            case HYF_ADULT:
            case HYF_CHILD12:
            case HYF_SENIOR:
            case HYF_STUDENT:
            case HYF_DISABLED:
            case HYF_CHILD1:
            case HYF_CHILD3:
            case HYF_STAFF:
            case HYF_STAFF_DEP_ADULT:
            case HYF_STAFF_DEP_CHILD:
            case HYF_STAFF_DEP_SENIOR:
            case HYF_STAFF_RETIRED:
                CscOk = true;
				
			// kmlam 25Jun2024: Allow SilverAge purchase group tickets for CSC
			///* Silver Age PTFCS 13-July-2021*/
			//if (FareCalc.IsPwDSilverAgeFlatFareEnabled())
			//{
			//	if (FareCalc.EvaluatePwDSilverAgeFlatFare(FALSE) == Ok)
			//	{
			//		if ( InvalidReason != NULL )
			//			*InvalidReason = InvalidPatronClassError;	
			//		else
			//			ProcessError(InvalidPatronClassError);
			//		CscOk = false;
			//	}
			//}
			/* End of Silver Age PTFCS 13-July-2021*/
				break;
		default:
			// williamto 25Oct2013: FAT observation *******, if the invalid reason pointer is valid that means the error is processed at a higher level, otherwise handle here
			if ( InvalidReason != NULL )
				*InvalidReason = InvalidPatronClassError;	
			else
				ProcessError(InvalidPatronClassError);
			CscOk = false;
			break;
		}

	return CscOk;
	}
}

bool CPostHyfProcessTrip::CustomerAcceptFare()
{
	CString		Text;
	CCurrency	NewPurse;

	if (PaymentMethod == PayCash || ((PaymentMethod == PayMp || PaymentMethod == PayDp) && PassUpgradePaymentMethod == PayCash && IsDeluxe == 1) /* || (PaymentMethod == PayDp && IsDeluxe == 1) */)
	{
		Text.Format( _R(IDS_OCP_CONFIRM_CASH_TICKET_TXN),  CString((FareType == Freight) ? _R(IDS_OCP_TRANS_FREIGHT) : _T("")), 
			mTotal.Format(), 
			mRecv.Format(), 
			mChange.Format());
		
		Text += _R( IDS_OCP_CONFIRM );
	}
	else
	{
		if (Csc.IsAutopayUsed())
		{
			NewPurse = (Csc.GeneralInfo.PurseValue + Csc.GeneralInfo.DirectDebit) - Fare;

			Text.Format( _R(IDS_OCP_CONFIRM_CSC_TICKET_TXN_AUTOPAY), 
				(Csc.GeneralInfo.bSmartOctopus == TRUE) ? _T("-------") : Csc.GeneralInfo.PurseValue.Format(), 
				CString((FareType == Freight) ? _R(IDS_OCP_TRANS_FREIGHT) : _T("")), 
				mTotal.Format(), 
				Csc.GeneralInfo.DirectDebit.Format(), 
				(Csc.GeneralInfo.bSmartOctopus == TRUE) ? _T("-------") : NewPurse.Format());

			Text += _R( IDS_OCP_CONFIRM );
		}
		else
		{
			NewPurse = Csc.GeneralInfo.PurseValue - Fare;

			Text.Format( _R(IDS_OCP_CONFIRM_CSC_TICKET_TXN), 
				(Csc.GeneralInfo.bSmartOctopus == TRUE) ? _T("-------") : Csc.GeneralInfo.PurseValue.Format(), 
				CString((FareType == Freight) ? _R(IDS_OCP_TRANS_FREIGHT) : _T("")), 
				mTotal.Format(), 
				(Csc.GeneralInfo.bSmartOctopus == TRUE) ? _T("-------") : NewPurse.Format());

			Text += _R( IDS_OCP_CONFIRM );
		}
	}

	if (OcpMessageBoxEx(NULL, Text, (MB_YESNO|MB_ICONQUESTION), RGB_THISTLE, RGB_BLUE50P, 20) != IDYES)
	{
		gPid.Clear();
		gPid.ResetPatronLanguage();		// reset patron language as necessary
		return false;
	}
	else
		return true;
}

BOOL CPostHyfProcessTrip::PrintGroupReceipt(int OrdinaryPatrons, int DeluxePatrons)
{
	CHyfTrip	
		HyfTrip;
	
	int		retCode = IDCANCEL;
	BOOL	bOrdFerry = FALSE;

	CCurrency NewPurse = (Csc.IsAutopayUsed()) ? (Csc.GeneralInfo.PurseValue + Csc.GeneralInfo.DirectDebit) - mTotal : Csc.GeneralInfo.PurseValue - mTotal;
	//CString szPurseValue = _R(IDS_OCP_REMAINING_VALUE) + _T(":\t") + NewPurse.Format() + _T("\n");
	CString szPurseValue = _T("");
    if (Csc.GeneralInfo.bSmartOctopus != 1)
    {
        szPurseValue = _R(IDS_OCP_REMAINING_VALUE) + _T(":\t") + NewPurse.Format() + _T("\n");
    }
	
	if ( ( HyfTrip.GetFerryType() == FERRY_ORDINARY && HyfTrip.IsTripVesselChanged() == FALSE ) ||
		( HyfTrip.GetFerryType() != FERRY_ORDINARY && HyfTrip.IsTripVesselChanged() == TRUE ) )
		bOrdFerry = TRUE;
	else if ( ( HyfTrip.GetFerryType() != FERRY_ORDINARY && HyfTrip.IsTripVesselChanged() == FALSE ) ||
		( HyfTrip.GetFerryType() == FERRY_ORDINARY && HyfTrip.IsTripVesselChanged() == TRUE ) )
		bOrdFerry = FALSE;

	if ( ( bOrdFerry == TRUE && (OrdinaryPatrons > 1 || DeluxePatrons > 1 ) ) || ( bOrdFerry == FALSE && DeluxePatrons > 1 ) )
	{
		// williamto 21Jan2013: NWFF requests prompt for separate/combined group ticket
		XMSGBOXPARAMS xmb;
		xmb.crBackground = RGB_THISTLE; 
		xmb.crText = RGB_BLUE50P; 
		xmb.nTextSize= 20;
		xmb.bUseUserDefinedButtonCaptions = TRUE;
		_tcscpy_s(xmb.UserDefinedButtonCaptions.szOK, 259, _R(IDS_OCP_PRINT_COMBINED_TICKET));	// williamto 10Dec2013: [OP][POST][#issue 121] add resource string to show Chinese
		_tcscpy_s(xmb.UserDefinedButtonCaptions.szCancel, 259, _R(IDS_OCP_PRINT_SEPARATE_TICKET));		// williamto 10Dec2013: [OP][POST][#issue 121] add resource string to show Chinese
		
		retCode = XMessageBox(this->GetSafeHwnd(), 
				(PaymentMethod == PayCsc) ?  szPurseValue + _R(IDS_OCP_GROUP_TICKET_COMB_OR_SEP) : _R(IDS_OCP_GROUP_TICKET_COMB_OR_SEP),
				AfxGetApp()->m_pszAppName, 
				MB_ICONQUESTION|MB_OKCANCEL, &xmb);		

		return (( retCode == IDOK )? TRUE : FALSE);
		// williamto 21Jan2013: NWFF requests prompt for separate/combined group ticket
	}
	else
	{
		return FALSE;
	}
}
bool CPostHyfProcessTrip::DeductGroupTicketsByItem(int *pOrdinaryPatrons, int *pDeluxePatrons, bool GroupSingleTicket)
{
    DWORD Res;
    bool bOk = true;
	CscCardSerialNum_t          SerialNumber;
	SerialNumber.serial_no = Csc.GetLogicalCscId().GetPhysicalId();

    for (int i = 0; i < (int)mItems.size(); i++) {
        CCurrency itemFare = mItems[i].GetItemSubtotal();
        int itemPatronClass = mItems[i].GetItemId();
        BOOL itemIsDeluxe = mItems[i].GetDeluxeTicket();
		CCurrency purse = Csc.GeneralInfo.PurseValue;
		PRINT_DEBUG_TRACE(_T("\r\nDeductGroupTicketsByItem: Processing item %d of %d"), i+1, mItems.size());
        Csc.fIncomplete = TRUE;
        while (Csc.fIncomplete) {
			PRINT_DEBUG_TRACE(_T("\r\nDeductGroupTicketsByItem: Before WriteGroupFare, fIncomplete=%d"), Csc.fIncomplete);
            if (!Csc.WriteTripFare(itemFare, PassUsed, itemIsDeluxe, TripIndex, itemPatronClass,
                                  FareIndicator, CrtSale, CrtValidate, CrtUpgrade, CrtSingleTicket, &Res)) {
				PRINT_DEBUG_TRACE(_T("\r\nDeductGroupTicketsByItem: WriteGroupFare failed, Res=%d, fIncomplete=%d"), Res, Csc.fIncomplete);
                if (Csc.fIncomplete) {
                    RedoAddValue RedoAdd(NULL, SerialNumber, FALSE);
                    if (RedoAdd.DoModal() == IDCANCEL) {
                        Status = UnconfTxnAbandoned;
                        bOk = false;
                        ProcessError();
                        return false;
                    } else {
                        continue;
                    }
                } 
				else {
					PRINT_DEBUG_TRACE(_T("\r\nDeductGroupTicketsByItem: Non-retryable error, Res=%d"), Res);
                    Status = CscWriteError;
					if (Res == OCPERROR_VALIDITY_OVERFLOW)
					{
						PRINT_DEBUG_TRACE(_T("\r\nDeductGroupTicketsByItem: Overflow error"));
						Status = CscValidityOverflow;
					}
					else if ( Res == OCPERROR_CSC_REJECTED )
					{	
						PRINT_DEBUG_TRACE(_T("\r\nDeductGroupTicketsByItem: Transaction rejected by URP"));
						Status = TransactionRejectedByUrp;
					}
					else if ( Res == OCPERROR_TARGET_SEQERR )
					{
						PRINT_DEBUG_TRACE(_T("\r\nDeductGroupTicketsByItem: Double deduction error"));
						Status = CscWriteError;
						break;	// get out!
					}
                    ProcessError();
                    return false;
                }
            }
			else{
				// Generate UD for this item (call ProcessGroupFares for this item only)
				vector<CPosTicketItem> backupItems = mItems;
				vector<CPosTicketItem> singleItemVec;
				singleItemVec.push_back(mItems[i]);
				mItems = singleItemVec;
				int dummyOrd = 0, dummyDel = 0;
				if (purse + Csc.GeneralInfo.NegValLimit - itemFare >= CCurrency(0, 0))
					ProcessGroupFares(true, GroupSingleTicket, &dummyOrd, &dummyDel, TRUE);
				else 
					ProcessGroupFares(true, GroupSingleTicket, &dummyOrd, &dummyDel, FALSE);
				mItems = backupItems;
				if (Csc.IsAutopayUsed() && Csc.GetAutopayAmount()){
					purse += Csc.GeneralInfo.DirectDebit;
				}
				Csc.GeneralInfo.PurseValue = purse - itemFare;
			}
        }
    }
    return bOk;
}
/*void*/ bool CPostHyfProcessTrip::CommitIssue(int *pOrdinaryPatrons, int *pDeluxePatrons, bool SingleJourney, bool GroupSingleTicket, bool bShortCut)
{
	CHyfTrip	HyfTrip;
	int			Result;

	bool		bOk = false;

	//
	// Request the quota from the LDP.  Note that if
	// we are not connected or quota is disabled this
	// will return TRUE anyway (unless the manual quota
	// is exhausted).
	//
	if( HyfTrip.RequestQuota(this, *pDeluxePatrons, *pOrdinaryPatrons, (FareType == GroupPrePaid) ? TRUE : FALSE))
	{
		PRINT_DEBUG_TRACE( _T("\r\nCommitIssue() RequestQuota(), *pDeluxePatrons=%d, *pOrdinaryPatrons=%d"), *pDeluxePatrons, *pOrdinaryPatrons );
		PRINT_DEBUG_TRACE( _T("\r\nCommitIssue() FareType %d PaymentMethod %d, Fare %d"), this->FareType, this->PaymentMethod, Fare.GetValueIn10th());
		
		if (PaymentMethod != PayCash)
		{
			if (FareType == GroupNormal || FareType == GroupPrePaid)
			{
				bOk = DeductGroupTicketsByItem(pOrdinaryPatrons, pDeluxePatrons, GroupSingleTicket);
				Result = bOk;
			}
			else{
				CscCardSerialNum_t          SerialNumber;
				SerialNumber.serial_no = Csc.GetLogicalCscId().GetPhysicalId();
							
				//
				// We must write to the CSC first to finish
				// the transaction (it may not be succesful).
				//
				//
				// Note that if we are a cash deluxe upgrade then
				// the fare on the CSC must be zero, although every
				// thing else will be the same.
				//
				DWORD Res;
				
				Csc.fIncomplete = TRUE;
				
				while ( Csc.fIncomplete )
				{
					if (!Csc.WriteTripFare( ( ( PaymentMethod == PayMp || PaymentMethod == PayDp ) && PassUpgradePaymentMethod == PayCash ) ? CCurrency(0) : mTotal,	// williamto 29Aug2013: FAT failure *********, perform 0-dollar deduction if PassUpgradePaymentMethod=Cash
											PassUsed, IsDeluxe, TripIndex, PatronClass, 
											FareIndicator, CrtSale, CrtValidate, CrtUpgrade, CrtSingleTicket, &Res))
					{
						bOk = false;
						if ( Csc.fIncomplete )
						{
							RedoAddValue	RedoAdd(NULL, SerialNumber, FALSE);

							if ( RedoAdd.DoModal() == IDCANCEL )
							{
								Status = UnconfTxnAbandoned;
								// generate unconfirmed UD
								if (SingleJourney == FALSE )
									Result = ProcessGroupFares(true, GroupSingleTicket, pOrdinaryPatrons, pDeluxePatrons);
								else
									Result = GenerateSjUd();
								
								bOk = false;
								ProcessError();
								break;	
							}
							else
							{
								bOk = true;
								continue;
							}
						}
						else
						{
							Status = CscWriteError;
							if (Res == OCPERROR_VALIDITY_OVERFLOW)
								Status = CscValidityOverflow;
							else if ( Res == OCPERROR_CSC_REJECTED )
								Status = TransactionRejectedByUrp;
							else if ( Res == OCPERROR_TARGET_SEQERR )
							{
								Status = CscWriteError;
								break;	// get out!
							}
				
							bOk = false; 
							ProcessError();
						}
					}
				}
			}
		}
		//
		// make the UD and print the receipts.
		//
/*        if ((*pOrdinaryPatrons + *pDeluxePatrons) > 1) */
		
		if ( Status == Ok )
		{
			if (SingleJourney == FALSE && PaymentMethod == PayCash)
				Result = ProcessGroupFares(true, GroupSingleTicket, pOrdinaryPatrons, pDeluxePatrons);
			else if (SingleJourney == TRUE)
				Result = GenerateSjUd();
		}

		if (Result && Status == Ok)
		{
			bOk = true;
			if (!bShortCut)
				OcpMessageBox(OCPMSG_HYF_TRANSACTIONCOMPLETE);
			HyfTrip.IncrementQuotaUsed( *pDeluxePatrons, *pOrdinaryPatrons, (FareType == GroupPrePaid) ? TRUE : FALSE  );
			if( HyfTrip.GetOperationMode() == CHyfTrip::StandAloneMode && HyfTrip.GetQuotaMode() == CHyfTrip::QuotaEnabled )
			{
				//
				// Show the new quota values if we are
				// in stand-alone mode.
				//
				ShowManualQuotaValues();
			}
			// Re-setup the defaults
			if ( PaymentMethod != PayCash )
			{
				ShowXBlk2ReaderPrompt( &Csc.InvalidCsc );		// williamto 09Nov2013: show alert message all the time after transaction
			}
			// williamto 19Dec2012: clear the succeeded transaction
			ClearPendingTransactionList();
			SetupControls(FareType);
			gPid.Clear();
			gPid.ResetPatronLanguage();		// reset patron language as necessary
		}
		else
		{
			//
			// Display an error and restore and return the quota
			//
			bOk = false;
			ProcessError();
			HyfTrip.RestoreQuota(*pDeluxePatrons, *pOrdinaryPatrons, (FareType == GroupPrePaid) ? TRUE : FALSE);
			HyfTrip.ReturnQuota();
		}
	}
	else
	{
		bOk = false;
		ProcessError(NotEnoughQuota);
	}
	return bOk;
}
	
/*void*/ bool CPostHyfProcessTrip::CommitIssue(bool bShortCut)
{
	int OrdinaryPatrons = 0;
	int DeluxePatrons   = 0;

	if (IsDeluxe)
		DeluxePatrons	= 1;
	else
		OrdinaryPatrons = 1;

	return CommitIssue(&OrdinaryPatrons, &DeluxePatrons, TRUE, FALSE, bShortCut);
}

void CPostHyfProcessTrip::ProcessSingleJourney(bool bCutPrompt, bool bShortCut)
{
	int			retCode = IDYES;
	BOOL		bUpgradeByCashOnly = FALSE;
	Result_t	InvalidReason = Ok;

	if ((PaymentMethod == PayCsc) || (PaymentMethod == PayMp) || (PaymentMethod == PayDp) )
	{
		/* add check for same CSC if paid by CSC */
		DWORD res;
		if ( ( res = OcpRepollCsc() ) != ERROR_SUCCESS )
		{
			OcpMessageBox( OCPMSG_ERROR, res );
			return;
		}
	}

	if (!CalculateSjFare())
	{
		ProcessError();
		return;
	}
	
	if ((PaymentMethod == PayCsc) ||
		((PaymentMethod == PayMp || PaymentMethod == PayDp ) &&
		  IsDeluxe && 
		  !RadioDeluxe.GetCheck() ))
	{
		if ( !ValidCsc(true, &InvalidReason) )
		{
			// williamto 11Sept2013: FAT failure *******, still allow passenger to use MT/DP when insufficient fund or exceeded autopay but make them pay cash
			if ( ( InvalidReason == MaxAutopayExceeded || InvalidReason == InsufficientFunds ) && ( PaymentMethod == PayMp || PaymentMethod == PayDp ) )
				bUpgradeByCashOnly = TRUE;
			else
			{
				ProcessError( InvalidReason );
				return;
			}
		}

// williamto 22Jan2013: NWFF requests to allow Operator to select upgrade payment by cash or CSC at the confirmation prompt
		if ( PaymentMethod == PayMp || PaymentMethod == PayDp && IsDeluxe && !RadioDeluxe.GetCheck() ) 
		{
			gPid.ShowHyfFare(0, 0, FALSE, PassUsed, FALSE, Csc.IsAutopayUsed(), Csc.GeneralInfo.DirectDebit, Csc.GeneralInfo.bSmartOctopus);		// show "MT valid for travel" prompt even for upgrade
			
			if ( !bUpgradeByCashOnly )
			{
				CString szPrompt = _R(IDS_OCP_PASS_UPGRADE_DLX) ;
				szPrompt.Format( _R(IDS_OCP_PASS_UPGRADE_DLX), 
					(PassUsed == CHyfFareCalculation::MonthlyPass1 || PassUsed == CHyfFareCalculation::MonthlyPass2 ) ? _R( IDS_OCP_MT ) : _R( IDS_OCP_DP ), 
					Fare.Format());

				szPrompt += _R(IDS_OCP_CONFIRM);

				XMSGBOXPARAMS xmb;
				xmb.crBackground = RGB_THISTLE; 
				xmb.crText = RGB_BLUE50P; 
				xmb.nTextSize= 20;
				xmb.bUseUserDefinedButtonCaptions = TRUE;
				_tcscpy_s(xmb.UserDefinedButtonCaptions.szYes, 259, _R(IDS_OCP_BTNLABEL_CASH_PAYMENT));
				_tcscpy_s(xmb.UserDefinedButtonCaptions.szNo, 259, _R(IDS_OCP_BTNLABEL_CSC_PAYMENT));
		
				retCode = XMessageBox(this->GetSafeHwnd(), 
						szPrompt, 
						AfxGetApp()->m_pszAppName, 
						MB_ICONQUESTION|MB_YESNOCANCEL, &xmb);		

				if (retCode == IDCANCEL)
				{
					gPid.Clear();
					gPid.ResetPatronLanguage();
					OnClearAll();
					return;
				}
				else if (retCode == IDYES)
					PassUpgradePaymentMethod = PayCash;		// williamto 29Aug2013: FAT failure *********, separate the payment method with upgrade surcharge
				else if (retCode == IDNO)
					PassUpgradePaymentMethod = PayCsc;
			}
			else
			{
				PassUpgradePaymentMethod = PayCash;
			}
		}
	}

	// Display the transaction on the PID
	gPid.ShowHyfFare((PaymentMethod == PayCash ) ? 0 : Csc.GeneralInfo.PurseValue, 
						mTotal/*Fare*/, 
						( PaymentMethod == PayCash || (PassUpgradePaymentMethod == PayCash && (PaymentMethod == PayMp || PaymentMethod == PayDp) ) ), 
						PassUsed, 
						IsDeluxe, 
						Csc.IsAutopayUsed(), 
						Csc.GeneralInfo.DirectDebit, 
						Csc.GeneralInfo.bSmartOctopus
					);
	if (bShortCut && bCutPrompt)
	{
		CommitIssue(bShortCut);
	}
	else 
	{
		if (CustomerAcceptFare())
			CommitIssue(bShortCut);
		else
			OnClearAll();
	}
}

void CPostHyfProcessTrip::ProcessComplementary()
{
	int	OrdinaryPatrons, DeluxePatrons;

	Fare           = 0;
	//HyfPatronClass = MapPatronIndexToClass(PatronIndex, &PatronClass);
	FareCalcTime     = CDateTime::GetCurrentTime();

	if (!ProcessGroupFares(false, false, &OrdinaryPatrons, &DeluxePatrons))	// 29Jan2013: process complementary tickets as groups
	{
		ProcessError();
		return;
	}

	// set the fare indicator here as we aren't doing any fare calcs.
	if (IsDeluxe)
		FareIndicator |= FsFirstClass;
	else
		FareIndicator  = 0;

	// Display the transaction on the PID
	gPid.ShowHyfFare(0 , Fare, TRUE, 0, IsDeluxe, 0, Csc.GeneralInfo.DirectDebit, Csc.GeneralInfo.bSmartOctopus);
	if (CustomerAcceptFare())
		CommitIssue( &OrdinaryPatrons, &DeluxePatrons, false, true, false );	//CommitIssue(false);
	else
		OnClearAll();
}

/*void*/ bool CPostHyfProcessTrip::ProcessConcessionSale(/*int OrdinaryPatrons, int DeluxePatrons,*/ bool bCutPrompt, bool bShortCut, bool bCutPidDisplay, bool bCheckAntiPassback)
{
	int		OrdinaryPatrons = 0;
	int		DeluxePatrons = 0;
	//int		numItems = 0;
	CRegistrationNumber			HrtRegistrationNumber;
	//CRegistrationNumberGenerator RegistrationNumberGenerator(&gLogIO, gEod.ServiceProviderId(), gEod.HrtPostId());
	
	if (!CalculateCrtFare())	// process single HRT sale
	{
		ProcessError();
		return false;
	}

	if (!ProcessGroupFares(false, false, &OrdinaryPatrons, &DeluxePatrons))	// 29Jan2013: process complementary tickets as groups
	{
		ProcessError();
		return false;
	}

	if (HrtPaymentMethod == HrtPayCash)
	{
		// once known number of actual patrons for ordinary and deluxe, generate the HRT here first
		CWaitCursor wc;

		// Display a Please wait message (destroyed when out of scope)
		CString		szWaitMessage;
		CDialog		HrtWaitMsg;		// display of "wait for HRT" dialog 
		HrtWaitMsg.Create(IDD_OCPDIALOG_PLEASEWAIT,NULL);
		HrtWaitMsg.ShowWindow(SW_SHOW);
			
		//for (numItems = 0; numItems < mItems.size(); numItems++)
		for (vector<CPosTicketItem>::iterator it = mItems.begin(); it != mItems.end(); it++)
		{
			int qty = 0;
			int totalQty = it->GetItemQty(); //mItems[numItems].GetItemQty();
			vector<CRegistrationNumber> vecHrtRegistrationNumber;
	
			for (qty=0; qty</*mItems[numItems].GetItemQty()*/it->GetItemQty(); qty++)	// for each ticket in the passenger group
			{
				szWaitMessage.Format(_T("%s\n\n%d/%d"), _R(IDS_OCP_HYFMSG_WAITREQUESTHRTREG), qty+1, totalQty );
				HrtWaitMsg.SetDlgItemText(IDC_WAIT_MESSAGE, szWaitMessage);
				HrtWaitMsg.UpdateWindow();
				// find the next registration number 
				if (RegistrationNumberGenerator.FindNextRegistrationNumber(HrtRegistrationNumber) == true)
				{
					vecHrtRegistrationNumber.push_back(HrtRegistrationNumber);
					::YieldToWindows();
				}
				else
				{
					HrtWaitMsg.SetDlgItemText(IDC_WAIT_MESSAGE, _R(IDS_OCP_HYFMSG_WAITRETURNHRTREG) );
					HrtWaitMsg.UpdateWindow();
					// return any generated HRT registration numbers to the pool
					for (std::vector<CRegistrationNumber>::iterator it = vecHrtRegistrationNumber.begin(); it != vecHrtRegistrationNumber.end(); it++)
					{
						RegistrationNumberGenerator.ReleaseRegistrationNumber(it->DevIDRegNumber());
						::YieldToWindows();
					}

					vecHrtRegistrationNumber.clear();
					vecHrtRegistrationNumberVector.clear();

					Status = HrtCashOutOfRegNumber;
					ProcessError(HrtCashOutOfRegNumber);
					return false;
				}
			}
			vecHrtRegistrationNumberVector.push_back(vecHrtRegistrationNumber);
			::YieldToWindows();
		}
	}

	if (PaymentMethod == PayCsc)
	{
		if (!ValidCsc(bCheckAntiPassback))
			return false;

		// check if this is an encoding transaction
		if (HrtPaymentMethod != HrtPayEncodeCsc)
		{
			this->IsDeluxe = false;		// can ignore the deluxe indicator as the fare price already reflects ordinary/deluxe
		}
		// check if this is an encoding transaction
	}

//	CalcItemSubtotal();
	mTotal = CCurrency(0,0);
	int i=0;
	for (i=0; i<(int)mItems.size(); i++)
	{
		mTotal += mItems[i].GetItemSubtotal();
	}

	// Display the transaction on the PID
	if (bCutPidDisplay == false)
	{
		gPid.ShowHyfConcession((PaymentMethod == PayCash) ? 0 : Csc.GeneralInfo.PurseValue, Fare, 
			(PaymentMethod == PayCash), Csc.IsAutopayUsed(), Csc.GeneralInfo.DirectDebit, Csc.GeneralInfo.bSmartOctopus);
	}

	if (bCutPrompt && bShortCut)
	{
		CrtSale = true;
		return CommitIssue(&OrdinaryPatrons, &DeluxePatrons, FALSE, /*PrintGroupReceipt( OrdinaryPatrons, DeluxePatrons )*/FALSE, bShortCut);
			//CommitIssue(bShortCut);
	}
	else
	{
		if (CustomerAcceptFare())
		{
			mTotal = Fare;
			CrtSale = true;
			return CommitIssue(&OrdinaryPatrons, &DeluxePatrons, FALSE, /*PrintGroupReceipt( OrdinaryPatrons, DeluxePatrons )*/FALSE, bShortCut); //CommitIssue(bShortCut);
		}
		else
		{
			this->ReturnHrtRegNumbers();
			OnClearAll();
		}
	}
	return false;
}

/*void*/ bool CPostHyfProcessTrip::ProcessConcessionCheck(/*int OrdinaryPatrons, int DeluxePatrons, */ bool bCutPrompt, bool bShortCut, bool bCutPidDisplay)
{
	int		OrdinaryPatrons = 0;
	int		DeluxePatrons = 0;
	//int		numItems = 0;
	CRegistrationNumber			HrtRegistrationNumber;
	//CRegistrationNumberGenerator RegistrationNumberGenerator(&gLogIO, gEod.ServiceProviderId(), gEod.HrtPostId());
	
	CHyfTrip		HyfTrip;
	
	Fare           = 0;
	HyfPatronClass = MapPatronIndexToClass(PatronIndex, &PatronClass);
	FareIndicator  = 0;
	FareCalcTime     = CDateTime::GetCurrentTime();
	Csc.IslandNum  = gEod.HyfGetIslandNum(HyfTrip.GetDestinationTerminal());

	if (!ProcessGroupFares(false, false, &OrdinaryPatrons, &DeluxePatrons))	// 29Jan2013: process complementary tickets as groups
	{
		ProcessError();
		return false;
	}

	if (HrtPaymentMethod == HrtPayCash)
	{
		CWaitCursor wc;
		
		// Display a Please wait message (destroyed when out of scope)
		CString		szWaitMessage;
		CDialog		HrtWaitMsg;		// display of "wait for HRT" dialog 
		HrtWaitMsg.Create(IDD_OCPDIALOG_PLEASEWAIT,NULL);
		HrtWaitMsg.ShowWindow(SW_SHOW);

		// once known number of actual patrons for ordinary and deluxe, generate the HRT here first
		//for (numItems = 0; numItems < mItems.size(); numItems++)
		for (vector<CPosTicketItem>::iterator it = mItems.begin(); it != mItems.end(); it++)
		{
			int qty = 0;
			int totalQty = it->GetItemQty(); //mItems[numItems].GetItemQty();
			vector<CRegistrationNumber> vecHrtRegistrationNumber;
	
			if (/*mItems[numItems].*/it->GetDeluxeTicket() == TRUE)
			{
				for (qty=0; qty</*mItems[numItems].*/it->GetItemQty(); qty++)	// for each ticket in the passenger group
				{
					szWaitMessage.Format(_T("%s\n\n%d/%d"), _R(IDS_OCP_HYFMSG_WAITREQUESTHRTREG), qty+1, totalQty );
					HrtWaitMsg.SetDlgItemText(IDC_WAIT_MESSAGE, szWaitMessage);
					HrtWaitMsg.UpdateWindow();
					// find the next registration number 
					if (RegistrationNumberGenerator.FindNextRegistrationNumber(HrtRegistrationNumber) == true)
					{
						vecHrtRegistrationNumber.push_back(HrtRegistrationNumber);
						::YieldToWindows();
					}
					else
					{
						HrtWaitMsg.SetDlgItemText(IDC_WAIT_MESSAGE, _R(IDS_OCP_HYFMSG_WAITRETURNHRTREG) );
						HrtWaitMsg.UpdateWindow();
						// return any generated HRT registration numbers to the pool
						for (std::vector<CRegistrationNumber>::iterator it = vecHrtRegistrationNumber.begin(); it != vecHrtRegistrationNumber.end(); it++)
						{
							RegistrationNumberGenerator.ReleaseRegistrationNumber(it->DevIDRegNumber());
						}

						vecHrtRegistrationNumber.clear();
						vecHrtRegistrationNumberVector.clear();

						Status = HrtCashOutOfRegNumber;
						ProcessError(HrtCashOutOfRegNumber);
						return false;
					}
				}
				vecHrtRegistrationNumberVector.push_back(vecHrtRegistrationNumber);	
			}
			else
			{
				vecHrtRegistrationNumber.push_back(CRegistrationNumber());	// placeholder to retain the HRT registration vector structure
				vecHrtRegistrationNumberVector.push_back(vecHrtRegistrationNumber);
			}
		}
	}

	if (PaymentMethod == PayCsc)
	{
		if (!ValidCsc(true))
			return false;

		// check if this is an encoding transaction
		if (HrtPaymentMethod != HrtPayEncodeCsc)
		{
			this->IsDeluxe = false;		// can ignore the deluxe indicator as the fare price already reflects ordinary/deluxe
		}
		// check if this is an encoding transaction
	}
  
	//CalcItemSubtotal();
	// calculate total amount, received amount and change amount
	mTotal = CCurrency(0,0);
	int i=0;
	for (i=0; i<(int)mItems.size(); i++)
	{
		mTotal += mItems[i].GetItemSubtotal();
	}

	// Display the transaction on the PID
	if (bCutPidDisplay == false)
	{
		gPid.ShowHyfConcession( 0, Fare, 1, 0, Csc.GeneralInfo.DirectDebit, Csc.GeneralInfo.bSmartOctopus);	// 30Jan2013: move to HyfIssTicket::OnOK
	}

	if ( bCutPrompt )
	{
		CrtValidate = true;
		if ( DeluxePatrons > 0 )
		{
			CrtUpgrade = true;
		}
		return CommitIssue(&OrdinaryPatrons, &DeluxePatrons, FALSE, FALSE, /*PrintGroupReceipt( OrdinaryPatrons, DeluxePatrons ),*/ bShortCut);
	}
	else 
	{
		if (CustomerAcceptFare())
		{
			CrtValidate = true;
			if ( DeluxePatrons > 0 )
			{
				CrtUpgrade = true;
			}

			return (bool)(CommitIssue(&OrdinaryPatrons, &DeluxePatrons, FALSE, FALSE/*PrintGroupReceipt( OrdinaryPatrons, DeluxePatrons )*/, bShortCut));
		}
		else
		{
			this->ReturnHrtRegNumbers();

			OnClearAll();
		}
	}
	return false;
}

void CPostHyfProcessTrip::ProcessFreight()
{
	if(!ProcessFreightFare(false))
	{
		ProcessError();
		UpdateData(FALSE);
		return;
	}

	if (PaymentMethod == PayCsc)
	{
		if (!ValidCsc(false))
			return;
	}
	// Display the transaction on the PID
	gPid.ShowHyfFreight(Csc.GeneralInfo.PurseValue, Fare, (PaymentMethod == PayCash), 
						Csc.IsAutopayUsed(), Csc.GeneralInfo.DirectDebit, 
						Csc.GeneralInfo.bSmartOctopus);
	if (!CustomerAcceptFare())
		return;

	
	if (PaymentMethod != PayCash)
	{
		if (!Csc.WriteFreightFare(Fare))
		{
			ProcessError(CscWriteError);
			return;
		}
	}
	//
	// make the UD and print the receipts.
	//
	ProcessFreightFare(true);
	OcpMessageBox(OCPMSG_HYF_TRANSACTIONCOMPLETE);

}

void CPostHyfProcessTrip::ProcessGroup(bool GroupSingleTicket)
{
	int	OrdinaryPatrons, DeluxePatrons;
	
	CPostHyfProcessTrip::Result_t RealInvalidReason = Ok;	// williamto 25Oct2013: FAT observation *******, handle autopay exceeded limit

	if (PaymentMethod == PayCsc)
	{
		/* add check for same CSC if paid by CSC */
		DWORD res;
		if ( ( res = OcpRepollCsc() ) != ERROR_SUCCESS )
		{
			OcpMessageBox( OCPMSG_ERROR, res );
			return;
		}
	}
	if (!ProcessGroupFares(false, false, &OrdinaryPatrons, &DeluxePatrons))
	{
		ProcessError();
		return;
	}
	// check if we have enough on the CSC (if we
	// are using one).
	if (PaymentMethod == PayCsc)
	{
		if (!ValidAdultCsc(true, &RealInvalidReason))	// williamto 30Aug2013: FAT failure *********, disallow purchase group tickets for CSC != ADULT
		{
			ProcessError(RealInvalidReason);		// williamto 25Oct2013: FAT observation *******, handle the actual failure reason here once
			return;
		}
	}
	// Display the transaction on the PID
	gPid.ShowHyfGroup(Csc.GeneralInfo.PurseValue, Fare, (PaymentMethod == PayCash), Csc.IsAutopayUsed(), Csc.GeneralInfo.DirectDebit, Csc.GeneralInfo.bSmartOctopus);

	if (CustomerAcceptFare())
	{
//		int	retCode;
//
//		CString szPurseValue;
//		szPurseValue = _R(IDS_OCP_REMAINING_VALUE) + _T(":\t") + Csc.GeneralInfo.PurseValue.Format() + _T("\n");
//
//// williamto 21Jan2013: NWFF requests prompt for separate/combined group ticket
//		XMSGBOXPARAMS xmb;
//		xmb.crBackground = RGB_THISTLE; 
//		xmb.crText = RGB_BLUE50P; 
//		xmb.nTextSize= 20;
//		xmb.bUseUserDefinedButtonCaptions = TRUE;
//		_tcscpy_s(xmb.UserDefinedButtonCaptions.szOK, 259, _R(IDS_OCP_PRINT_COMBINED_TICKET));	// williamto 10Dec2013: [OP][POST][#issue 121] add resource string to show Chinese
//		_tcscpy_s(xmb.UserDefinedButtonCaptions.szCancel, 259, _R(IDS_OCP_PRINT_SEPARATE_TICKET));		// williamto 10Dec2013: [OP][POST][#issue 121] add resource string to show Chinese
//		
//		retCode = XMessageBox(this->GetSafeHwnd(), 
//				(PaymentMethod == PayCsc) ?  szPurseValue + _R(IDS_OCP_GROUP_TICKET_COMB_OR_SEP) : _R(IDS_OCP_GROUP_TICKET_COMB_OR_SEP),
//				AfxGetApp()->m_pszAppName, 
//				MB_ICONQUESTION|MB_OKCANCEL, &xmb);		
//// williamto 21Jan2013: NWFF requests prompt for separate/combined group ticket

		(bool)(CommitIssue(&OrdinaryPatrons, &DeluxePatrons, FALSE, PrintGroupReceipt( OrdinaryPatrons, DeluxePatrons ), false));
	}
	else
		OnClearAll();
}

/*void*/ bool CPostHyfProcessTrip::ProcessCrtUpgrade(/*int OrdinaryPatrons, int DeluxePatrons, */int OldFareClass, int NewFareClass, bool bCutPrompt, bool bShortCut, bool bCheckAntiPassback)
{
	int OrdinaryPatrons = 0;
	int DeluxePatrons = 0;
	//int		numItems = 0;
	CRegistrationNumber			HrtRegistrationNumber;
	//CRegistrationNumberGenerator RegistrationNumberGenerator(&gLogIO, gEod.ServiceProviderId(), gEod.HrtPostId());
	
	//CFareCalculation::Status_t	Result;
	CHyfFareCalculation			FareCalc;
	CHyfTrip					HyfTrip;

	CrtOrgFareClass = OldFareClass;
	CrtCurFareClass = NewFareClass;

	FareCalcTime   = CDateTime::GetCurrentTime();
	Csc.IslandNum  = gEod.HyfGetIslandNum(HyfTrip.GetDestinationTerminal());

	Fare           = 0;
	HyfPatronClass = MapPatronIndexToClass(PatronIndex, &PatronClass);
	FareIndicator  = 0;
	FareCalcTime     = CDateTime::GetCurrentTime();
	Csc.IslandNum  = gEod.HyfGetIslandNum(HyfTrip.GetDestinationTerminal());

	if (!ProcessGroupFares(false, false, &OrdinaryPatrons, &DeluxePatrons))	// 29Jan2013: process complementary tickets as groups
	{
		ProcessError();
		return false;
	}

	if (HrtPaymentMethod == HrtPayCash) 
	{
		CWaitCursor wc;
		
		// Display a Please wait message (destroyed when out of scope)
		CString		szWaitMessage;
		CDialog		HrtWaitMsg;		// display of "wait for HRT" dialog 
		HrtWaitMsg.Create(IDD_OCPDIALOG_PLEASEWAIT,NULL);
		HrtWaitMsg.ShowWindow(SW_SHOW);

		// once known number of actual patrons for ordinary and deluxe, generate the HRT here first
		//for (numItems = 0; numItems < mItems.size(); numItems++)
		for (vector<CPosTicketItem>::iterator it = mItems.begin(); it != mItems.end(); it++)
		{
			int qty = 0;
			int	totalQty = /*mItems[numItems].*/it->GetItemQty();
			vector<CRegistrationNumber> vecHrtRegistrationNumber;
	
			if (/*mItems[numItems].*/it->GetDeluxeTicket() != FALSE)
			{
				for (qty=0; qty</*mItems[numItems].*/it->GetItemQty(); qty++)	// for each ticket in the passenger group
				{
					szWaitMessage.Format(_T("%s\n\n%d/%d"), _R(IDS_OCP_HYFMSG_WAITREQUESTHRTREG), qty+1, totalQty );
					HrtWaitMsg.SetDlgItemText(IDC_WAIT_MESSAGE, szWaitMessage);
					HrtWaitMsg.UpdateWindow();
					// find the next registration number 
					if (RegistrationNumberGenerator.FindNextRegistrationNumber(HrtRegistrationNumber) == true)
					{
						vecHrtRegistrationNumber.push_back(HrtRegistrationNumber);
						::YieldToWindows();
					}
					else
					{
						HrtWaitMsg.SetDlgItemText(IDC_WAIT_MESSAGE, _R(IDS_OCP_HYFMSG_WAITRETURNHRTREG) );
						HrtWaitMsg.UpdateWindow();

						// return any generated HRT registration numbers to the pool
						for (std::vector<CRegistrationNumber>::iterator it = vecHrtRegistrationNumber.begin(); it != vecHrtRegistrationNumber.end(); it++)
						{
							RegistrationNumberGenerator.ReleaseRegistrationNumber(it->DevIDRegNumber());
						}

						vecHrtRegistrationNumber.clear();
						vecHrtRegistrationNumberVector.clear();

						Status = HrtCashOutOfRegNumber;
						ProcessError(HrtCashOutOfRegNumber);
						return false;
					}
				}
				vecHrtRegistrationNumberVector.push_back(vecHrtRegistrationNumber);	
			}
			else
			{
				vecHrtRegistrationNumber.push_back(CRegistrationNumber());	// placeholder to retain the HRT registration vector structure
				vecHrtRegistrationNumberVector.push_back(vecHrtRegistrationNumber);
			}
		}
	}

	if (PaymentMethod == PayCsc)
	{
		if (!ValidCsc(bCheckAntiPassback))
			return false;

		// check if this is an encoding transaction
		if (HrtPaymentMethod != HrtPayEncodeCsc)
		{
			this->IsDeluxe = false;		// can ignore the deluxe indicator as the fare price already reflects ordinary/deluxe
		}
		// check if this is an encoding transaction
	}
  
	mTotal = CCurrency(0,0);
	int i=0;
	for (i=0; i<(int)mItems.size(); i++)
	{
		mTotal += mItems[i].GetItemSubtotal();
	}

	PRINT_DEBUG_TRACE(_T("\r\nmTotal=%s"), mTotal.Format() );

	if (bCutPrompt && bShortCut)
	{
		CrtUpgrade	= true;

		return CommitIssue(&OrdinaryPatrons, &DeluxePatrons, FALSE, FALSE/*PrintGroupReceipt( OrdinaryPatrons, DeluxePatrons )*/, bShortCut);
	}
	else
	{
		// Display the transaction on the PID
		gPid.ShowHyfCrtUpgrade((PaymentMethod == PayCash) ? 0 : Csc.GeneralInfo.PurseValue, mTotal, 
						 (PaymentMethod == PayCash), Csc.IsAutopayUsed(), 
						 Csc.GeneralInfo.DirectDebit, 
						 Csc.GeneralInfo.bSmartOctopus);
	
		if (CustomerAcceptFare())
		{
			CrtUpgrade	= true;
			return (bool)(CommitIssue(&OrdinaryPatrons, &DeluxePatrons, FALSE, FALSE/*PrintGroupReceipt( OrdinaryPatrons, DeluxePatrons )*/, bShortCut));
		}
		else
		{
			this->ReturnHrtRegNumbers();

			OnClearAll();
		}
	}
	return false;
}

void CPostHyfProcessTrip::OnTripCrtUpgrade() 
{
	// TODO: Add your control notification handler code here
	SetupControls(CRT_UPGRADE);
}

void CPostHyfProcessTrip::OnTripIssueTicketCombo()
{
  OnTripIssueTicket(FALSE);
}

void CPostHyfProcessTrip::OnTripIssueTicketSingleGroup()
{
  OnTripIssueTicket(TRUE);
}

//BOOL CPostHyfProcessTrip::OnSetActive()
//{
///*	CHyfTrip
//		HyfTrip;
//
//	if (OcpVerifyAppTransactionState())
//    {
//		if (!HyfTrip.IsInTrip())
//			OcpMessageBox(_T("No trip has been started"));
//	}
//	// williamto 17072012: return OnSetActive no matter what */
//	return COCPPropertyPage::OnSetActive();
//}

void CPostHyfProcessTrip::ProcessItemAndQty(void)
{
	//int	OrdinaryPatrons, DeluxePatrons;

	BOOL	GroupSingleTicket = FALSE;

	CHyfFareCalculation FareCalc;
	CHyfTrip			HyfTrip;

	CHyfFareCalculation::Status_t FareCalcResult = CHyfFareCalculation::Status_t::Ok;

	// process the last input quantity
	if ( mQty > 0 )
	{
		switch (FareType)
		{
			case SingleJourney:
			case ConcessionSale:
			case ConcessionCheck:
			case CRT_UPGRADE:
				if ( mSequence == COCPPosDialog::POS_EVENT_ENTER_ITEM )
				{
					HyfPatronClass = MapPatronIndexToClass(PatronIndex);

					if (FareType == SingleJourney)
					{
						//if (!CalculateSjFare())
						FareCalcResult = FareCalc.CalculateFare(HyfPatronClass, 
							HyfTrip.GetSailingTime(), 
							HyfTrip.GetOriginTerminal(), 
							HyfTrip.GetDestinationTerminal(), 
							HyfTrip.GetFerryType(), 
							IsDeluxe);
					}
					else if (FareType == ConcessionCheck || FareType == CRT_UPGRADE)
					{
						CrtSingleTicket = (HrtPaymentMethod == HrtPayEncodeCsc) ? true : false;	// no need to encode the HRT information even when paid by CSC
						
						m_nOriginFareClass = ORDINARY_ORDINARY;		// regard entitled return trip is ordinary class ordinary ferry only

						FareCalcResult = FareCalc.CalculateCrtUpgrade(HyfPatronClass, 
							HyfTrip.GetSailingTime(), 
							HyfTrip.GetOriginTerminal(), 
							HyfTrip.GetDestinationTerminal(), 
							m_nOriginFareClass, 
							gEod.HyfGetFareClass(HyfTrip.GetFerryType(), this->IsDeluxe));

						if ( FareType == ConcessionCheck && this->IsDeluxe == TRUE)
							FareType = CRT_UPGRADE;

					}
					else	// concession sale
					{
						CrtSingleTicket = (HrtPaymentMethod == HrtPayEncodeCsc) ? true : false;	// no need to encode the HRT information even when paid by CSC
						
						FareCalcResult = FareCalc.GetConcessionReturnFare(HyfTrip.GetOriginTerminal(), 
							HyfTrip.GetDestinationTerminal(), 
							HyfTrip.GetFerryType(), 
							IsDeluxe, 
							HyfPatronClass);
					}

					if (FareCalcResult != CHyfFareCalculation::Ok)
					{
						Status = CPostHyfProcessTrip::FareCalculationError;
						ProcessError();
					}
					else
					{
						Fare = FareCalc.GetFare();
						AddItem( /*desc*/HyfPatronClass, IsDeluxe, mQty, Fare);
						//CString desc = gEod.HyfPatronTypeStr(HyfPatronClass) + _T("-") + gEod.HyfTravelClassStr((IsDeluxe) ? 1 : 0);
						//m_ctlList.ResetContent();
					}
				}
				else if (mSequence == POS_EVENT_ENTER_QTY)
				{
					if ((FareType == ConcessionSale) || (FareType == ConcessionCheck) || (FareType == CRT_UPGRADE))	// only process the updated quantity for CRT related sales and ignore SJT
					{
						// this is only applicable for ConcessionSale && PayCash
						CrtSingleTicket = (HrtPaymentMethod == HrtPayEncodeCsc) ? true : false;	// no need to encode the HRT information even when paid by CSC
					
						if (HrtPaymentMethod != HrtPayEncodeCsc)
						{
							if ((FareType == ConcessionSale))
							{
								// allow multiple tickets to be sold for HRT
								FareCalcResult = FareCalc.GetConcessionReturnFare(HyfTrip.GetOriginTerminal(), 
									HyfTrip.GetDestinationTerminal(), 
									HyfTrip.GetFerryType(), 
									IsDeluxe, 
									HyfPatronClass);
							}
							else if ((FareType == ConcessionCheck) || (FareType == CRT_UPGRADE))
							{
								m_nOriginFareClass = CrtOrgFareClass = ORDINARY_ORDINARY;
								CrtCurFareClass = gEod.HyfGetFareClass(HyfTrip.GetFerryType(), this->IsDeluxe);
						
								FareCalcResult = FareCalc.CalculateCrtUpgrade(HyfPatronClass, 
									HyfTrip.GetSailingTime(), 
									HyfTrip.GetOriginTerminal(), 
									HyfTrip.GetDestinationTerminal(), 
									m_nOriginFareClass, 
									CrtCurFareClass);
							}

							if (FareCalcResult != CHyfFareCalculation::Ok)
							{
								Status = CPostHyfProcessTrip::FareCalculationError;
								ProcessError();
							}
							else
							{
								Fare = FareCalc.GetFare();
								ModifyItem( m_ctlList.GetCount()-1, IsDeluxe, mQty, Fare);
							}
						}
					}
				}
				break;


			//case ConcessionSale:
			//	break;

			//case ConcessionCheck:
			//	break;

			case Freight:
				break;

			case GroupNormal:
				GroupSingleTicket = TRUE;
			case GroupPrePaid:
			case Complementary:
				//if (IsDeluxe)
				//	m_DeluxePatrons[PatronIndex] = mQty;
				//else
				//	m_OrdinaryPatrons[PatronIndex] = mQty;
				//
				if ( mSequence == COCPPosDialog::POS_EVENT_ENTER_QTY ) 
				{
					HyfPatronClass = MapPatronIndexToClass(PatronIndex);
					
					if (FareCalc.CalculateFare(HyfPatronClass, 
						HyfTrip.GetSailingTime(), 
						HyfTrip.GetOriginTerminal(), 
						HyfTrip.GetDestinationTerminal(), 
						HyfTrip.GetFerryType(), 
						IsDeluxe) != CHyfFareCalculation::Ok)
					{
						Status = CPostHyfProcessTrip::FareCalculationError;
						ProcessError();
						return;
					}
					else
					{
						// Get the fare
						if ( FareType == Complementary) 
						{
							Fare = CCurrency(0, 0);
						}
						else
						{
							Fare = (FareType == GroupPrePaid) ? 0 : FareCalc.GetFare();
						}
						//AddItem( /*desc*/HyfPatronClass, IsDeluxe, mQty, Fare);
						ModifyItem( m_ctlList.GetCount()-1, IsDeluxe, mQty, Fare );	// assume modify the last item in the list
					}
				}
				break;

			//case CRT_UPGRADE:
			//	break;
			
			default:
				break;
		}
	}

	// reset the entries
	//mQty = 0;
	//mEnteredValue = _T("");
}

afx_msg void CPostHyfProcessTrip::OnPosRecv(void)
{
	if (PaymentMethod != PayCash)
		return;

	//ProcessItemAndQty();
	mQty = 0;
	mEnteredValue = _T("");
	mChange = 0;	// williamto 17Jan2014: [OP][OCP][POST] don't have to calculate the change by defualt, only calculate when press "recv/chng" button
	mSequence = COCPPosDialog::POS_EVENT_ENTER_RECV;

	// calculate total amount, received amount and change amount
	Fare = mTotal = CCurrency(0,0);
	for (unsigned int i=0; i<mItems.size(); i++)
	{
		mTotal += mItems[i].GetItemSubtotal();
	}

	mRecv = mTotal;	// williamto 17Jan2014: [OP][OCP][POST] don't have to calculate the change by defualt, only calculate when press "recv/chng" button

	UpdateData(FALSE);
}

afx_msg void CPostHyfProcessTrip::OnClr(void)
{
	COCPPosDialog::OnClr();

	switch ( mSequence )
	{
		case COCPPosDialog::POS_EVENT_ENTER_QTY:
			ModifyItem( m_ctlList.GetCount()-1, IsDeluxe, mQty, Fare );	// assume modify the last item in the list
		break;

	}
	UpdateData(FALSE);
}

afx_msg void CPostHyfProcessTrip::OnOK(void)
{
	CString title;
	CString szEnterPrompt;
	GetWindowText(title);
	szEnterPrompt.Format( IDS_OCP_ENTER_TICKET_ITEM, title );
		
	vector<CPosTicketItem>::iterator itor = mItems.begin();
	//ProcessItemAndQty();
	mQty = 0;
	mEnteredValue = _T("");

	// calculate total amount, received amount and change amount
	if ( m_ctlList.GetCount() == 0 )
	{
		OcpMessageBox( szEnterPrompt, MB_ICONERROR|MB_OK);
		return;
	}
	
	Fare = mTotal = CCurrency(0,0);
	//for (unsigned int i=0; i<mItems.size(); i++)
	for ( itor = mItems.begin(); itor != mItems.end(); itor++ )
	{
		if ( itor->GetItemId() != -1 && itor->GetItemQty() < 1 )
		{
			OcpMessageBox( szEnterPrompt, MB_ICONERROR|MB_OK);
			return;
		}
		
		mTotal += itor->GetItemSubtotal();
	}

	// webcall #13574: moved here to make sure disallow change of patron type when transaction is progress
	m_DoingCscOperation = TRUE;			// williamto 22Oct2013: FAT failure *******, do not allow clearing of transaction data when doing operation

	if ( PaymentMethod == PayCsc || PaymentMethod == PayMp || PaymentMethod == PayDp )
	{
		if (mItems.size() > 3 && PaymentMethod == PayCsc) {
			if (OcpMessageBoxEx(NULL, _R(IDS_OCP_CSC_GROUP_NO_MORE_THAN_3), (MB_ICONERROR | MB_OK), RGB_THISTLE, RGB_BLUE50P, 20) != IDYES)
			{
				m_DoingCscOperation = FALSE;
				gPid.Clear();
				gPid.ResetPatronLanguage();		// reset patron language as necessary
				return;
			}
		}
		else {
			while (!Csc.Refresh() )
			{
			mRecv = mTotal;
			
				// if pay by CSC and no CSC, prompt operator to present CSC or press Recv/Chng key to pay by cash
				if ( OcpMessageBox( OCPMSG_PLACECARD ) == IDCANCEL )
				{
					m_DoingCscOperation = FALSE;
					// Cancel pressed
					return;
				}
			}
		}
	}
	mChange = mRecv - mTotal;

	UpdateData(FALSE);
/*	williamto 19Dec2012: ignore recv/chng key
	else
	{
		if ( mRecv < mTotal )
		{
			ProcessError(CashPaymentNotReceived);
			m_DoingCscOperation = FALSE;
			return;
		}

	}*/
	
	if ( FareType != GroupPrePaid )
		OnTripIssueTicket(FALSE);
	else
		OnTripIssueTicket(TRUE);

	m_DoingCscOperation = FALSE;			// williamto 22Oct2013: FAT failure *******, do not allow clearing of transaction data when doing operation
}

BOOL CPostHyfProcessTrip::AddItem(int nItemID, BOOL isDeluxe, int nQuantity, COleCurrency nPrice)
{
	int nItemPos = -1;
	
	mSequence = COCPPosDialog::POS_EVENT_ENTER_ITEM; 
	
	CString strItem, qtyStr/*, tmpStr*/;
	CPosTicketItem tmp(nItemID, isDeluxe, nQuantity, nPrice);
	tmp.SetItemName(((FareType == ConcessionSale || FareType == ConcessionCheck || FareType == CRT_UPGRADE) ? _T("HRT ") : _T("")) + gEod.HyfPatronTypeStr(nItemID));
	//mItems.push_back(tmp);

	PRINT_DEBUG_TRACE(_T("\r\nnItemID=%d"), nItemID);

	// display the item ID
	qtyStr.Format(_T("%d"), nQuantity);
	strItem = tmp.GetItemName(); //gEod.HyfPatronTypeStr(nItemID);
	strItem += _T("\t");
	strItem += _T(" ") + gEod.HyfTravelClassStr(isDeluxe);
	strItem += _T("\t");
	strItem += _T(" ") + qtyStr;
	strItem += _T("\t");
	strItem += _T(" ") + tmp.GetItemSubtotal().Format(0, MAKELCID(MAKELANGID(LANG_CHINESE, SUBLANG_CHINESE_HONGKONG), SORT_DEFAULT));

/*	tmpStr.Format(_T("%s\t%s\t%d\t%s\t"), gEod.HyfPatronTypeStr(nItemID), gEod.HyfTravelClassStr(isDeluxe), 
		nQuantity, tmp.GetItemSubtotal().Format(0, MAKELCID(MAKELANGID(LANG_CHINESE, SUBLANG_CHINESE_HONGKONG), SORT_DEFAULT)));*/
	nItemPos = m_ctlList.InsertString(-1, strItem/*tmpStr*/);
	tmp.SetItemListPos( nItemPos );

	PRINT_DEBUG_TRACE( _T("\nAddItem(): %s"), strItem );
	PRINT_DEBUG_TRACE( _T("\ntmp.nListPos=%d"), tmp.GetItemListPos() );

	ASSERT(nItemID < MAX_HYF_TICKET_TYPE+1);
	mItems.push_back( tmp ); //mItems[nItemID] = tmp;	// williamto 05Dec2013: [FAT][POST][TF 4.28.14] group ticket now supports arbituary input of patron-travel type
	//mItems[nItemID].nListPos = tmp.nListPos;

	PRINT_DEBUG_TRACE( _T("\nmItems.back().nListPos=%d\n"), mItems.back().GetItemListPos() );

	CalcItemSubtotal();

	m_ctlList.SetSel( -1, FALSE );
	m_ctlList.SetSel( nItemPos );

	return TRUE;
}

// williamto 20121019: add an entire item to list
BOOL CPostHyfProcessTrip::AddItem(CPosTicketItem & item)
{
	int nItemPos = -1;
	
	mSequence = COCPPosDialog::POS_EVENT_ENTER_ITEM;

	CString strItem, qtyStr/*, tmpQty*/;
	//mItems.push_back(item);

	// display the item ID
	qtyStr.Format(_T("%d"), item.GetItemQty());
	strItem = item.GetItemName();
	strItem += _T("\t");
	strItem += _T(" ") + gEod.HyfTravelClassStr(item.GetDeluxeTicket());
	strItem += _T("\t");
	strItem += _T(" ") + qtyStr;
	strItem += _T("\t");
	strItem += _T(" ") + item.GetItemSubtotal().Format(0, MAKELCID(MAKELANGID(LANG_CHINESE, SUBLANG_CHINESE_HONGKONG), SORT_DEFAULT));

/*	tmpStr.Format(_T("%s\t%s\t%d\t%s\t"), item.GetItemName(), gEod.HyfTravelClassStr(item.GetDeluxeTicket()), 
		item.GetItemQty(), item.GetItemSubtotal().Format(0, MAKELCID(MAKELANGID(LANG_CHINESE, SUBLANG_CHINESE_HONGKONG), SORT_DEFAULT)));*/
	nItemPos = m_ctlList.InsertString(-1, /*tmpStr*/strItem);
	item.SetItemListPos( nItemPos );

	PRINT_DEBUG_TRACE( _T("\nAddItem(): %s"), strItem );
	PRINT_DEBUG_TRACE( _T("\nitem.nListPos=%d"), item.GetItemListPos() );
	
	ASSERT(item.GetItemId() < MAX_HYF_TICKET_TYPE+1);
	mItems.push_back( item ); //mItems[item.GetItemId()] = item;	// williamto 05Dec2013: [FAT][POST][TF 4.28.14] group ticket now supports arbituary input of patron-travel type
	//mItems[item.GetItemId()].nListPos = item.nListPos;

	PRINT_DEBUG_TRACE( _T("\nmItems.back()->nListPos=%d"), mItems.back().GetItemListPos() );

	CalcItemSubtotal();

	m_ctlList.SetSel( -1, FALSE );
	m_ctlList.SetSel( nItemPos );

	return TRUE;
}

BOOL CPostHyfProcessTrip::ModifyItem(int nListPos, BOOL isDeluxe, int nQuantity, COleCurrency nFare)
{
	CString strItem, qtyStr;

	int selIdx, selItems = m_ctlList.GetSelItems( 1, &selIdx );

	if ( /*selItems == 1 && selIdx < mItems.size()*/ TRUE )
	{
		// williamto 20Dec2012: "delete" the item with (item.nListPos == selIdx) by setting it back to 0 so it can be slow for long lists
		vector<CPosTicketItem>::iterator it;
		CString	empty = _T("");
		for ( it = mItems.begin(); it != mItems.end(); it++ )
		{
			CPosTicketItem & item = *it;
			
			PRINT_DEBUG_TRACE(_T("\nitem.nListPos=%d"), item.GetItemListPos());
			if ( item.GetItemListPos() == nListPos )
			{
				item.SetDeluxeTicket(isDeluxe);
				item.SetItemQty(nQuantity);
				item.SetItemPrice( nFare );

				strItem = item.GetItemName();
				strItem += _T("\t");
				strItem += _T(" ") + gEod.HyfTravelClassStr(item.GetDeluxeTicket());
				strItem += _T("\t");
				qtyStr.Format(_T("%d"), item.GetItemQty());
				strItem += _T(" ") + qtyStr;
				strItem += _T("\t");
				strItem += _T(" ") + item.GetItemSubtotal().Format(0, MAKELCID(MAKELANGID(LANG_CHINESE, SUBLANG_CHINESE_HONGKONG), SORT_DEFAULT));

				PRINT_DEBUG_TRACE( _T("\nModifyItem(): %s"), strItem );

				m_ctlList.DeleteString(nListPos);
				m_ctlList.InsertString(nListPos, strItem);
				m_ctlList.SetSel( -1, FALSE );
				m_ctlList.SetSel( nListPos );

				break;
			}
		}
	}
	CalcItemSubtotal();
	return TRUE;
}

void CPostHyfProcessTrip::OnClearSelection()
{
	CString strItem, qtyStr;
	mSequence = COCPPosDialog::POS_EVENT_ENTER_ITEM; 
	
	mEnteredValue = _T("");
	
	int selIdx, selItems = m_ctlList.GetSelItems( 1, &selIdx );

	if ( selItems == 1 && selIdx < (int)mItems.size() )
	{
		// williamto 20Dec2012: "delete" the item with (item.nListPos == selIdx) by setting it back to 0 so it can be slow for long lists
		vector<CPosTicketItem>::iterator it;
		vector<CPosTicketItem>::iterator itToErase;
		CString	empty = _T("");
		for ( it = mItems.begin(); it != mItems.end(); it++ )
		{
			CPosTicketItem & item = *it;
			
			PRINT_DEBUG_TRACE(_T("\nitem.nListPos=%d"), item.GetItemListPos());
			if ( item.GetItemListPos() == selIdx )
			{
			// williamto 05Dec2013: [FAT][POST][TF 4.28.14] group ticket now supports arbituary input of patron-travel type
				itToErase = it;  //mItems.erase( it );	// [webcall #14420] mark down the erase iterator entry instead of actually deleting now
				//break;
			}
// [webcall #14420] decrement the rest of the list to make sure data grid position is correct
			else if (item.GetItemListPos() > selIdx)
			{
				item.DecItemListPos();
			}
		}

// [webcall #14420] delete the entry here
		mItems.erase(itToErase);
		m_ctlList.DeleteString(selIdx);

		CalcItemSubtotal();
	} 
}

void CPostHyfProcessTrip::ClearPendingTransactionList()
{
	mEnteredValue = _T("");
	mTotal = 0;
	mRecv = 0;
	mChange = 0;

	UpdateData(FALSE);

	mItems.clear();
	//mItems.resize(MAX_HYF_TICKET_TYPE+1, CPosTicketItem());
	mItems.reserve( MAX_HYF_TICKET_TYPE+1 );	// williamto 05Dec2013: [FAT][POST][TF 4.28.14] group ticket now supports arbituary input of patron-travel type


	m_ctlList.ClearAll();

	EnablePatronEdits(0, FALSE, TRUE);
	if ((FareType == ConcessionSale) || (FareType == ConcessionCheck) || (FareType == CRT_UPGRADE))
	{
		for (int i=0; i<NUM_OF_HYF_PATRON_CLASS; i++)
		{
			EnablePatronCheckBox(i+1, (BOOL)this->bHrtEligiblePaxClass[i], FALSE);
		}
	}
	else
	{
		EnablePatronCheckBox(0, TRUE, TRUE);
	}
}

void CPostHyfProcessTrip::OnClearAll()
{
	if ( m_DoingCscOperation == FALSE )		// williamto 22Oct2013: FAT failure *******, do not allow clearing of transaction data when doing operation
	{
		mSequence = COCPPosDialog::POS_EVENT_BEGIN_TXN; 
	
		ClearPendingTransactionList();

		SetupControls(/*SingleJourney*/FareType);	// williamto 06Dec2013: [NWFF][POST] NWFF says keep the current fare mode when clearing the list

		UpdateData(FALSE);

		gPid.Clear();
		gPid.ResetPatronLanguage();		// reset patron language as necessary
	}
}

// williamto 20121112: jump into add value page instead
void CPostHyfProcessTrip::OnOctopusFn1(void)
{
	CAddValue dlg(this);

	dlg.DoModal();
}

void CPostHyfProcessTrip::OnPostButtonOp1()
{
	CHyfTrip
		HyfTrip;

	if (OcpVerifyAppTransactionState() && HyfTrip.IsInTrip())
	{
		CHyfTripInfo dlg(this);

		dlg.DoModal();
	}
}

void CPostHyfProcessTrip::OnPostButtonOp2()
{
	CHyfTrip 
		HyfTrip;
	
	if (OcpVerifyAppTransactionState() && HyfTrip.IsInTrip())
	{
		CFreightChargeTransaction FreightTrans;
		CHyfFreightPosDialog dlg(&FreightTrans, FALSE, this);	// williamto 07March2014: [OP][POST} freight ticket dependent on sailing
		dlg.m_bFromProcessSailing	= TRUE;
		dlg.m_pTicketProcess		= this;
		if ( dlg.DoModal() == IDCANCEL )
			EndDialog( IDCANCEL );
	}
}

void CPostHyfProcessTrip::CalcItemSubtotal(void)
{
	// calculate total amount, received amount and change amount
	mTotal = CCurrency(0,0);
	int i=0;
	for (i=0; i<(int)mItems.size(); i++)
	{
		mTotal += mItems[i].GetItemSubtotal();
	}
	
	mRecv = mTotal;		// williamto 17Jan2014: [OP][OCP][POST] don't have to calculate the change by defualt, only calculate when press "recv/chng" button
	
	UpdateData(FALSE);
}

void CPostHyfProcessTrip::PosIncrDecr(int nQty)
{
	int	nListPos, nItemsSel;
	CString strItem, qtyStr;

	CHyfFareCalculation FareCalc;
	CHyfTrip			HyfTrip;


	int fareCalcOk = CHyfFareCalculation::Ok;

	nItemsSel = m_ctlList.GetSelItems( 1, &nListPos );
	
	PRINT_DEBUG_TRACE(_T("\r\n%s: nListPos=%d"), _T(__FUNCTION__), nListPos);
	
	if ( nItemsSel == 1 ) 
	{
		vector<CPosTicketItem>::iterator it;
		CString	empty = _T("");
		for ( it = mItems.begin(); it != mItems.end(); it++ )
		{
			//CPosTicketItem & item = *it;
			
			PRINT_DEBUG_TRACE(_T("\r\n%s: item.nListPos=%d"), _T(__FUNCTION__), it->GetItemListPos());
			if ( it->GetItemListPos() == nListPos )
			{
				if ( it->GetItemQty() + nQty < 0 )
					it->SetItemQty(0);
				else				
				{
					if ( it->GetItemQty() == 0 && nQty > 0 )
					{
						// recalculate the fare, assuming the fares are the same
						if ( PaymentMethod == PayCash )
						{
							fareCalcOk = FareCalc.CalculateFare(it->GetItemId(), 
													HyfTrip.GetSailingTime(), 
													HyfTrip.GetOriginTerminal(), 
													HyfTrip.GetDestinationTerminal(), 
													HyfTrip.GetFerryType(), 
													it->GetDeluxeTicket());
						}
						else if ( PaymentMethod == PayCsc )
						{
							fareCalcOk = FareCalc.CalculateFare(HyfTrip.GetSailingTime(), 
													HyfTrip.GetOriginTerminal(), 
													HyfTrip.GetDestinationTerminal(), 
													HyfTrip.GetFerryType(), 
													HyfTrip.GetHolidayNum(),
													it->GetDeluxeTicket(), &Csc.GeneralInfo, &Csc.HyfInfo, &Csc.PersonalInfo);
						}
						else
						{
							fareCalcOk = CHyfFareCalculation::NoAction;
							OcpMessageBox(OCPMSG_INTERNALFAREERROR);
							return;
						}
						
						if ( fareCalcOk == CHyfFareCalculation::Ok )
						{
							if ( FareType != Complementary )
							{
								it->SetItemPrice( FareCalc.GetFare() );
							}
						}
					}

					it->SetItemQty(it->GetItemQty() + nQty);
				}
				// display the item ID
				qtyStr.Format(_T("%d"), it->GetItemQty());
				strItem = it->GetItemName();
				strItem += _T("\t");
				strItem += _T(" ") + gEod.HyfTravelClassStr(it->GetDeluxeTicket());
				strItem += _T("\t");
				strItem += _T(" ") + qtyStr;
				strItem += _T("\t");
				strItem += _T(" ") + it->GetItemSubtotal().Format(0, MAKELCID(MAKELANGID(LANG_CHINESE, SUBLANG_CHINESE_HONGKONG), SORT_DEFAULT));

				m_ctlList.DeleteString( nListPos );
				it->SetItemListPos(m_ctlList.InsertString( nListPos, strItem ));
				m_ctlList.SetSel(it->GetItemListPos());	// williamto 29Aug2013: FAT failure ********* reselect the same position so the list item won't jump around

				CalcItemSubtotal();
				
				UpdateData(FALSE);
				
				return;
			}
		}
	}		
}

void CPostHyfProcessTrip::OnPosIncr()
{
	PosIncrDecr(1);
}


void CPostHyfProcessTrip::OnPosDecr()
{
	PosIncrDecr(-1);
}

void CPostHyfProcessTrip::OnPosDigit(UINT nID)
{
	if (FareType != SingleJourney)	// only parse the numbers for non-SJT ticket sales, SJT sales quantity shall always be 1
	{
		COCPPosDialog::OnPosDigit(nID);
	}

	PRINT_DEBUG_TRACE(_T("\r\nmQty=%d"), mQty);

	ProcessItemAndQty();
}

BOOL CPostHyfProcessTrip::GetCrtFare(bool IsCash, BOOL Deluxe, ShortCutPatronType ScType, CCurrency & theFare)
{
	BOOL	ReturnCode = FALSE;

	PaymentMethod = (IsCash ?  PayCash : PayCsc);
	
	IsDeluxe = Deluxe;	// williamto 25Jan2013: deluxe class calculation from CHyfIssTicket class

	switch ( ScType )
	{
		case SC_ADULT:
			PatronIndex = 0;
			break;
		case SC_CHILD:
			PatronIndex = 1;
			break;
		case SC_STUDENT:
			PatronIndex = 3;
			break;
	}
	
	if ( (ReturnCode = CalculateCrtFare()) == TRUE )
		theFare = Fare;
	else
		ProcessError(CPostHyfProcessTrip::FareCalculationError);
	
	return ReturnCode;

}

BOOL CPostHyfProcessTrip::GetCrtUpgradeFare(bool IsCash, int OldFareClass, int NewFareClass, ShortCutPatronType ScType, CCurrency & theFare)
{
	BOOL	ReturnCode = FALSE;

	PaymentMethod = (IsCash ?  PayCash : PayCsc);

	switch ( ScType )
	{
		case SC_ADULT:
			PatronIndex = 0;
			break;
		case SC_CHILD:
			PatronIndex = 1;
			break;
		case SC_STUDENT:
			PatronIndex = 3;
			break;
	}

	CrtOrgFareClass = OldFareClass;
	CrtCurFareClass = NewFareClass;
	
	if ( (ReturnCode = CalculateCrtUpgrade( OldFareClass, NewFareClass )) == TRUE )
		theFare = Fare;
	else
		ProcessError(CPostHyfProcessTrip::FareCalculationError);
	
	return ReturnCode;

}

// return (aka delete) HRT registration numbers if the transaction did not proceed 
void CPostHyfProcessTrip::ReturnHrtRegNumbers(bool bDisplayPrompt)
{
	CWaitCursor wc;
	CDialog		HrtWaitMsg;		// display of "wait for HRT" dialog 
	HrtWaitMsg.Create(IDD_OCPDIALOG_PLEASEWAIT,NULL);
	
	if (bDisplayPrompt == true)
	{
		HrtWaitMsg.ShowWindow(SW_SHOW);
	}

	// return the assigned HRT registration numbers
	for (std::vector<vector<CRegistrationNumber>>::iterator it1 = this->vecHrtRegistrationNumberVector.begin(); it1 != this->vecHrtRegistrationNumberVector.end(); it1++)
	{
		for (std::vector<CRegistrationNumber>::iterator it2 = it1->begin(); it2 != it1->end(); it2++)
		{
			RegistrationNumberGenerator.ReleaseRegistrationNumber(it2->DevIDRegNumber());
			if (bDisplayPrompt == true)
			{
				HrtWaitMsg.SetDlgItemText(IDC_WAIT_MESSAGE, _R(IDS_OCP_HYFMSG_WAITRETURNHRTREG) );
				HrtWaitMsg.UpdateWindow();
			}
			::YieldToWindows();
		}
	}
	this->vecHrtRegistrationNumberVector.clear();
}

void CPostHyfProcessTrip::OnTestHrtRegCode(void)
{
	CRegistrationNumber Number;
	//CRegistrationNumberGenerator Generator(&gLogIO, gEod.ServiceProviderId(), gEod.HrtPostId());

	uint32_t	attempts = 0;
	FILE	*pOpenStream = NULL;
	TCHAR	 Filename[260] = {_T('\0')};
	::ExpandEnvironmentStrings(_T("%TEMP%\\HrtRegCode.csv"), Filename, 259);

	if ( _tfopen_s( &pOpenStream, Filename, _T("w, ccs=Unicode") ) != 0 )
	{
		OcpMessageBox( _T("Cannot open test file") );
		return;
	}
	
	int iNumDeletedRecords = gLogIO.DeleteExpiredHrtRegNumber();

	CStdioFile HrtRegCodes( pOpenStream );

	// we generate 100000 codes and make sure they are unique

	CString szNumber;
	szNumber.Format(_T("Iteration;attempt;regNum\n"));
	HrtRegCodes.WriteString(szNumber);
	for (int i=0; i<1000; i++)
	{
		if (RegistrationNumberGenerator.FindNextRegistrationNumber(Number, 10, &attempts) == false)
		{
			// failed to find the registration code, write to the file
			HrtRegCodes.WriteString(_T("cannot find non repeating reg code after 10 attempts\n"));
		}
		else
		{
			// after finding the HRT number commit it to the database
			//RegistrationNumberGenerator.CommitRegistrationNumber(Number);

			szNumber = _T("");
			szNumber.Format(_T("%d;%d;%015I64u\n"), i+1, attempts, Number.FullReg());
			PRINT_DEBUG_TRACE(szNumber);
			HrtRegCodes.WriteString(szNumber);

			if ((i % 1000) == 0)	// flush to disk every 1000 numbers
			{
				HrtRegCodes.Flush();
			}
		}
	}

	fclose(pOpenStream);
}

void CPostHyfProcessTrip::OnReprintReceipt()
{
	CString	text = _R( IDS_OCP_PROMPT_REPRINT_RECEIPT );

    CSupervisorSignOnDialog signOnDlg;		// williamto 10Dec2013: [OP][OCP][POST][issue #110], add supervisor sign on for reprint receipt
	//ShiftInfo shiftInfo;

	// reprint HRT receipt by reloading the last receipt information and then reprint
	CNonVolatile nv;
	nv.Read(&(this->mHrtReprintReceipts), CNonVolatile::POST_HRT_RECEIPT, CNonVolatile::READ_LOCK);

	//nv.Read(&shiftInfo, CNonVolatile::OCP_SHIFT_INFO, CNonVolatile::READ_LOCK);

	if (this->mHrtReprintReceipts.size() <= 0)
	{
		text = _R(IDS_OCP_NO_RECEIPT_REPRINT);
		OcpMessageBox( text, MB_OK );
		return;
	}

	// ask for supervisor CSC
	if (signOnDlg.DoModal() == IDOK)
	{
		if (OcpMessageBox( text, MB_YESNO|MB_ICONQUESTION ) == IDYES)
		{
			CPostHrtPrn		HrtPrn(gEod.HrtPrinterName(), TRUE);

			//for each (CPostHrtReceipt PostHrtReceipt in this->mHrtReprintReceipts)
			for (CPostHrtReceiptVector::iterator PostHrtReceipt = this->mHrtReprintReceipts.begin(); PostHrtReceipt != this->mHrtReprintReceipts.end(); ++PostHrtReceipt)
			{
				HrtPrn.PrintConcessionReturnReceipt((*PostHrtReceipt));

				if (PostHrtReceipt->m_bReturnTrip == FALSE)
				{
					// print the return trip voucher
					HrtPrn.PrintCrtReturnEntry(*(PostHrtReceipt));
				}

				// print the deluxe class voucher
				if ((PostHrtReceipt->m_IsDeluxe == TRUE) && (PostHrtReceipt->m_FerryType == FERRY_ORDINARY))
				{
					HrtPrn.PrintCrtDeluxeEntry(*(PostHrtReceipt));
				}

				CLogEvtHrtReceiptReprint HrtReceiptReprint(COcpEventDataShiftI::GetShift()->Shift.String(), 
						(*(PostHrtReceipt)));

				COcpEventDataOcpEventLogI::WriteLog(HrtReceiptReprint);
				
				if (this->hrtReprintRecordFile.is_open())
				{
					std::wstring hrtReceiptReprintRecord = L"";
					HrtReceiptReprint.FormatLogFileString(hrtReceiptReprintRecord);
					this->hrtReprintRecordFile << hrtReceiptReprintRecord << endl;
					this->hrtReprintRecordFile.flush();
				}
			}
		}
	}
}

void CPostHyfProcessTrip::OnTestHrtPrinter(void)
{
	CPostHrtPrn		HrtPrn(gEod.HrtPrinterName(), FALSE);

	HrtPrn.PrintTestTicket();
}


/* end of file */

