﻿Build started 2/7/2025 2:51:28 pm.
     1>Project "D:\GIT_CLONE\ocp_nwff_upgrade\ocplog\ocplog.vcxproj" on node 5 (build target(s)).
     1>InitializeBuildStatus:
         Creating ".\Release\ocplog.unsuccessfulbuild" because "AlwaysCreate" was specified.
       ClCompile:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I..\inc /I..\coms\inc /Zi /nologo /W3 /WX- /O2 /Ob1 /Oy- /D WIN32 /D NDEBUG /D _WINDOWS /D _AFXDLL /D _USE_32BIT_TIME_T /D _WIN32_WINNT=0x0501 /D UNICODE /D _UNICODE /GF /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Fo".\Release\\" /Fd".\Release\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt LogIO.cpp
         LogIO.cpp
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(255): warning C4005: 'MSG_UNCONF_TRANSACTION_CSC' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(666) : see previous definition of 'MSG_UNCONF_TRANSACTION_CSC'
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(264): warning C4005: 'MSG_UNCONF_AUTOPAY' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(674) : see previous definition of 'MSG_UNCONF_AUTOPAY'
     1>LogIO.cpp(1260): warning C4018: '<' : signed/unsigned mismatch
     1>LogIO.cpp(1263): warning C4018: '<' : signed/unsigned mismatch
       Lib:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\Lib.exe /OUT:".\Release\ocplog.lib" /NOLOGO .\Release\LogIO.obj
         .\Release\AFIFO.obj
         .\Release\afifo_ph.obj
         .\Release\OCPLOG.obj
         "..\sqlite-cipher\lib\sqlite3.lib"
     1>afifo_ph.obj : warning LNK4221: This object file does not define any previously undefined public symbols, so it will not be used by any link operation that consumes this library
         ocplog.vcxproj -> D:\GIT_CLONE\ocp_nwff_upgrade\ocplog\.\Release\ocplog.lib
       FinalizeBuildStatus:
         Deleting file ".\Release\ocplog.unsuccessfulbuild".
         Touching ".\Release\ocplog.lastbuildstate".
     1>Done Building Project "D:\GIT_CLONE\ocp_nwff_upgrade\ocplog\ocplog.vcxproj" (build target(s)).

Build succeeded.

Time Elapsed 00:00:03.10
