﻿Build started 2/7/2025 2:51:28 pm.
     1>Project "D:\GIT_CLONE\ocp_nwff_upgrade\eod\eod.vcxproj" on node 2 (build target(s)).
     1>InitializeBuildStatus:
         Creating ".\Release\eod.unsuccessfulbuild" because "AlwaysCreate" was specified.
       ClCompile:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I..\inc /Zi /nologo /W3 /WX- /O2 /Ob1 /Oy- /D _AFXDLL /D _USE_32BIT_TIME_T /D _WIN32_WINNT=0x0501 /D UNICODE /D _UNICODE /GF /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Fo".\Release\\" /Fd".\Release\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt Eodmanag.cpp eodstdmt.cpp eodurp.cpp
         Eodmanag.cpp
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(255): warning C4005: 'MSG_UNCONF_TRANSACTION_CSC' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(666) : see previous definition of 'MSG_UNCONF_TRANSACTION_CSC'
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(264): warning C4005: 'MSG_UNCONF_AUTOPAY' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(674) : see previous definition of 'MSG_UNCONF_AUTOPAY'
     1>Eodmanag.cpp(712): warning C4996: 'strcpy': This function or variable may be unsafe. Consider using strcpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\string.h(105) : see declaration of 'strcpy'
     1>Eodmanag.cpp(739): warning C4996: 'strcpy': This function or variable may be unsafe. Consider using strcpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\string.h(105) : see declaration of 'strcpy'
     1>Eodmanag.cpp(3319): warning C4482: nonstandard extension used: enum 'EodManStable_t' used in qualified name
     1>Eodmanag.cpp(3347): warning C4482: nonstandard extension used: enum 'EodManStable_t' used in qualified name
     1>Eodmanag.cpp(3371): warning C4482: nonstandard extension used: enum 'EodManStable_t' used in qualified name
     1>Eodmanag.cpp(5076): warning C4996: 'strcpy': This function or variable may be unsafe. Consider using strcpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\string.h(105) : see declaration of 'strcpy'
     1>Eodmanag.cpp(5328): warning C4244: '=' : conversion from 'unsigned long' to 'unsigned short', possible loss of data
     1>Eodmanag.cpp(5439): warning C4244: '=' : conversion from 'unsigned long' to 'unsigned short', possible loss of data
         eodstdmt.cpp
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(255): warning C4005: 'MSG_UNCONF_TRANSACTION_CSC' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(666) : see previous definition of 'MSG_UNCONF_TRANSACTION_CSC'
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(264): warning C4005: 'MSG_UNCONF_AUTOPAY' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(674) : see previous definition of 'MSG_UNCONF_AUTOPAY'
     1>eodstdmt.cpp(167): warning C4996: '_wsplitpath': This function or variable may be unsafe. Consider using _wsplitpath_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\wchar.h(948) : see declaration of '_wsplitpath'
     1>C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\utility(163): warning C4800: 'int' : forcing value to bool 'true' or 'false' (performance warning)
                 C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\utility(247) : see reference to function template instantiation 'std::_Pair_base<_Ty1,_Ty2>::_Pair_base<_Ty,int>(_Other1 &&,_Other2 &&)' being compiled
                 with
                 [
                     _Ty1=std::_Tree_iterator<std::_Tree_val<std::_Tmap_traits<long,StdmtEntry_t,std::less<long>,std::allocator<std::pair<const long,StdmtEntry_t>>,false>>>,
                     _Ty2=bool,
                     _Ty=std::_Tree_iterator<std::_Tree_val<std::_Tmap_traits<long,StdmtEntry_t,std::less<long>,std::allocator<std::pair<const long,StdmtEntry_t>>,false>>>,
                     _Other1=std::_Tree_iterator<std::_Tree_val<std::_Tmap_traits<long,StdmtEntry_t,std::less<long>,std::allocator<std::pair<const long,StdmtEntry_t>>,false>>>,
                     _Other2=int
                 ]
                 C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\xtree(966) : see reference to function template instantiation 'std::pair<_Ty1,_Ty2>::pair<std::_Tree_iterator<_Mytree>,int>(_Other1 &&,_Other2 &&)' being compiled
                 with
                 [
                     _Ty1=std::_Tree_iterator<std::_Tree_val<std::_Tmap_traits<long,StdmtEntry_t,std::less<long>,std::allocator<std::pair<const long,StdmtEntry_t>>,false>>>,
                     _Ty2=bool,
                     _Mytree=std::_Tree_val<std::_Tmap_traits<long,StdmtEntry_t,std::less<long>,std::allocator<std::pair<const long,StdmtEntry_t>>,false>>,
                     _Other1=std::_Tree_iterator<std::_Tree_val<std::_Tmap_traits<long,StdmtEntry_t,std::less<long>,std::allocator<std::pair<const long,StdmtEntry_t>>,false>>>,
                     _Other2=int
                 ]
                 C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\xtree(944) : while compiling class template member function 'std::pair<_Ty1,_Ty2> std::_Tree<_Traits>::_Linsert(std::_Tree_nod<_Traits>::_Node *,bool)'
                 with
                 [
                     _Ty1=std::_Tree_iterator<std::_Tree_val<std::_Tmap_traits<long,StdmtEntry_t,std::less<long>,std::allocator<std::pair<const long,StdmtEntry_t>>,false>>>,
                     _Ty2=bool,
                     _Traits=std::_Tmap_traits<long,StdmtEntry_t,std::less<long>,std::allocator<std::pair<const long,StdmtEntry_t>>,false>
                 ]
                 C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\map(81) : see reference to class template instantiation 'std::_Tree<_Traits>' being compiled
                 with
                 [
                     _Traits=std::_Tmap_traits<long,StdmtEntry_t,std::less<long>,std::allocator<std::pair<const long,StdmtEntry_t>>,false>
                 ]
                 d:\git_clone\ocp_nwff_upgrade\eod\eodpriv.h(1419) : see reference to class template instantiation 'std::map<_Kty,_Ty>' being compiled
                 with
                 [
                     _Kty=long,
                     _Ty=StdmtEntry_t
                 ]
     1>C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\utility(163): warning C4800: 'int' : forcing value to bool 'true' or 'false' (performance warning)
                 C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\utility(247) : see reference to function template instantiation 'std::_Pair_base<_Ty1,_Ty2>::_Pair_base<std::_Tree_iterator<_Mytree>&,_Ty>(_Other1,_Other2 &&)' being compiled
                 with
                 [
                     _Ty1=std::_Tree_iterator<std::_Tree_val<std::_Tmap_traits<long,StdmtEntry_t,std::less<long>,std::allocator<std::pair<const long,StdmtEntry_t>>,false>>>,
                     _Ty2=bool,
                     _Mytree=std::_Tree_val<std::_Tmap_traits<long,StdmtEntry_t,std::less<long>,std::allocator<std::pair<const long,StdmtEntry_t>>,false>>,
                     _Ty=int,
                     _Other1=std::_Tree_iterator<std::_Tree_val<std::_Tmap_traits<long,StdmtEntry_t,std::less<long>,std::allocator<std::pair<const long,StdmtEntry_t>>,false>>> &,
                     _Other2=int
                 ]
                 C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\xtree(988) : see reference to function template instantiation 'std::pair<_Ty1,_Ty2>::pair<std::_Tree_iterator<_Mytree>&,int>(_Other1,_Other2 &&)' being compiled
                 with
                 [
                     _Ty1=std::_Tree_iterator<std::_Tree_val<std::_Tmap_traits<long,StdmtEntry_t,std::less<long>,std::allocator<std::pair<const long,StdmtEntry_t>>,false>>>,
                     _Ty2=bool,
                     _Mytree=std::_Tree_val<std::_Tmap_traits<long,StdmtEntry_t,std::less<long>,std::allocator<std::pair<const long,StdmtEntry_t>>,false>>,
                     _Other1=std::_Tree_iterator<std::_Tree_val<std::_Tmap_traits<long,StdmtEntry_t,std::less<long>,std::allocator<std::pair<const long,StdmtEntry_t>>,false>>> &,
                     _Other2=int
                 ]
         eodurp.cpp
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(255): warning C4005: 'MSG_UNCONF_TRANSACTION_CSC' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(666) : see previous definition of 'MSG_UNCONF_TRANSACTION_CSC'
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(264): warning C4005: 'MSG_UNCONF_AUTOPAY' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(674) : see previous definition of 'MSG_UNCONF_AUTOPAY'
     1>eodurp.cpp(144): warning C4996: '_wsplitpath': This function or variable may be unsafe. Consider using _wsplitpath_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\wchar.h(948) : see declaration of '_wsplitpath'
     1>eodurp.cpp(257): warning C4129: '-' : unrecognized character escape sequence
     1>eodurp.cpp(300): warning C4996: '_wsplitpath': This function or variable may be unsafe. Consider using _wsplitpath_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\wchar.h(948) : see declaration of '_wsplitpath'
     1>eodurp.cpp(639): warning C4996: 'wcsncpy': This function or variable may be unsafe. Consider using wcsncpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\string.h(306) : see declaration of 'wcsncpy'
         Generating Code...
       Lib:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\Lib.exe /OUT:".\Release\eod.lib" /NOLOGO .\Release\CRC32.obj
         .\Release\eodblk.obj
         .\Release\Eodcfg.obj
         .\Release\Eodfare.obj
         .\Release\EODFILE.obj
         .\Release\eodimg.obj
         .\Release\Eodmanag.obj
         .\Release\Eodoper.obj
         .\Release\eodpriv.obj
         .\Release\EODRES.obj
         .\Release\Eodset.obj
         .\Release\eodstdmt.obj
         .\Release\eodurp.obj
     1>eodpriv.obj : warning LNK4221: This object file does not define any previously undefined public symbols, so it will not be used by any link operation that consumes this library
         eod.vcxproj -> D:\GIT_CLONE\ocp_nwff_upgrade\eod\.\Release\eod.lib
       FinalizeBuildStatus:
         Deleting file ".\Release\eod.unsuccessfulbuild".
         Touching ".\Release\eod.lastbuildstate".
     1>Done Building Project "D:\GIT_CLONE\ocp_nwff_upgrade\eod\eod.vcxproj" (build target(s)).

Build succeeded.

Time Elapsed 00:00:11.71
