﻿Build started 2/7/2025 2:51:28 pm.
     1>Project "D:\GIT_CLONE\ocp_nwff_upgrade\mobile\mobile.vcxproj" on node 6 (build target(s)).
     1>InitializeBuildStatus:
         Creating ".\Release\mobile.unsuccessfulbuild" because "AlwaysCreate" was specified.
       ClCompile:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I..\ocp /I..\inc /Zi /nologo /W3 /WX- /O2 /Ob1 /Oy- /D WIN32 /D NDEBUG /D _LIB /D _AFXDLL /D _USE_32BIT_TIME_T /D _WIN32_WINNT=0x0501 /D UNICODE /D _UNICODE /GF /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Fo".\Release\\" /Fd".\Release\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt MobileDialog.cpp MobileDiscount.cpp MobileEData.cpp
         MobileDialog.cpp
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(255): warning C4005: 'MSG_UNCONF_TRANSACTION_CSC' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(666) : see previous definition of 'MSG_UNCONF_TRANSACTION_CSC'
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(264): warning C4005: 'MSG_UNCONF_AUTOPAY' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(674) : see previous definition of 'MSG_UNCONF_AUTOPAY'
     1>d:\git_clone\ocp_nwff_upgrade\ocp\CSCard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         MobileDiscount.cpp
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(255): warning C4005: 'MSG_UNCONF_TRANSACTION_CSC' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(666) : see previous definition of 'MSG_UNCONF_TRANSACTION_CSC'
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(264): warning C4005: 'MSG_UNCONF_AUTOPAY' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(674) : see previous definition of 'MSG_UNCONF_AUTOPAY'
     1>d:\git_clone\ocp_nwff_upgrade\ocp\CSCard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
         MobileEData.cpp
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(255): warning C4005: 'MSG_UNCONF_TRANSACTION_CSC' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(666) : see previous definition of 'MSG_UNCONF_TRANSACTION_CSC'
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(264): warning C4005: 'MSG_UNCONF_AUTOPAY' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(674) : see previous definition of 'MSG_UNCONF_AUTOPAY'
     1>d:\git_clone\ocp_nwff_upgrade\ocp\CSCard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>MobileEData.cpp(35): warning C4244: 'argument' : conversion from '__int64' to 'unsigned long', possible loss of data
         Generating Code...
       Lib:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\Lib.exe /OUT:".\Release\mobile.lib" /NOLOGO .\Release\MobileAudit.obj
         .\Release\MobileDialog.obj
         .\Release\MobileDiscount.obj
         .\Release\MobileEData.obj
         .\Release\MobileElog.obj
         mobile.vcxproj -> D:\GIT_CLONE\ocp_nwff_upgrade\mobile\.\Release\mobile.lib
       FinalizeBuildStatus:
         Deleting file ".\Release\mobile.unsuccessfulbuild".
         Touching ".\Release\mobile.lastbuildstate".
     1>Done Building Project "D:\GIT_CLONE\ocp_nwff_upgrade\mobile\mobile.vcxproj" (build target(s)).

Build succeeded.

Time Elapsed 00:00:05.23
