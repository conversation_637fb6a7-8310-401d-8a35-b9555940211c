﻿Build started 2/7/2025 2:51:29 pm.
     1>Project "D:\GIT_CLONE\ocp_nwff_upgrade\OcpEData\OcpEData.vcxproj" on node 8 (build target(s)).
     1>InitializeBuildStatus:
         Creating ".\Release\OcpEData.unsuccessfulbuild" because "AlwaysCreate" was specified.
       ClCompile:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I..\inc /Zi /nologo /W3 /WX- /O2 /Ob1 /Oy- /D WIN32 /D NDEBUG /D _WINDOWS /D _AFXDLL /D _USE_32BIT_TIME_T /D _WIN32_WINNT=0x0501 /D UNICODE /D _UNICODE /GF /Gm- /EHsc /MD /GS /Gy /fp:precise /Zc:wchar_t /Zc:forScope /Fo".\Release\\" /Fd".\Release\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt OcpEData.cpp
         OcpEData.cpp
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(255): warning C4005: 'MSG_UNCONF_TRANSACTION_CSC' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(666) : see previous definition of 'MSG_UNCONF_TRANSACTION_CSC'
     1>D:\GIT_CLONE\ocp_nwff_upgrade\inc\toastype.h(264): warning C4005: 'MSG_UNCONF_AUTOPAY' : macro redefinition
                 D:\GIT_CLONE\ocp_nwff_upgrade\inc\udmf.h(674) : see previous definition of 'MSG_UNCONF_AUTOPAY'
     1>d:\git_clone\ocp_nwff_upgrade\ocp\CSCard.h(196): warning C4244: 'return' : conversion from '__int64' to 'long', possible loss of data
     1>OcpEData.cpp(184): warning C4356: 'COcpEventDataManager::AuditRegistersInterface' : static data member cannot be initialized via derived class
     1>OcpEData.cpp(185): warning C4356: 'COcpEventDataManager::ShiftInterface' : static data member cannot be initialized via derived class
     1>OcpEData.cpp(186): warning C4356: 'COcpEventDataManager::DailyInterface' : static data member cannot be initialized via derived class
     1>OcpEData.cpp(187): warning C4356: 'COcpEventDataManager::OcpLocalEventLog' : static data member cannot be initialized via derived class
     1>OcpEData.cpp(188): warning C4356: 'COcpEventDataManager::UdManagerInterface' : static data member cannot be initialized via derived class
       Lib:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\Lib.exe /OUT:".\Release\OcpEData.lib" /NOLOGO .\Release\OcpEData.obj
         OcpEData.vcxproj -> D:\GIT_CLONE\ocp_nwff_upgrade\OcpEData\.\Release\OcpEData.lib
       FinalizeBuildStatus:
         Deleting file ".\Release\OcpEData.unsuccessfulbuild".
         Touching ".\Release\OcpEData.lastbuildstate".
     1>Done Building Project "D:\GIT_CLONE\ocp_nwff_upgrade\OcpEData\OcpEData.vcxproj" (build target(s)).

Build succeeded.

Time Elapsed 00:00:03.95
