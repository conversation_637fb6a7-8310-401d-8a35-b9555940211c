/*
IS
--------------------------------------------------------------
Copyright (C) 1995, AES Prodata Holdings Ltd ACN ***********.

FILE :
	EODVO.h

SUMMARY :
	This file defines all the macros for the variable operation. This header
	shall be called by the System Monitor and Comms tasks.

NOTES :

REVISION HISTORY :

$Id$
$Log$
Revision 1.4  2002/12/23 06:05:28  johnd
ocp 6.4.8.1 import

Revision 1.1  1999/08/19 09:18:10  johnd
new source code

Revision 1.4  1998/02/09 07:26:18  JUDEW
SWIS 7 compliant files.
Revision 1.4  1998/02/05 02:37:04  MICHAELC
Updated after final SWIS 7 review.
In accordance with HK1-02-SWIS-0100-6-DCN-REV4

Revision 1.16  1998/02/04 16:24:48  JulianD
Renames the HYF POST Route ID varop to HYF Route ID as used on gates as well.

Revision 1.15  1997/07/08 12:30:40  <PERSON><PERSON><PERSON>gley
Added program download method varop for use on AVM devices.
Added POST quota control varops

Revision 1.14  1997/03/20 12:33:29  <PERSON>_Kirk
Version3 SWIS support.

Revision 1.12  1997/03/02 08:00     J. Dingley
Added VO_GATE_HYF_SAILING_TIME, FERRY_TYPE and CONCOURSE_ID

Revision 1.11  1996/08/24 08:00     J. Dingley
Added VO_DDU_BCP_MAINTENANCE which will eventually replace
VO_DDU_ADDRESS_RESET. All users should reference the new VO now.
Added VO_DEVICE_BRIGHTNESS to be used in place of the old MPR one.
Added VO_DEVICE_VOLUME to be used in place of the old MPR one.
Added VO_DEVICE_CONTRAST to be  used in place of the old MPR one.
Added VO_AVM_SIREN_RESET to be used in prefernce to the .._ENABLED one.
Added VO_AVM_STATUS to be used in preference to the OUT_OF_SERVICE one.
Removed VO_AVM_MAINTENANCE as not needed (use AVM_STATUS instead).
Removed VO_AVM_TYPE as now defined in EOD
Added VO_AUTHENTICATE_TIMEOUT for use by gates (currently)

*** The Varop values are defined within EODFMT.H ****
Revision 1.10  1995/10/16 16:30:25  liam
Added VO_GATE_STANDBY_MODE

Revision 1.9  1995/09/19 10:19:13  KCWD
added VO-DDU_BCP_DOWNLOADING
ADDED VO_DDU_ADDRESS_RESET

Revision 1.8  1995/08/08 22:34:57  Richard_Jack
checked in for integration

Revision 1.7  1995/07/12 22:11:12  Richard_Jack
removed VO_GATE_GOODWILL_DISCOUNT

Revision 1.6  1995/07/12 08:53:09  Richard_Jack
renamed a number of var ops and added VO_AUDIT_REGISTER_GENERATE_TIME

Revision 1.5  1995/07/05 20:55:52  Richard_Jack
added VO_AVM_OUT_OF_SERVICE
added VO_AVM_CALL_FOR_ASSISTANCE_ENABLED
added VO_AVM_STAFF_CSC_ID_NUM

Revision 1.4  1995/07/04 22:40:38  Richard_Jack
*** empty log message ***

Revision 1.3  1995/06/26 17:53:41  Richard_Jack
altered Name

Revision 1.2  1995/06/06 15:50:00  richardj
checked in for version control

Revision 1.1  1995/05/26 22:30:02  richardj
Initial revision

Revision 1.3  1995/05/09 22:52:40  richardj
Altered varop #defines and removed last section of file with #if 0

--------------------------------------------------------------
IE
*/


/*
TS
*/

/*
** The followings are used to register the Variable ID of the device status
** for the RPC VariableOp request
*/
#define VO_PROGRAM_DOWNLOAD             1
#define VO_REMOTE_DIAGNOSTIC            2
#define VO_ENQUIRY_TRANSACTIONS         3
#define VO_START_BUSINESS_DAY           4
#define VO_AUDIT_REGISTER_GENERATE_TIME 5
#define VO_AUTHENTICATE_TIMEOUT         6
#define VO_PROGRAM_DOWNLOAD_METHOD      7

#define VO_GATE_TYPE                    101
#define VO_GATE_DIRECTION               102
#define VO_GATE_OPERATION               103
#define VO_GATE_STATUS                  104
#define VO_GATE_FARE_MODE               105
#define VO_GATE_KCRC_SYSTEM_PARAM       106
#define VO_GATE_LW_QUOTA_ACTIVE         107
#define VO_GATE_HARDWARE_CONFIG         108
#define VO_GATE_STANDBY_MODE            109
#define VO_GATE_HYF_SAILING_TIME        110
#define VO_GATE_HYF_FERRY_TYPE          111
#define VO_GATE_HYF_CONCOURSE_ID        112
#define VO_GATE_HYF_QUOTA_REMAINING     113
#define VO_GATE_HYF_QUOTA_THRESHOLD     114
#define VO_GATE_HYF_QUOTA_ENABLED       115

#define VO_PCA_COMMS_STATUS             201

#define VO_AVM_CASH_COLLECT             302
#define VO_AVM_PIN_CASH_COLLECTION      304
#define VO_AVM_PIN_ENTRY_RETRIES        305
#define VO_AVM_PIN_ENTRY_TIMEOUT        306
#define VO_AVM_CASH_COLLECTION_EXPIRY   307

/* use of the following variable should reference the new one below as it is
   more representative */
#define VO_AVM_ALARM_SIREN_ENABLED      308
#define VO_AVM_ALARM_SIREN_RESET        308

/* use of the following variable should reference the new one below as it is
   more representative */
#define VO_AVM_OUT_OF_SERVICE           309
#define VO_AVM_STATUS                   309

#define VO_AVM_CALL_FOR_ASSISTANCE_ENABLED  310
#define VO_AVM_STAFF_CSC_ID_NUM         311

#define VO_MPR_MODE_CHANGE              401
#define VO_MPR_MODE_STATUS              402

/* The first 3 varop codes below should not be used now as the VO_DEVICE_..have
   been included to be more representative. The old codes will be removed when
   the next release of this file occurs */

#define VO_MPR_VOLUME                   403
#define VO_MPR_CONTRAST                 404
#define VO_MPR_BRIGHTNESS               405

#define VO_DEVICE_VOLUME                403
#define VO_DEVICE_CONTRAST              404
#define VO_DEVICE_BRIGHTNESS            405

#define VO_MPR_LOWU_AVAILABLE           406

#define VO_DDU_BCP_DOWNLOADING          501

/* VO_DDU_ADDRESS_RESET should not be used now as VO_DDU_BCP_MAINTENANCE has
   been  included as it is more representative. The old code will be removed
   when the next release of this file is made */

#define VO_DDU_ADDRESS_RESET            502
#define VO_DDU_BCP_MAINTENANCE          502

#define VO_POST_QUOTA_PRIMARY           550
#define VO_POST_QUOTA_RESERVED          551

#define VO_HYF_ROUTE_ID                 552
#define VO_POST_HYF_VESSEL_ID           553

#define VO_POST_STATUS                  554
#define VO_POST_TRIP_STATUS             555






/*
TE
*/

